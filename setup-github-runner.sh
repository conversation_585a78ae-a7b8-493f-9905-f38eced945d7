#!/bin/bash

# GitHub Actions Self-Hosted Runner Setup Script
# This script sets up a GitHub Actions runner on your EC2 instance

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
REPO_URL="https://github.com/kotilabs/smart-agent"
RUNNER_VERSION="2.311.0"
RUNNER_USER="ubuntu"
RUNNER_DIR="/opt/github-runner"

echo "🚀 Setting up GitHub Actions Self-Hosted Runner"
echo "================================================"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_error "This script should not be run as root. Please run as the $RUNNER_USER user."
    exit 1
fi

# Check if running as the correct user
if [ "$USER" != "$RUNNER_USER" ]; then
    print_error "This script should be run as the $RUNNER_USER user, not $USER"
    exit 1
fi

print_status "Setting up runner for repository: $REPO_URL"
print_status "Runner version: $RUNNER_VERSION"
print_status "Runner user: $RUNNER_USER"
print_status "Runner directory: $RUNNER_DIR"

# Create runner directory
print_status "Creating runner directory..."
sudo mkdir -p "$RUNNER_DIR"
sudo chown "$RUNNER_USER:$RUNNER_USER" "$RUNNER_DIR"
cd "$RUNNER_DIR"

# Download runner
print_status "Downloading GitHub Actions runner..."
RUNNER_ARCHIVE="actions-runner-linux-x64-$RUNNER_VERSION.tar.gz"
wget -O "$RUNNER_ARCHIVE" "https://github.com/actions/runner/releases/download/v$RUNNER_VERSION/$RUNNER_ARCHIVE"

# Verify download
if [ ! -f "$RUNNER_ARCHIVE" ]; then
    print_error "Failed to download runner archive"
    exit 1
fi

print_success "Runner downloaded successfully"

# Extract runner
print_status "Extracting runner..."
tar xzf "$RUNNER_ARCHIVE"
rm "$RUNNER_ARCHIVE"

# Set permissions
print_status "Setting runner permissions..."
chmod +x config.sh
chmod +x run.sh

print_success "Runner extracted and permissions set"

# Create systemd service file
print_status "Creating systemd service..."
sudo tee /etc/systemd/system/github-runner.service > /dev/null <<EOF
[Unit]
Description=GitHub Actions Runner
After=network.target

[Service]
Type=simple
User=$RUNNER_USER
WorkingDirectory=$RUNNER_DIR
ExecStart=$RUNNER_DIR/run.sh
Restart=always
RestartSec=10
Environment=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin

[Install]
WantedBy=multi-user.target
EOF

print_success "Systemd service created"

# Create runner configuration script
print_status "Creating runner configuration script..."
tee configure-runner.sh > /dev/null <<EOF
#!/bin/bash

echo "🔧 Configuring GitHub Actions Runner"
echo "===================================="
echo ""
echo "You need to configure the runner with a registration token from GitHub."
echo ""
echo "To get the token:"
echo "1. Go to: https://github.com/kotilabs/smart-agent/settings/actions/runners"
echo "2. Click 'New self-hosted runner'"
echo "3. Choose 'Linux' and 'x64'"
echo "4. Copy the registration token"
echo ""
echo "Then run this command with your token:"
echo "./config.sh --url $REPO_URL --token YOUR_TOKEN --labels self-hosted,linux,x64,ubuntu,ec2"
echo ""
echo "After configuration, you can start the service with:"
echo "sudo systemctl enable github-runner"
echo "sudo systemctl start github-runner"
echo ""
echo "To check status:"
echo "sudo systemctl status github-runner"
echo ""
echo "To view logs:"
echo "sudo journalctl -u github-runner -f"
EOF

chmod +x configure-runner.sh

print_success "Runner configuration script created"

# Create runner labels based on environment
print_status "Creating environment-specific runner labels..."

# Create staging runner labels
if [ -f "/opt/aagman/.env.staging" ]; then
    print_status "Detected staging environment"
    tee configure-staging-runner.sh > /dev/null <<EOF
#!/bin/bash

echo "🔧 Configuring Staging Runner"
echo "=============================="
echo ""
echo "Run this command with your staging runner token:"
echo "./config.sh --url $REPO_URL --token YOUR_STAGING_TOKEN --labels self-hosted,linux,x64,ubuntu,ec2,staging"
echo ""
echo "After configuration, start the service:"
echo "sudo systemctl enable github-runner"
echo "sudo systemctl start github-runner"
EOF
    chmod +x configure-staging-runner.sh
    print_success "Staging runner configuration script created"
fi

# Create production runner labels
if [ -f "/opt/aagman/.env.production" ]; then
    print_status "Detected production environment"
    tee configure-production-runner.sh > /dev/null <<EOF
#!/bin/bash

echo "🔧 Configuring Production Runner"
echo "================================"
echo ""
echo "Run this command with your production runner token:"
echo "./config.sh --url $REPO_URL --token YOUR_PRODUCTION_TOKEN --labels self-hosted,linux,x64,ubuntu,ec2,production"
echo ""
echo "After configuration, start the service:"
echo "sudo systemctl enable github-runner"
echo "sudo systemctl start github-runner"
EOF
    chmod +x configure-production-runner.sh
    print_success "Production runner configuration script created"
fi

# Create cleanup script
print_status "Creating cleanup script..."
tee cleanup-runner.sh > /dev/null <<EOF
#!/bin/bash

echo "🧹 Cleaning up GitHub Actions Runner"
echo "===================================="

# Stop and disable service
sudo systemctl stop github-runner || true
sudo systemctl disable github-runner || true

# Remove service file
sudo rm -f /etc/systemd/system/github-runner.service

# Reload systemd
sudo systemctl daemon-reload

# Remove runner directory
sudo rm -rf $RUNNER_DIR

echo "✅ Runner cleanup completed"
EOF

chmod +x cleanup-runner.sh

print_success "Cleanup script created"

# Create README
print_status "Creating README..."
tee README.md > /dev/null <<EOF
# GitHub Actions Self-Hosted Runner

This directory contains the GitHub Actions runner for your EC2 instance.

## Files

- \`config.sh\` - Runner configuration script
- \`run.sh\` - Runner execution script
- \`configure-runner.sh\` - Helper script for configuration
- \`configure-staging-runner.sh\` - Staging-specific configuration
- \`configure-production-runner.sh\` - Production-specific configuration
- \`cleanup-runner.sh\` - Cleanup script
- \`README.md\` - This file

## Setup Instructions

1. **Configure the runner:**
   \`\`\`bash
   ./configure-runner.sh
   \`\`\`

2. **Get registration token from GitHub:**
   - Go to: https://github.com/kotilabs/smart-agent/settings/actions/runners
   - Click 'New self-hosted runner'
   - Choose 'Linux' and 'x64'
   - Copy the registration token

3. **Run configuration:**
   \`\`\`bash
   # For staging
   ./config.sh --url $REPO_URL --token YOUR_STAGING_TOKEN --labels self-hosted,linux,x64,ubuntu,ec2,staging
   
   # For production
   ./config.sh --url $REPO_URL --token YOUR_PRODUCTION_TOKEN --labels self-hosted,linux,x64,ubuntu,ec2,production
   \`\`\`

4. **Start the service:**
   \`\`\`bash
   sudo systemctl enable github-runner
   sudo systemctl start github-runner
   \`\`\`

5. **Check status:**
   \`\`\`bash
   sudo systemctl status github-runner
   \`\`\`

## Security Notes

- The runner connects to GitHub (outbound only)
- No inbound SSH connections are needed
- The runner runs as the \`$RUNNER_USER\` user
- All runner files are owned by \`$RUNNER_USER\`

## Troubleshooting

- **View logs:** \`sudo journalctl -u github-runner -f\`
- **Restart service:** \`sudo systemctl restart github-runner\`
- **Check runner status:** \`./run.sh --help\`
- **Cleanup:** \`./cleanup-runner.sh\`
EOF

print_success "README created"

print_success "🎉 GitHub Actions runner setup completed!"
echo ""
echo "Next steps:"
echo "1. Run: ./configure-runner.sh"
echo "2. Get registration token from GitHub"
echo "3. Configure runner with token"
echo "4. Start the service"
echo ""
echo "For detailed instructions, see README.md"
echo ""
echo "Runner directory: $RUNNER_DIR"
echo "Service name: github-runner"
