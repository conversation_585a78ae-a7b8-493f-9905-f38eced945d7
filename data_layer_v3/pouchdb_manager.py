"""
PouchDB Manager for Data Layer V3.

Handles local PouchDB operations and synchronization.
"""

import json
import sqlite3
from pathlib import Path
from typing import Any, Dict, List, Optional

import structlog
from config import DataLayerConfig
from exceptions import PouchDBError

logger = structlog.get_logger(__name__)


class PouchDBManager:
    """Manages PouchDB local operations using SQLite as backend."""

    def __init__(self, config: DataLayerConfig):
        """Initialize PouchDB manager."""
        self.config = config
        self.db_path = Path(f"{config.pouchdb_name}.db")
        self.connection = None
        self._initialize_database()

    def _initialize_database(self):
        """Initialize the SQLite database for PouchDB."""
        try:
            self.connection = sqlite3.connect(str(self.db_path))
            self.connection.row_factory = sqlite3.Row

            # Create tables if they don't exist
            cursor = self.connection.cursor()

            # Documents table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS documents (
                    id TEXT PRIMARY KEY,
                    rev TEXT NOT NULL,
                    doc_data TEXT NOT NULL,
                    deleted INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Changes table for sync
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS changes (
                    seq INTEGER PRIMARY KEY AUTOINCREMENT,
                    doc_id TEXT NOT NULL,
                    rev TEXT NOT NULL,
                    deleted INTEGER DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            # Views table
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS views (
                    id TEXT PRIMARY KEY,
                    view_data TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """
            )

            self.connection.commit()
            logger.info("PouchDB database initialized", database=str(self.db_path))

        except Exception as e:
            logger.error("Failed to initialize PouchDB database", error=str(e))
            raise PouchDBError(f"Failed to initialize database: {e}")

    def get_document(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """Get a document from PouchDB."""
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                "SELECT doc_data FROM documents WHERE id = ? AND deleted = 0", (doc_id,)
            )
            row = cursor.fetchone()

            if row:
                return json.loads(row["doc_data"])
            return None

        except Exception as e:
            logger.error("Failed to get document", doc_id=doc_id, error=str(e))
            raise PouchDBError(f"Failed to get document {doc_id}: {e}")

    def save_document(self, doc_id: str, document: Dict[str, Any]) -> Dict[str, Any]:
        """Save a document to PouchDB."""
        try:
            # Generate new revision
            import uuid

            new_rev = f"1-{uuid.uuid4().hex}"

            # Check if document exists
            cursor = self.connection.cursor()
            cursor.execute("SELECT rev FROM documents WHERE id = ?", (doc_id,))
            existing = cursor.fetchone()

            if existing:
                # Update existing document
                cursor.execute(
                    """
                    UPDATE documents
                    SET rev = ?, doc_data = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """,
                    (new_rev, json.dumps(document), doc_id),
                )
            else:
                # Insert new document
                cursor.execute(
                    """
                    INSERT INTO documents (id, rev, doc_data)
                    VALUES (?, ?, ?)
                """,
                    (doc_id, new_rev, json.dumps(document)),
                )

            # Add to changes table
            cursor.execute(
                """
                INSERT INTO changes (doc_id, rev, deleted)
                VALUES (?, ?, 0)
            """,
                (doc_id, new_rev),
            )

            self.connection.commit()

            result = {"ok": True, "id": doc_id, "rev": new_rev}
            logger.info("Document saved successfully", doc_id=doc_id, rev=new_rev)
            return result

        except Exception as e:
            logger.error("Failed to save document", doc_id=doc_id, error=str(e))
            raise PouchDBError(f"Failed to save document {doc_id}: {e}")

    def delete_document(self, doc_id: str, rev: str) -> bool:
        """Delete a document from PouchDB."""
        try:
            cursor = self.connection.cursor()

            # Mark as deleted
            cursor.execute(
                """
                UPDATE documents
                SET deleted = 1, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """,
                (doc_id,),
            )

            # Add to changes table
            cursor.execute(
                """
                INSERT INTO changes (doc_id, rev, deleted)
                VALUES (?, ?, 1)
            """,
                (doc_id, rev),
            )

            self.connection.commit()
            logger.info("Document deleted successfully", doc_id=doc_id)
            return True

        except Exception as e:
            logger.error("Failed to delete document", doc_id=doc_id, error=str(e))
            raise PouchDBError(f"Failed to delete document {doc_id}: {e}")

    def find_documents(
        self, selector: Dict[str, Any], limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Find documents using simple selector matching."""
        try:
            cursor = self.connection.cursor()

            # Simple implementation - get all documents and filter
            cursor.execute(
                """
                SELECT doc_data FROM documents
                WHERE deleted = 0
            """
            )

            documents = []
            for row in cursor.fetchall():
                doc = json.loads(row["doc_data"])

                # Check if document matches selector
                matches = True
                for key, value in selector.items():
                    if key not in doc or doc[key] != value:
                        matches = False
                        break

                if matches:
                    documents.append(doc)
                    if limit and len(documents) >= limit:
                        break

            return documents

        except Exception as e:
            logger.error("Failed to find documents", selector=selector, error=str(e))
            raise PouchDBError(f"Failed to find documents: {e}")

    def get_all_documents(self, include_docs: bool = True) -> List[Dict[str, Any]]:
        """Get all documents from PouchDB."""
        try:
            cursor = self.connection.cursor()
            cursor.execute(
                """
                SELECT id, rev, doc_data FROM documents
                WHERE deleted = 0
            """
            )

            documents = []
            for row in cursor.fetchall():
                if include_docs:
                    doc = json.loads(row["doc_data"])
                    doc["_id"] = row["id"]
                    doc["_rev"] = row["rev"]
                    documents.append(doc)
                else:
                    documents.append(
                        {
                            "id": row["id"],
                            "key": row["id"],
                            "value": {"rev": row["rev"]},
                        }
                    )

            return documents

        except Exception as e:
            logger.error("Failed to get all documents", error=str(e))
            raise PouchDBError(f"Failed to get all documents: {e}")

    def get_changes(
        self, since: Optional[str] = None, limit: Optional[int] = None
    ) -> Dict[str, Any]:
        """Get changes feed."""
        try:
            cursor = self.connection.cursor()

            query = """
                SELECT seq, doc_id, rev, deleted, timestamp
                FROM changes
            """
            params = []

            if since:
                query += " WHERE seq > ?"
                params.append(int(since))

            query += " ORDER BY seq"

            if limit:
                query += " LIMIT ?"
                params.append(limit)

            cursor.execute(query, params)

            changes = []
            last_seq = 0

            for row in cursor.fetchall():
                change = {
                    "seq": row["seq"],
                    "id": row["doc_id"],
                    "changes": [{"rev": row["rev"]}],
                    "deleted": bool(row["deleted"]),
                }
                changes.append(change)
                last_seq = row["seq"]

            return {"results": changes, "last_seq": last_seq}

        except Exception as e:
            logger.error("Failed to get changes", error=str(e))
            raise PouchDBError(f"Failed to get changes: {e}")

    def create_view(
        self,
        design_doc_id: str,
        view_name: str,
        map_function: str,
        reduce_function: Optional[str] = None,
    ):
        """Create a view."""
        try:
            view_data = {"map": map_function, "reduce": reduce_function}

            view_id = f"{design_doc_id}/{view_name}"
            cursor = self.connection.cursor()

            cursor.execute(
                """
                INSERT OR REPLACE INTO views (id, view_data)
                VALUES (?, ?)
            """,
                (view_id, json.dumps(view_data)),
            )

            self.connection.commit()
            logger.info("View created", view_id=view_id)

        except Exception as e:
            logger.error("Failed to create view", error=str(e))
            raise PouchDBError(f"Failed to create view: {e}")

    def query_view(
        self, design_doc: str, view_name: str, **params
    ) -> List[Dict[str, Any]]:
        """Query a view (simplified implementation)."""
        try:
            # For now, return all documents as this is a simplified implementation
            return self.get_all_documents(include_docs=True)
        except Exception as e:
            logger.error("Failed to query view", error=str(e))
            raise PouchDBError(f"Failed to query view: {e}")

    def close(self):
        """Close the database connection."""
        if self.connection:
            self.connection.close()
            logger.info("PouchDB connection closed")
