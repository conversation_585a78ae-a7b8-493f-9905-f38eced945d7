"""
Configuration module for Data Layer V3.
"""

import logging
import os

from pydantic import BaseModel, Field, model_validator, validator

# Setup basic logging for config loading
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables from the appropriate file based on ENV mode
try:
    from dotenv import load_dotenv

    # First load base .env file if it exists
    base_env = os.path.join(os.path.dirname(__file__), ".env")
    if os.path.exists(base_env):
        load_dotenv(dotenv_path=base_env)

    # Then load .env.local which can override .env
    local_env = os.path.join(os.path.dirname(__file__), ".env.local")
    if os.path.exists(local_env):
        load_dotenv(dotenv_path=local_env, override=True)

    # Finally load environment specific file which takes highest precedence
    env_mode = os.getenv("ENV", "dev").lower()
    env_file_map = {
        "dev": ".env.development",
        "staging": ".env.staging",
        "prod": ".env.production",
    }
    env_file = os.path.join(os.path.dirname(__file__), env_file_map.get(env_mode))
    if os.path.exists(env_file):
        load_dotenv(dotenv_path=env_file, override=True)
    else:
        logger.warning(f"Environment specific file {env_file} not found")

except Exception as e:
    print(f"[config.py] Error loading environment files: {e}")
    logger.exception("[config.py] Error loading environment files")

from typing import Optional


class ConfigurationError(Exception):
    """Configuration error."""

    pass


class DataLayerConfig(BaseModel):
    """Configuration for Data Layer V3."""

    class Config:
        validate_assignment = True

    # CouchDB Configuration
    couchdb_host: str = Field(
        default_factory=lambda: os.getenv("COUCHDB_HOST", "localhost"),
        description="CouchDB host",
    )
    couchdb_port: int = Field(
        default_factory=lambda: int(os.getenv("COUCHDB_PORT", "5984")),
        description="CouchDB port",
    )
    couchdb_username: str = Field(
        default_factory=lambda: os.getenv("COUCHDB_USERNAME") or "",
        description="CouchDB username",
    )
    couchdb_password: str = Field(
        default_factory=lambda: os.getenv("COUCHDB_PASSWORD") or "",
        description="CouchDB password",
    )
    couchdb_database: str = Field(
        default_factory=lambda: os.getenv("COUCHDB_DATABASE", "aagmanai"),
        description="CouchDB database name",
    )
    couchdb_use_ssl: bool = Field(
        default_factory=lambda: os.getenv("COUCHDB_USE_SSL", "false").lower() == "true",
        description="Use SSL for CouchDB",
    )

    # PouchDB Configuration
    pouchdb_name: str = Field(
        default_factory=lambda: os.getenv("POUCHDB_NAME", "aagmanai_local"),
        description="PouchDB database name",
    )
    pouchdb_adapter: str = Field(
        default_factory=lambda: os.getenv("POUCHDB_ADAPTER", "idb"),
        description="PouchDB adapter (idb, leveldb, memory)",
    )

    # Connection Settings
    timeout: int = Field(
        default_factory=lambda: int(os.getenv("DB_TIMEOUT", "30")),
        description="Connection timeout in seconds",
    )
    max_retries: int = Field(
        default_factory=lambda: int(os.getenv("DB_MAX_RETRIES", "3")),
        description="Maximum retry attempts",
    )
    retry_delay: float = Field(
        default_factory=lambda: float(os.getenv("DB_RETRY_DELAY", "1.0")),
        description="Delay between retries in seconds",
    )
    pool_size: int = Field(
        default_factory=lambda: int(os.getenv("DB_POOL_SIZE", "10")),
        description="Connection pool size",
    )

    # Sync Settings
    sync_enabled: bool = Field(
        default_factory=lambda: os.getenv("SYNC_ENABLED", "true").lower() == "true",
        description="Enable CouchDB-PouchDB sync",
    )
    sync_interval: int = Field(
        default_factory=lambda: int(os.getenv("SYNC_INTERVAL", "5000")),
        description="Sync interval in milliseconds",
    )
    sync_live: bool = Field(
        default_factory=lambda: os.getenv("SYNC_LIVE", "true").lower() == "true",
        description="Enable live sync",
    )
    sync_retry: bool = Field(
        default_factory=lambda: os.getenv("SYNC_RETRY", "true").lower() == "true",
        description="Retry failed sync operations",
    )

    # Schema Settings
    schema_validation_enabled: bool = Field(
        default_factory=lambda: os.getenv("SCHEMA_VALIDATION_ENABLED", "true").lower()
        == "true",
        description="Enable schema validation",
    )
    auto_create_design_docs: bool = Field(
        default_factory=lambda: os.getenv("AUTO_CREATE_DESIGN_DOCS", "true").lower()
        == "true",
        description="Auto-create design documents",
    )

    @validator("couchdb_username")
    def validate_username(cls, v: str) -> str:
        """Validate username is provided."""
        if not v or not v.strip():
            raise ConfigurationError(
                "COUCHDB_USERNAME must be provided in environment variables"
            )
        return v.strip()

    @validator("couchdb_password")
    def validate_password(cls, v: str) -> str:
        """Validate password is provided."""
        if not v or not v.strip():
            raise ConfigurationError(
                "COUCHDB_PASSWORD must be provided in environment variables"
            )
        return v.strip()

    @validator("couchdb_database")
    def validate_database_name(cls, v: str) -> str:
        """Validate database name format."""
        if not v or not v.strip():
            raise ConfigurationError("Database name cannot be empty")

        # CouchDB database name restrictions
        if len(v) > 64:
            raise ConfigurationError("Database name cannot exceed 64 characters")

        # Check for invalid characters
        invalid_chars = ["/", "\\", "?", "%", "*", ":", "|", '"', "<", ">", ".", " "]
        for char in invalid_chars:
            if char in v:
                raise ConfigurationError(
                    f"Database name cannot contain '{char}' character"
                )

        return v.strip()

    @validator("couchdb_port")
    def validate_port(cls, v: int) -> int:
        """Validate port number."""
        if not (1 <= v <= 65535):
            raise ConfigurationError("Port must be between 1 and 65535")
        return v

    @validator("timeout", "max_retries", "pool_size")
    def validate_positive_integers(cls, v: int) -> int:
        """Validate that integer values are positive."""
        if v <= 0:
            raise ConfigurationError("Value must be positive")
        return v

    @validator("retry_delay")
    def validate_retry_delay(cls, v: float) -> float:
        """Validate retry delay."""
        if v < 0:
            raise ConfigurationError("Retry delay cannot be negative")
        return v

    @model_validator(mode="after")
    def validate_credentials(self):
        """Validate that CouchDB credentials are provided."""
        if not self.couchdb_username or not self.couchdb_username.strip():
            raise ConfigurationError(
                "COUCHDB_USERNAME must be provided in environment variables"
            )
        if not self.couchdb_password or not self.couchdb_password.strip():
            raise ConfigurationError(
                "COUCHDB_PASSWORD must be provided in environment variables"
            )

        return self

    @property
    def couchdb_url(self) -> str:
        """Get CouchDB URL."""
        protocol = "https" if self.couchdb_use_ssl else "http"
        return f"{protocol}://{self.couchdb_host}:{self.couchdb_port}"

    @property
    def database_url(self) -> str:
        """Get full database URL."""
        return f"{self.couchdb_url}/{self.couchdb_database}"
