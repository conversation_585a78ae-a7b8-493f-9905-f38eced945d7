#!/usr/bin/env python3
"""
Create monitoring table in aagmanai database.
Based on monitoring.csv schema with sample data generation.
"""

import json
import os
import random
import sys
import uuid
from datetime import datetime, timedelta
from decimal import Decimal

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import DataLayerConfig
from data_layer import DataLayer


def create_monitoring_table():
    """Create monitoring table with sample data."""

    print("=== Creating Monitoring Table ===")

    # Initialize Data Layer
    config = DataLayerConfig()
    data_layer = DataLayer(config)

    try:
        # Connect to databases
        print("Connecting to databases...")
        data_layer.connect()
        print("✓ Connected successfully")

        # Define monitoring schema (cleaned up from CSV - removed duplicates)
        monitoring_schema = {
            "type": "object",
            "properties": {
                "monitoring_id": {"type": "string"},
                "user_id": {"type": "string"},
                "account_id": {"type": "string"},
                "broker_id": {"type": "string"},
                "symbol": {"type": "string"},
                "broker_trade_id": {"type": "string"},
                "execution_time": {"type": "string"},
                "executed_quantity": {"type": "integer"},
                "executed_price": {"type": "number"},
                "transaction_type": {"type": "string"},
                "exchange": {"type": "string"},
                "brokerage_fee": {"type": "number"},
                "taxes_fees": {"type": "number"},
                "net_amount": {"type": "number"},
                "status": {"type": "string"},
                "exec_ref": {"type": "string"},
                "created_at": {"type": "string"},
                "order_type": {"type": "string"},
                "quantity": {"type": "integer"},
                "price": {"type": "number"},
                "trigger_price": {"type": "number"},
                "validity": {"type": "string"},
                "product_type": {"type": "string"},
                "desc": {"type": "string"},
                "stop_loss_price": {"type": "number"},
                "take_profit_price": {"type": "number"},
                "trailing_stop_percent": {"type": "number"},
            },
            "required": [
                "monitoring_id",
                "user_id",
                "account_id",
                "broker_id",
                "symbol",
                "broker_trade_id",
                "execution_time",
                "executed_quantity",
                "executed_price",
                "transaction_type",
                "exchange",
                "brokerage_fee",
                "taxes_fees",
                "net_amount",
                "status",
                "exec_ref",
                "created_at",
                "order_type",
                "quantity",
                "price",
                "trigger_price",
                "validity",
                "product_type",
                "desc",
                "stop_loss_price",
                "take_profit_price",
                "trailing_stop_percent",
            ],
        }

        # Define schema for monitoring table
        print("Defining monitoring schema...")
        data_layer.define_schema("monitoring", monitoring_schema, version=1)
        print("✓ Schema defined for monitoring table")

        # Generate sample monitoring data
        print("Generating sample monitoring data...")

        # Sample data arrays
        users = ["user-123", "user-456", "user-789", "user-101", "user-202"]
        accounts = ["acc-001", "acc-002", "acc-003", "acc-004", "acc-005"]
        brokers = ["broker-001", "broker-002", "broker-003", "broker-004", "broker-005"]
        symbols = [
            "RELIANCE",
            "TCS",
            "INFY",
            "HDFC",
            "ICICIBANK",
            "WIPRO",
            "HCLTECH",
            "TECHM",
        ]
        exchanges = ["NSE", "BSE"]
        transaction_types = ["BUY", "SELL"]
        order_types = ["MARKET", "LIMIT", "STOP_LIMIT", "STOP_LOSS"]
        statuses = ["filled", "partial", "cancelled"]
        validities = ["DAY", "IOC", "GTT"]
        product_types = ["CNC", "MIS", "NRML"]

        monitoring_records = []

        for i in range(30):  # Create 30 monitoring records
            # Generate monitoring data
            monitoring = {
                "monitoring_id": str(uuid.uuid4()),
                "user_id": users[i % len(users)],
                "account_id": accounts[i % len(accounts)],
                "broker_id": brokers[i % len(brokers)],
                "symbol": symbols[i % len(symbols)],
                "broker_trade_id": f"TRADE-{str(uuid.uuid4())[:8].upper()}",
                "execution_time": (
                    datetime.now() - timedelta(hours=random.randint(1, 168))
                ).isoformat(),
                "executed_quantity": random.randint(10, 1000),
                "executed_price": round(random.uniform(100.0, 5000.0), 2),
                "transaction_type": transaction_types[i % len(transaction_types)],
                "exchange": exchanges[i % len(exchanges)],
                "brokerage_fee": round(random.uniform(10.0, 100.0), 2),
                "taxes_fees": round(random.uniform(5.0, 50.0), 2),
                "net_amount": 0,  # Will be calculated
                "status": statuses[i % len(statuses)],
                "exec_ref": f"EXEC-{str(uuid.uuid4())[:8].upper()}",
                "created_at": datetime.now().isoformat(),
                "order_type": order_types[i % len(order_types)],
                "quantity": random.randint(10, 1000),
                "price": round(random.uniform(100.0, 5000.0), 2),
                "trigger_price": round(random.uniform(100.0, 5000.0), 2)
                if random.choice([True, False])
                else None,
                "validity": validities[i % len(validities)],
                "product_type": product_types[i % len(product_types)],
                "desc": f"Monitoring record for {symbols[i % len(symbols)]} - Sample monitoring {i+1}",
                "stop_loss_price": round(random.uniform(50.0, 4500.0), 2)
                if random.choice([True, False])
                else None,
                "take_profit_price": round(random.uniform(150.0, 5500.0), 2)
                if random.choice([True, False])
                else None,
                "trailing_stop_percent": round(random.uniform(1.0, 10.0), 2)
                if random.choice([True, False])
                else None,
            }

            # Calculate net amount
            gross_amount = (
                monitoring["executed_quantity"] * monitoring["executed_price"]
            )
            total_fees = monitoring["brokerage_fee"] + monitoring["taxes_fees"]
            if monitoring["transaction_type"] == "BUY":
                monitoring["net_amount"] = round(gross_amount + total_fees, 2)
            else:
                monitoring["net_amount"] = round(gross_amount - total_fees, 2)

            monitoring_records.append(monitoring)

        print(f"✓ Generated {len(monitoring_records)} sample monitoring records")

        # Save monitoring data to database
        print("Saving monitoring data to database...")

        saved_count = 0
        for i, monitoring in enumerate(monitoring_records):
            try:
                doc_id = data_layer.save_document(
                    "monitoring", monitoring, monitoring["monitoring_id"]
                )
                saved_count += 1

                if (i + 1) % 10 == 0:
                    print(
                        f"✓ Saved {saved_count}/{len(monitoring_records)} monitoring records"
                    )

            except Exception as e:
                print(f"❌ Failed to save monitoring record {i+1}: {e}")

        print(f"✓ Successfully saved {saved_count} monitoring records")

        # Verify saved monitoring data
        print("Verifying saved monitoring data...")

        # Query for monitoring records
        all_monitoring = data_layer.pouchdb_manager.find_documents(
            {"table_name": "monitoring"}
        )

        print(f"✓ Found {len(all_monitoring)} monitoring records in database")

        if all_monitoring:
            # Show sample monitoring record
            sample_monitoring = all_monitoring[0]
            print("\n📄 Sample Monitoring Record:")
            print(f"  - Monitoring ID: {sample_monitoring.get('monitoring_id')}")
            print(f"  - User ID: {sample_monitoring.get('user_id')}")
            print(f"  - Symbol: {sample_monitoring.get('symbol')}")
            print(f"  - Transaction Type: {sample_monitoring.get('transaction_type')}")
            print(
                f"  - Executed Quantity: {sample_monitoring.get('executed_quantity')}"
            )
            print(f"  - Executed Price: {sample_monitoring.get('executed_price')}")
            print(f"  - Net Amount: {sample_monitoring.get('net_amount')}")
            print(f"  - Status: {sample_monitoring.get('status')}")
            print(f"  - Exchange: {sample_monitoring.get('exchange')}")

        # Disconnect from databases
        data_layer.disconnect()
        print("✓ Disconnected from databases")

        return True

    except Exception as e:
        print(f"❌ Error creating monitoring table: {e}")
        try:
            data_layer.disconnect()
        except:
            pass
        return False


def show_monitoring_statistics():
    """Show statistics about the monitoring table."""
    print("\n==================================================")
    print("=== Monitoring Table Statistics ===")

    # Initialize Data Layer
    config = DataLayerConfig()
    data_layer = DataLayer(config)

    try:
        data_layer.connect()

        # Get all monitoring records
        all_monitoring = data_layer.pouchdb_manager.find_documents(
            {"table_name": "monitoring"}
        )

        print(f"Total monitoring records: {len(all_monitoring)}")

        if all_monitoring:
            # Analyze data
            buy_count = sum(
                1 for m in all_monitoring if m.get("transaction_type") == "BUY"
            )
            sell_count = sum(
                1 for m in all_monitoring if m.get("transaction_type") == "SELL"
            )

            unique_users = set(
                m.get("user_id") for m in all_monitoring if m.get("user_id")
            )
            unique_symbols = set(
                m.get("symbol") for m in all_monitoring if m.get("symbol")
            )
            unique_brokers = set(
                m.get("broker_id") for m in all_monitoring if m.get("broker_id")
            )

            # Status distribution
            status_counts = {}
            for m in all_monitoring:
                status = m.get("status")
                if status:
                    status_counts[status] = status_counts.get(status, 0) + 1

            print(f"Buy transactions: {buy_count}")
            print(f"Sell transactions: {sell_count}")
            print(f"Unique users: {len(unique_users)}")
            print(f"Unique symbols: {len(unique_symbols)}")
            print(f"Unique brokers: {len(unique_brokers)}")

            print("\nStatus distribution:")
            for status, count in status_counts.items():
                print(f"  {status}: {count}")

            # Calculate total net amount
            total_net_amount = sum(m.get("net_amount", 0) for m in all_monitoring)
            print(f"\nTotal net amount: ₹{total_net_amount:,.2f}")

        data_layer.disconnect()

    except Exception as e:
        print(f"❌ Error showing statistics: {e}")
        try:
            data_layer.disconnect()
        except:
            pass


if __name__ == "__main__":
    print("🚀 Creating Monitoring Table in aagmanai Database")
    print("=" * 60)

    success = create_monitoring_table()

    if success:
        show_monitoring_statistics()
        print("\n🎉 Monitoring table creation completed successfully!")
    else:
        print("\n❌ Monitoring table creation failed!")
