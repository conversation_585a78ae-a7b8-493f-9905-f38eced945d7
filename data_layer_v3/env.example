# Database Configuration
# Copy this file to .env and update with your actual credentials

# CouchDB Configuration
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_USERNAME=your_couchdb_username
COUCHDB_PASSWORD=your_couchdb_password
COUCHDB_DATABASE=aagmanai
COUCHDB_USE_SSL=false

# PouchDB Configuration
POUCHDB_NAME=aagmanai_local
POUCHDB_ADAPTER=idb

# PostgreSQL Configuration (for SQL queries)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=aagmanai
DB_USER=postgres
DB_PASSWORD=your_postgres_password

# Connection Settings
DB_TIMEOUT=30
DB_MAX_RETRIES=3
DB_RETRY_DELAY=1.0
DB_POOL_SIZE=10

# Sync Settings
SYNC_ENABLED=true
SYNC_INTERVAL=5000
SYNC_LIVE=true
SYNC_RETRY=true

# Schema Settings
SCHEMA_VALIDATION_ENABLED=true
AUTO_CREATE_DESIGN_DOCS=true
