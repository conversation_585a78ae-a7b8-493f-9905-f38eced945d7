# Data Layer V3

A hybrid data layer that combines CouchDB for remote storage with PouchDB for local database operations, providing seamless synchronization between the two.

> **⚠️ Important**: This project requires a Python virtual environment. Please follow the setup instructions below to create and activate a virtual environment before running any Python commands.

## Features

- **Dual Database Support**: CouchDB (remote) + PouchDB (local SQLite)
- **Automatic Synchronization**: Real-time sync between remote and local databases
- **Schema Validation**: JSON schema validation for data integrity
- **Offline Capability**: Local PouchDB allows offline operations
- **Conflict Resolution**: Built-in conflict detection and resolution
- **High Performance**: Local-first architecture for fast queries
- **Secure Credential Management**: All database credentials are managed via environment variables
- **Environment-based Configuration**: Support for multiple environments (dev, staging, prod)

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Application   │    │   Data Layer    │    │   CouchDB       │
│                 │◄──►│      V3         │◄──►│   (Remote)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   PouchDB       │
                       │   (Local)       │
                       │   SQLite        │
                       └─────────────────┘
```

## Quick Start

### Prerequisites

Before you begin, ensure you have `uv` installed. `uv` is a fast Python package installer and resolver.

#### Installing uv

**On macOS and Linux:**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**On Windows:**

```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Using pip:**

```bash
pip install uv
```

### Installation

1. **Create and activate a virtual environment:**

   ```bash
   # Create a virtual environment with uv
   uv venv --seed

   # Activate the virtual environment
   # On macOS/Linux:
   source .venv/bin/activate
   # On Windows:
   .venv\Scripts\activate
   ```

2. **Install dependencies and configure environment:**

   ```bash
   # Install dependencies first (required for setup.py)
   uv pip install -r requirements.txt

   # Complete setup (recommended) - installs dependencies and configures environment
   python setup.py

   # Or install dependencies and module separately
   uv pip install -r requirements.txt
   uv pip install -e .
   ```

### Environment Setup

The Data Layer V3 uses environment variables for configuration. You can set up your environment in several ways:

#### Option 1: Complete Setup (Recommended)

```bash
# Ensure you're in your virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies first (required for setup.py)
uv pip install -e .

# Run the complete setup script (installs dependencies + configures environment)
python setup.py
```

This will install all dependencies and prompt you for your database settings to create a `.env` file.

#### Option 2: Environment Setup Only

```bash
# Ensure you're in your virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies first (required for setup.py)
uv pip install -r requirements.txt

# Run only the environment setup
python setup.py --env
```

This will prompt you for your database settings and create a `.env` file.

#### Option 3: Manual Setup

1. **Ensure you're in your virtual environment:**

   ```bash
   source .venv/bin/activate  # On macOS/Linux
   # .venv\Scripts\activate   # On Windows
   ```

2. Copy the environment template:

   ```bash
   cp env.example .env
   ```

3. Edit the `.env` file with your database settings:

   ```bash
   # CouchDB Configuration
   COUCHDB_HOST=localhost
   COUCHDB_PORT=5984
   COUCHDB_USERNAME=your_couchdb_username
   COUCHDB_PASSWORD=your_couchdb_password
   COUCHDB_DATABASE=aagmanai
   COUCHDB_USE_SSL=false

   # PouchDB Configuration
   POUCHDB_NAME=aagmanai_local
   POUCHDB_ADAPTER=idb

   # PostgreSQL Configuration (for SQL queries)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=aagmanai
   DB_USER=postgres
   DB_PASSWORD=your_postgres_password

   # Connection Settings
   DB_TIMEOUT=30
   DB_MAX_RETRIES=3
   DB_RETRY_DELAY=1.0
   DB_POOL_SIZE=10

   # Sync Settings
   SYNC_ENABLED=true
   SYNC_INTERVAL=5000
   SYNC_LIVE=true
   SYNC_RETRY=true

   # Schema Settings
   SCHEMA_VALIDATION_ENABLED=true
   AUTO_CREATE_DESIGN_DOCS=true
   ```

   **Important**: Replace `your_couchdb_username` and `your_couchdb_password` with your actual CouchDB credentials. Never commit these values to version control.

#### Environment File Selection

The environment file loaded depends on the `ENV` variable:

| ENV Value | File Loaded |
| dev | .env.development |
| staging | .env.staging |
| prod | .env.production |

Set the `ENV` environment variable to select the mode (default is `dev`):

```bash
export ENV=dev   # or dev, staging, prod
```

#### Check Current Settings

```bash
# Check your current environment settings
python setup.py --check
```

#### Install Dependencies Only

```bash
# Ensure you're in your virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies (recommended method)
uv pip install -r requirements.txt

# Or install dependencies without environment setup
python setup.py --install
```

### Basic Usage

```python
# Ensure your virtual environment is activated
# source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate     # On Windows

# Make sure dependencies are installed
# uv pip install -r requirements.txt

from data_layer_v3 import DataLayer, DataLayerConfig

# Configuration - uses environment variables automatically
config = DataLayerConfig()

# Initialize and connect
data_layer = DataLayer(config)
data_layer.connect()

# Define schema
user_schema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "email": {"type": "string"},
        "age": {"type": "integer"}
    },
    "required": ["name", "email"]
}

data_layer.define_schema("users", user_schema)

# Save document
user_doc = {
    "name": "John Doe",
    "email": "<EMAIL>",
    "age": 30
}

doc_id = data_layer.save_document("users", user_doc)

# Retrieve document
retrieved_user = data_layer.get_document("users", doc_id)

# Find documents
active_users = data_layer.find_documents("users", {"active": True})

# Disconnect
data_layer.disconnect()
```

### Context Manager Usage

```python
with DataLayer(config) as data_layer:
    # Your operations here
    doc_id = data_layer.save_document("users", user_doc)
    user = data_layer.get_document("users", doc_id)
```

## Configuration

### DataLayerConfig Options

| Option                      | Type  | Default          | Description                            |
| --------------------------- | ----- | ---------------- | -------------------------------------- |
| `couchdb_host`              | str   | "localhost"      | CouchDB server host                    |
| `couchdb_port`              | int   | 5984             | CouchDB server port                    |
| `couchdb_username`          | str   | Required         | CouchDB username (from environment)    |
| `couchdb_password`          | str   | Required         | CouchDB password (from environment)    |
| `couchdb_database`          | str   | "aagmanai"       | CouchDB database name                  |
| `couchdb_use_ssl`           | bool  | false            | Use SSL for CouchDB connections        |
| `pouchdb_name`              | str   | "aagmanai_local" | Local PouchDB database name            |
| `pouchdb_adapter`           | str   | "idb"            | PouchDB adapter (idb, leveldb, memory) |
| `sync_enabled`              | bool  | true             | Enable synchronization                 |
| `sync_interval`             | int   | 5000             | Sync interval in milliseconds          |
| `sync_live`                 | bool  | true             | Enable live sync                       |
| `sync_retry`                | bool  | true             | Retry failed sync operations           |
| `timeout`                   | int   | 30               | Connection timeout in seconds          |
| `max_retries`               | int   | 3                | Maximum retry attempts                 |
| `retry_delay`               | float | 1.0              | Delay between retries in seconds       |
| `pool_size`                 | int   | 10               | Connection pool size                   |
| `schema_validation_enabled` | bool  | true             | Enable schema validation               |
| `auto_create_design_docs`   | bool  | true             | Auto-create design documents           |

## Database Operations

The module includes functions for direct database operations:

### Chat History Operations

#### `save_chat_message_sql()`

Save a chat message to the conversation history.

```python
from sql_queries import save_chat_message

chat_id = save_chat_message(
    cursor=None,  # For compatibility with existing interface
    user_id="user123",
    conversation_id="conv456",
    role="user",
    message="Hello, how are you?",
    llm_model_version="gpt-4",
    meta_json={"source": "web", "session_id": "sess789"},
    order_id="order123",
    type="message"
)
```

#### `extract_chat_history()`

Extract chat history for a specific user and conversation.

```python
from sql_queries import extract_chat_history

history = extract_chat_history("user123", "conv456")
for message in history:
    print(f"{message['role']}: {message['message']}")
```

#### `fetch_chat_history_after_timestamp_sql()`

Fetch chat messages after a specific timestamp.

```python
from sql_queries import fetch_chat_history_after_timestamp_sql
from datetime import datetime

timestamp = datetime(2024, 1, 1, 12, 0, 0)
messages = fetch_chat_history_after_timestamp_sql("user123", "conv456", timestamp)
```

### Summary Operations

#### `fetch_summary()`

Fetch summary records by user_id and conversation_id.

```python
from sql_queries import fetch_summary

summaries = fetch_summary(None, "user123", "conv456")
for summary in summaries:
    print(f"Summary: {summary['summary']}")
```

#### `fetch_summary_by_id_sql()`

Fetch a specific summary record by summary_id.

```python
from sql_queries import fetch_summary_by_id_sql

summary = fetch_summary_by_id_sql("summary-uuid-123")
if summary:
    summary_id, user_id, conv_id, timestamp, summary_text, llm_model, meta_json = summary
    print(f"Summary: {summary_text}")
```

#### `fetch_summaries_by_user_sql()`

Fetch all summary records for a specific user.

```python
from sql_queries import fetch_summaries_by_user_sql

user_summaries = fetch_summaries_by_user_sql("user123")
for summary in user_summaries:
    print(f"Conversation {summary[2]}: {summary[4][:100]}...")
```

#### `save_summary()`

Save a summary record.

```python
from sql_queries import save_summary

summary_id = save_summary(
    cursor=None,
    user_id="user123",
    conversation_id="conv456",
    summary="This conversation discussed trading strategies...",
    llm_model_version="gpt-4",
    meta_json={"tokens_used": 150, "model": "gpt-4"}
)
```

### Order Operations

#### `fetch_orders_by_user()`

Fetch orders for a specific user.

```python
from sql_queries import fetch_orders_by_user

orders = fetch_orders_by_user(None, "user123", broker_id="zerodha", status="EXECUTED")
for order in orders:
    print(f"Order {order['order_id']}: {order['symbol']} - {order['status']}")
```

### Monitoring Operations

#### `fetch_monitoring_by_user()`

Fetch monitoring records for a specific user.

```python
from sql_queries import fetch_monitoring_by_user
from datetime import datetime

time_from = datetime(2024, 1, 1, 12, 0, 0)
monitoring = fetch_monitoring_by_user(None, "user123", status="filled", time_from=time_from)
for record in monitoring:
    print(f"Execution: {record['symbol']} at {record['executed_price']}")
```

## Database Schema

The module supports four main data tables:

### Chat History (`chat_history.csv`)

- `chat_id`: Unique identifier for each chat message
- `user_id`: The user who sent/received the message
- `conversation_id`: The session this message belongs to
- `timestamp`: Timestamp when the message was sent
- `role`: Sender type ('user', 'agent', 'system', 'assistant')
- `message`: The content of the message
- `llm_model_version`: Version of the LLM model used
- `meta_json`: LLM prompt/response metadata
- `order_id`: Linked order ID if the chat initiated an order
- `type`: Type of conversation ('chat', 'order', 'monitoring')

### Orders (`orders.csv`)

- `order_id`: Unique identifier for the order
- `user_id`: The user who placed this order
- `account_id`: The trading account through which the order was placed
- `broker_id`: Unique identifier for the broker
- `symbol`: Trading symbol (e.g., 'RELIANCE')
- `order_type`: Type of order ('LIMIT', 'MARKET', 'STOP_LOSS')
- `transaction_type`: 'BUY' or 'SELL'
- `quantity`: Number of shares/units ordered
- `price`: Limit price for limit/stop orders
- `status`: Current status ('PENDING', 'OPEN', 'EXECUTED', 'CANCELLED', 'REJECTED', 'FAILED')
- And many more fields for comprehensive order tracking

### Monitoring (`monitoring.csv`)

- `monitoring_id`: Unique identifier for each execution
- `user_id`: The user associated with this execution
- `account_id`: The account through which the trade was executed
- `broker_id`: Broker identifier
- `symbol`: Trading symbol
- `execution_time`: Timestamp of the actual trade execution
- `executed_quantity`: Quantity of shares/units executed
- `executed_price`: Price at which this fill was executed
- `transaction_type`: 'BUY' or 'SELL'
- `exchange`: Exchange where the trade occurred
- And more fields for execution tracking

### Summary (`summary.csv`)

- `summary_id`: Unique identifier for each summary
- `user_id`: The user who owns this summary
- `conversation_id`: The session this summary belongs to
- `timestamp`: Timestamp when the summary was created
- `summary`: The summary content
- `llm_model_version`: Version of the LLM model used
- `meta_json`: LLM metadata

## API Reference

### Core Methods

#### `connect()`

Connect to both CouchDB and PouchDB databases.

#### `disconnect()`

Disconnect from databases and stop synchronization.

#### `define_schema(table_name, schema, version=1)`

Define a JSON schema for a table.

#### `save_document(table_name, document, doc_id=None)`

Save a document to both databases. Returns the document ID.

#### `get_document(table_name, doc_id)`

Retrieve a document (local-first, then remote).

#### `find_documents(table_name, selector, limit=None)`

Find documents using a selector (local-first, then remote).

#### `update_document(table_name, doc_id, document)`

Update an existing document.

#### `delete_document(table_name, doc_id)`

Delete a document from both databases.

### Synchronization

#### `manual_sync()`

Perform manual synchronization between databases.

#### `get_sync_status()`

Get synchronization status and statistics.

## Database Structure

### CouchDB (Remote)

- Full CouchDB API support
- Document-based storage
- RESTful interface
- Replication capabilities

### PouchDB (Local)

- SQLite backend for local storage
- Document-based storage
- Offline capability
- Fast local queries

## Error Handling

The module provides comprehensive error handling:

- `ConnectionError`: Database connection issues
- `DocumentNotFoundError`: Document not found
- `SchemaValidationError`: Schema validation failures
- `SyncError`: Synchronization issues
- `ConfigurationError`: Configuration problems

## Performance Considerations

- **Local-First**: Queries are performed locally first for speed
- **Lazy Loading**: Remote data is fetched only when needed
- **Background Sync**: Synchronization runs in background threads
- **Connection Pooling**: Efficient connection management

## Security

### Credential Management

- **Environment Variables**: All database credentials are stored in environment variables
- **No Hardcoded Credentials**: The codebase has been refactored to remove all hardcoded username/password values
- **Validation**: Required credentials are validated at startup
- **Template Files**: Use `env.example` as a starting point for your `.env` file

### Security Features

- Credential validation
- SSL/TLS support for CouchDB
- Local data encryption (SQLite)
- Secure authentication

### Testing Security Configuration

Run the security test script to verify your configuration:

```bash
python test_credentials.py
```

This script will:

- Verify that required environment variables are set
- Test that all modules read credentials from environment variables
- Confirm no hardcoded credentials are present
- Validate configuration across all components

## Migration from V2

Data Layer V3 is designed to be backward compatible with V2:

```python
# V2 usage
from data_layer_v2 import DataLayer, DataLayerConfig

# V3 usage (same interface)
from data_layer_v3 import DataLayer, DataLayerConfig
```

## Dependencies

The module requires the following Python packages:

- `pydantic>=2.0.0`: Data validation and settings management
- `requests>=2.28.0`: HTTP client for CouchDB
- `structlog>=23.0.0`: Structured logging
- `python-dotenv>=1.0.0`: Environment variable loading
- `couchdb>=1.2`: CouchDB client library
- `setuptools>=80.9.0`: Package installation

### Dependency Management with uv

This project uses `uv` for fast dependency management. The dependencies are defined in `pyproject.toml` and can be installed using:

```bash
# Install all dependencies
uv pip install -r requirements.txt

# Install specific dependencies
uv pip install pydantic requests structlog
```

### Alternative: Using pip

If you prefer to use pip instead of uv:

```bash
# Install from requirements.txt
pip install -r requirements.txt
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
