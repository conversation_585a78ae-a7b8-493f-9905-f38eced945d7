#!/usr/bin/env python3
"""
Script to check user data in CouchDB for debugging purposes.
This helps verify that user profiles and session mappings are correctly stored.
"""

import json
import os
import sys

# Add backend_api_module/src to Python path to import env_config
backend_src_path = os.path.join(
    os.path.dirname(__file__), "..", "backend_api_module", "src"
)
sys.path.insert(0, backend_src_path)

# Import and setup environment from backend
from env_config import setup_real_db_environment

setup_real_db_environment()

from sql_queries import get_couchdb_connection


def check_user_data(firebase_uid: str):
    """
    Check user profile and session mapping data for a Firebase UID.

    Args:
        firebase_uid: Firebase user UID to check data for
    """
    try:
        db = get_couchdb_connection()

        # Document IDs to check
        user_profile_id = f"user_profile_{firebase_uid}"
        user_session_id = f"user_session_{firebase_uid}"

        print(f"🔍 Checking user data for Firebase UID: {firebase_uid}")
        print("-" * 60)

        # Check user profile document
        if user_profile_id in db:
            profile_doc = db[user_profile_id]
            print(f"✅ User Profile Found: {user_profile_id}")
            print(f"   📝 Name: {profile_doc.get('name', 'NOT SET')}")
            print(f"   📱 Phone: {profile_doc.get('phone', 'NOT SET')}")
            print(f"   🆔 Firebase UID: {profile_doc.get('firebase_uid', 'NOT SET')}")
            print(f"   📅 Created: {profile_doc.get('created_at', 'NOT SET')}")
            print(f"   🔄 Updated: {profile_doc.get('updated_at', 'NOT SET')}")
        else:
            print(f"❌ User Profile NOT Found: {user_profile_id}")

        print()

        # Check user session mapping
        if user_session_id in db:
            session_doc = db[user_session_id]
            print(f"✅ Session Mapping Found: {user_session_id}")
            print(f"   🔗 Firebase UID: {session_doc.get('firebase_uid', 'NOT SET')}")
            print(
                f"   🆔 Session User ID: {session_doc.get('session_user_id', 'NOT SET')}"
            )
            print(f"   📅 Created: {session_doc.get('created_at', 'NOT SET')}")
        else:
            print(f"❌ Session Mapping NOT Found: {user_session_id}")

    except Exception as e:
        print(f"❌ Error checking user data: {e}")
        raise


def list_recent_documents():
    """List recent documents to see what's been created."""
    try:
        db = get_couchdb_connection()

        print(f"\n📋 Recent Documents:")
        print("-" * 30)

        # Get all document IDs
        all_docs = db.view("_all_docs", include_docs=False)

        user_profile_docs = []
        user_session_docs = []

        for row in all_docs:
            doc_id = row.id
            if doc_id.startswith("user_profile_"):
                user_profile_docs.append(doc_id)
            elif doc_id.startswith("user_session_"):
                user_session_docs.append(doc_id)

        print(f"👤 User Profiles ({len(user_profile_docs)}):")
        for doc_id in user_profile_docs[-5:]:  # Show last 5
            doc = db[doc_id]
            print(f"   • {doc_id}")
            print(f"     Name: {doc.get('name', 'N/A')}")
            print(f"     Phone: {doc.get('phone', 'N/A')}")

        print(f"\n🔗 Session Mappings ({len(user_session_docs)}):")
        for doc_id in user_session_docs[-5:]:  # Show last 5
            doc = db[doc_id]
            print(f"   • {doc_id}")
            print(f"     Firebase UID: {doc.get('firebase_uid', 'N/A')}")
            print(f"     Session User ID: {doc.get('session_user_id', 'N/A')}")

    except Exception as e:
        print(f"❌ Error listing documents: {e}")


def main():
    """Main function to check user data."""
    # Default Firebase UID from the logs
    default_firebase_uid = "2b0tSFkvEZZUJ2DZPJWgV4OVcAi1"

    if len(sys.argv) > 1:
        firebase_uid = sys.argv[1]
    else:
        firebase_uid = default_firebase_uid
        print(f"Using default Firebase UID from logs: {firebase_uid}")

    check_user_data(firebase_uid)
    list_recent_documents()


if __name__ == "__main__":
    main()
