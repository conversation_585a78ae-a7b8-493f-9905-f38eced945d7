"""
Custom exceptions for Data Layer V3.
"""


class DataLayerError(Exception):
    """Base exception for Data Layer V3."""

    pass


class ConnectionError(DataLayerError):
    """Raised when database connection fails."""

    pass


class DocumentNotFoundError(DataLayerError):
    """Raised when a document is not found."""

    pass


class SchemaValidationError(DataLayerError):
    """Raised when document validation fails."""

    pass


class ConfigurationError(DataLayerError):
    """Raised when configuration is invalid."""

    pass


class SyncError(DataLayerError):
    """Raised when synchronization fails."""

    pass


class PouchDBError(DataLayerError):
    """Raised when PouchDB operations fail."""

    pass


class CouchDBError(DataLayerError):
    """Raised when CouchDB operations fail."""

    pass
