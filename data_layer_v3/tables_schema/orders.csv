Column Name,Data Type,Description,Keys/Indexes,SOT
order_id,UUID/BIGINT,Unique identifier for the order in our system.,P<PERSON>,"Cloud DB (Primary), Broker API (for current status reconciliation)"
user_id,UUID/BIGINT,The user who placed this order.,"FK (Users), IX",
account_id,UUID/BIGINT,The trading account through which the order was placed.,"FK (Accounts), IX",
broker_id,UUID/BIGINT,Unique identifier for the broker through which the order was placed.,"FK (Brokers), IX",
instrument_id,UUID/BIGINT,The instrument being traded.,"FK (Instruments), IX",
symbol,VARCHAR(50),"Trading symbol (e.g., 'RELIANCE').",IX,
broker_order_id,VARCHAR(255),The order ID assigned by the broker (if available).,,
order_type,VARCHAR(50),"Type of order (e.g., 'LIMIT', 'MARKET', 'STOP_LOSS').",,
transaction_type,VARCHAR(10),BUY' or 'SELL'.,,
quantity,INTEGER,Number of shares/units ordered.,,
price,"DECIMAL(18,4)",Limit price for limit/stop orders.,,
trigger_price,"DECIMAL(18,4)",Trigger price for stop-loss orders (if applicable).,,
status,VARCHAR(50),"Current status: 'PENDING', 'OPEN', 'EXECUTED', 'CANCELLED', 'REJECTED', 'FAILED'.",IX,
validity,VARCHAR(20),"Order validity (e.g., 'DAY', 'IOC', 'GTT').",,
product_type,VARCHAR(20),"Product type (e.g., 'CNC', 'MIS', 'NRML').",,
created_at,TIMESTAMP WITH TIME ZONE,Timestamp when the order was placed in our system.,IX,
updated_at,TIMESTAMP WITH TIME ZONE,Last update timestamp for the order status.,,
completed_at,TIMESTAMP WITH TIME ZONE,Timestamp when the order reached a final state (executed/cancelled/rejected).,,
parent_order_id,UUID/BIGINT,Parent order reference (for OCO/Bracket orders).,FK (Orders),
comments,VARCHAR(255),Manual comments or LLM command context.,,
submitted_by,VARCHAR(64),"Originator of the order (e.g., 'user', 'llm', 'auto').",,
source,VARCHAR(16),"Source of the order (e.g., 'extension', 'web', 'api').",,
llm_intent_id,UUID/BIGINT,Link to the chat message that initiated the order.,FK (Chat_History),
strategy_id,UUID/BIGINT,Link to trading strategy that generated this order,FK (Trading_Strategies),Cloud DB
is_automated,BOOLEAN,Flag indicating if order was generated by autopilot,IX,Cloud DB
risk_score,"DECIMAL(5,2)",Risk assessment score for this order,,Cloud DB
stop_loss_price,"DECIMAL(18,4)",Auto stop-loss price,,Cloud DB
take_profit_price,"DECIMAL(18,4)",Auto take-profit price,,Cloud DB
trailing_stop_percent,"DECIMAL(5,2)",Trailing stop percentage,,Cloud DB
portfolio_id,UUID/BIGINT,Portfolio this order belongs to,FK (Portfolios),Cloud DB
goal_id,UUID/BIGINT,Investment goal this order contributes to,FK (Investment_Goals),Cloud DB
,,,,
,,,,
,,,,
Index Name,Columns,Purpose,,
idx_orders_user_status_created,"(user_id, status, created_at)",Fast retrieval of user orders by status,,
idx_orders_account_symbol_created,"(account_id, symbol, created_at)",Account-specific symbol orders,,
idx_orders_status_updated,"(status, updated_at)",Pending orders cleanup,,
idx_orders_automated_created,"(is_automated, created_at)",Autopilot order tracking,,
