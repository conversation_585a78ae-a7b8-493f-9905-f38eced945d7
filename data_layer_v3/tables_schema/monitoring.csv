Column Name,Data Type,Description,Keys/Indexes,SOT
user_id,UUID/BIGINT,The user associated with this execution.,"FK (Users), IX",
monitoring_id,UUID/BIGINT,unique identifier for each execution.,,
account_id,UUID/BIGINT,The account through which the trade was executed.,"FK (Accounts), IX",
broker_id,UUID/BIGINT,to identify the broker,,
symbol,VARCHAR(50),Trading symbol.,,
broker_trade_id,VARCHAR(255),The trade ID assigned by the broker.,,
execution_time,TIMESTAMP WITH TIME ZONE,Timestamp of the actual trade execution.,IX,
executed_quantity,INTEGER,Quantity of shares/units executed in this fill.,,
executed_price,"DECIMAL(18,4)",Price at which this fill was executed.,,
transaction_type,VARCHAR(10),BUY' or 'SELL'.,,
exchange,VARCHAR(32),"Exchange where the trade occurred (e.g., 'NSE', 'BSE').",,
brokerage_fee,"DECIMAL(10,4)",Brokerage charged for this execution.,,
taxes_fees,"DECIMAL(10,4)","Other taxes and fees (e.g., STT, transaction charges, GST).",,
net_amount,"DECIMAL(18,4)",Net amount (executed_quantity * executed_price +/- fees).,,
status,VARCHAR(16),"Status of the execution (e.g., 'filled', 'partial', 'cancelled').",,
exec_ref,VARCHAR(64),Broker execution reference ID.,,
created_at,TIMESTAMP WITH TIME ZONE,Timestamp when this execution record was logged in our system.,,
order_type,VARCHAR(50),"Type of order (e.g., 'LIMIT', 'MARKET', 'STOP_LOSS').",,
transaction_type,VARCHAR(10),BUY' or 'SELL'.,,
quantity,INTEGER,Number of shares/units ordered.,,
price,"DECIMAL(18,4)",Limit price for limit/stop orders.,,
trigger_price,"DECIMAL(18,4)",Trigger price for stop-loss orders (if applicable).,,
status,VARCHAR(50),"Current status: 'PENDING', 'OPEN', 'EXECUTED', 'CANCELLED', 'REJECTED', 'FAILED'.",IX,
validity,VARCHAR(20),"Order validity (e.g., 'DAY', 'IOC', 'GTT').",,
product_type,VARCHAR(20),"Product type (e.g., 'CNC', 'MIS', 'NRML').",,
desc,TEXT,description of the order,,
stop_loss_price,"DECIMAL(18,4)",Auto stop-loss price,,Cloud DB
take_profit_price,"DECIMAL(18,4)",Auto take-profit price,,Cloud DB
trailing_stop_percent,"DECIMAL(5,2)",Trailing stop percentage,,Cloud DB
