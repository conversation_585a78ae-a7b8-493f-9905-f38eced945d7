Column Name,Data Type,Description,Keys/Indexes,SOT
chat_id,UUID/BIGINT,Unique identifier for each chat message.,<PERSON><PERSON>,Cloud DB
user_id,UUID/BIGINT,The user who sent/received the message.,"FK (Users), IX",
conversation_id,UUID/BIGINT,The session this message belongs to.,"FK (Sessions), IX",
timestamp,TIMESTAMP WITH TIME ZONE,Timestamp when the message was sent.,IX,
role,VARCHAR(16),"Sender type: 'user', 'agent', 'system', 'assistant'.",,
message,TEXT,The content of the message.,,
llm_model_version,VARCHAR(50),Version of the LLM model used for the response.,,
meta_json,JSONB,"LLM prompt/response metadata, actions suggested, token usage.",,
order_id,UUID/BIGINT,(Optional) Linked order ID if the chat initiated an order.,FK (Orders),
type,VARCHAR(16),"Type of conversation 'chat', 'order', 'monitoring'",,
