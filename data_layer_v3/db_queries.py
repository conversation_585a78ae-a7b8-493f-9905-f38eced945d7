"""
Generalized Database Queries Module

Provides a comprehensive set of database operations for the aagmanai database
including fetch, save, update, and delete operations for various entities.
"""

import logging
import os
from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from .config import DataLayerConfig
from .data_layer import DataLayer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseQueries:
    """Main class for database operations."""

    def __init__(
        self,
        couchdb_host: str = None,
        couchdb_port: int = None,
        couchdb_username: str = None,
        couchdb_password: str = None,
        couchdb_database: str = None,
    ):
        """
        Initialize database queries with connection parameters.

        Args:
            couchdb_host: CouchDB host
            couchdb_port: CouchDB port
            couchdb_username: CouchDB username
            couchdb_password: CouchDB password
            couchdb_database: CouchDB database name
        """
        # Use environment variables if not provided, with validation
        self.couchdb_host = couchdb_host or os.getenv("COUCHDB_HOST")
        if not self.couchdb_host:
            raise ValueError(
                "COUCHDB_HOST must be provided or set in environment variables"
            )

        self.couchdb_port = couchdb_port or int(os.getenv("COUCHDB_PORT", "5984"))

        self.couchdb_username = couchdb_username or os.getenv("COUCHDB_USERNAME")
        if not self.couchdb_username:
            raise ValueError(
                "COUCHDB_USERNAME must be provided or set in environment variables"
            )

        self.couchdb_password = couchdb_password or os.getenv("COUCHDB_PASSWORD")
        if not self.couchdb_password:
            raise ValueError(
                "COUCHDB_PASSWORD must be provided or set in environment variables"
            )

        self.couchdb_database = couchdb_database or os.getenv(
            "COUCHDB_DATABASE", "aagmanai"
        )
        self.data_layer = None

    def _get_data_layer(self) -> DataLayer:
        """Get or create data layer instance."""
        if self.data_layer is None or not self.data_layer.connected:
            config = DataLayerConfig(
                couchdb_host=self.couchdb_host,
                couchdb_port=self.couchdb_port,
                couchdb_username=self.couchdb_username,
                couchdb_password=self.couchdb_password,
                couchdb_database=self.couchdb_database,
                sync_enabled=False,  # Disable sync for simple queries
            )
            self.data_layer = DataLayer(config)
            self.data_layer.connect()
        return self.data_layer

    def _validate_string_param(self, param_name: str, param_value: str) -> str:
        """
        Validate and clean string parameter.

        Args:
            param_name: Name of the parameter for error messages
            param_value: Value to validate

        Returns:
            Cleaned string value

        Raises:
            ValueError: If parameter is empty or invalid
        """
        if not param_value or not param_value.strip():
            raise ValueError(f"{param_name} cannot be empty")
        return param_value.strip()

    def _validate_document(
        self, document: Dict[str, Any], required_fields: List[str]
    ) -> None:
        """
        Validate document has required fields.

        Args:
            document: Document to validate
            required_fields: List of required field names

        Raises:
            ValueError: If required fields are missing
        """
        if not isinstance(document, dict):
            raise ValueError("Document must be a dictionary")

        missing_fields = [field for field in required_fields if field not in document]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

    def _handle_database_operation(self, operation_name: str, operation_func):
        """
        Generic handler for database operations with error handling and cleanup.

        Args:
            operation_name: Name of the operation for logging
            operation_func: Function to execute

        Returns:
            Result of the operation

        Raises:
            Exception: For database operation errors
        """
        logger.info(f"Starting {operation_name}")

        try:
            data_layer = self._get_data_layer()
            result = operation_func(data_layer)
            logger.info(f"Completed {operation_name} successfully")
            return result

        except Exception as e:
            logger.error(f"Error in {operation_name}: {e}")
            raise Exception(f"Failed to {operation_name}: {e}")

    def close_connection(self):
        """Close database connection."""
        if self.data_layer and self.data_layer.connected:
            try:
                self.data_layer.disconnect()
                logger.info("Database connection closed")
            except Exception as e:
                logger.warning(f"Error closing connection: {e}")


# Chat History Operations
class ChatHistoryQueries(DatabaseQueries):
    """Chat history specific database operations."""

    def extract_chat_history(
        self, user_id: str, conversation_id: str, limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Extract chat history using user_id and conversation_id.

        Args:
            user_id: The user ID to filter by
            conversation_id: The conversation ID to filter by
            limit: Maximum number of records to return (optional)

        Returns:
            List of chat history documents
        """
        # Validate parameters
        user_id = self._validate_string_param("user_id", user_id)
        conversation_id = self._validate_string_param(
            "conversation_id", conversation_id
        )

        def operation(data_layer):
            # Build selector for the query
            selector = {"user_id": user_id, "conversation_id": conversation_id}

            # Execute query
            chat_history = data_layer.find_documents("chat_history", selector, limit)

            # Sort by timestamp if available
            if chat_history:
                try:
                    chat_history = sorted(
                        chat_history, key=lambda x: x.get("timestamp", "")
                    )
                except Exception as e:
                    logger.warning(f"Could not sort by timestamp: {e}")

            return chat_history

        return self._handle_database_operation("extract chat history", operation)

    def save_chat_message(
        self,
        user_id: str,
        conversation_id: str,
        role: str,
        message: str,
        llm_model_version: Optional[str] = None,
        meta_json: Optional[Dict[str, Any]] = None,
        order_id: Optional[str] = None,
        chat_type: str = "chat",
    ) -> str:
        """
        Save a new chat message to the database.

        Args:
            user_id: The user ID
            conversation_id: The conversation ID
            role: Sender type ('user', 'agent', 'system', 'assistant')
            message: The message content
            llm_model_version: Version of the LLM model used (optional)
            meta_json: LLM metadata (optional)
            order_id: Linked order ID (optional)
            chat_type: Type of conversation (default: 'chat')

        Returns:
            Document ID of the saved message
        """
        # Validate required parameters
        user_id = self._validate_string_param("user_id", user_id)
        conversation_id = self._validate_string_param(
            "conversation_id", conversation_id
        )
        role = self._validate_string_param("role", role)
        message = self._validate_string_param("message", message)

        # Create document
        chat_document: Dict[str, Any] = {
            "user_id": user_id,
            "conversation_id": conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": role,
            "message": message,
            "type": chat_type,
        }

        # Add optional fields
        if llm_model_version:
            chat_document["llm_model_version"] = llm_model_version
        if meta_json is not None:
            chat_document["meta_json"] = meta_json
        if order_id:
            chat_document["order_id"] = order_id

        def operation(data_layer):
            return data_layer.save_document("chat_history", chat_document)

        return self._handle_database_operation("save chat message", operation)


# Convenience functions for backward compatibility
def extract_chat_history_simple(
    user_id: str, conversation_id: str, limit: Optional[int] = None
) -> List[Dict[str, Any]]:
    """
    Simple wrapper function for extracting chat history with default settings.

    Args:
        user_id: The user ID to filter by
        conversation_id: The conversation ID to filter by
        limit: Maximum number of records to return (optional)

    Returns:
        List of chat history documents
    """
    queries = ChatHistoryQueries()
    try:
        return queries.extract_chat_history(user_id, conversation_id, limit)
    finally:
        queries.close_connection()


def save_chat_message_simple(
    user_id: str, conversation_id: str, role: str, message: str, **kwargs
) -> str:
    """
    Simple wrapper function for saving chat messages with default settings.

    Args:
        user_id: The user ID
        conversation_id: The conversation ID
        role: Sender type ('user', 'agent', 'system', 'assistant')
        message: The message content
        **kwargs: Additional optional parameters

    Returns:
        Document ID of the saved message
    """
    queries = ChatHistoryQueries()
    try:
        return queries.save_chat_message(
            user_id, conversation_id, role, message, **kwargs
        )
    finally:
        queries.close_connection()


# SQL Query equivalents (for reference)
SQL_QUERIES = {
    "extract_chat_history": """
        SELECT
            chat_id,
            user_id,
            conversation_id,
            timestamp,
            role,
            message,
            llm_model_version,
            meta_json,
            order_id,
            type
        FROM chat_history
        WHERE user_id = %s AND conversation_id = %s
        ORDER BY timestamp ASC
    """,
    "save_chat_message": """
        INSERT INTO chat_history (
            user_id, conversation_id, timestamp, role, message,
            llm_model_version, meta_json, order_id, type
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
    """,
}


# Example usage
if __name__ == "__main__":
    # Example usage
    try:
        # Replace with actual user_id and conversation_id
        user_id = "your_user_id_here"
        conversation_id = "your_conversation_id_here"

        # Extract chat history
        chat_history = extract_chat_history_simple(user_id, conversation_id)

        # Print results
        print(f"Found {len(chat_history)} chat messages:")
        for i, chat in enumerate(chat_history, 1):
            print(
                f"{i}. [{chat.get('role', 'unknown')}] {chat.get('message', '')[:50]}..."
            )
            print(f"   Timestamp: {chat.get('timestamp', 'unknown')}")
            print()

    except Exception as e:
        print(f"Error: {e}")
