# Environment Setup Guide

This guide explains how to configure the Data Layer V3 for different environments and PCs.

> **⚠️ Important**: This project requires a Python virtual environment. Please follow the setup instructions below to create and activate a virtual environment before running any Python commands.

## Overview

The Data Layer V3 now uses environment variables for all configuration settings, making it easy to deploy on different machines without code changes.

## Quick Setup

### Prerequisites

Before you begin, ensure you have `uv` installed. `uv` is a fast Python package installer and resolver.

#### Installing uv

**On macOS and Linux:**

```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

**On Windows:**

```powershell
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

**Using pip:**

```bash
pip install uv
```

### 1. Interactive Setup (Recommended)

1. **Create and activate a virtual environment:**

   ```bash
   # Create a virtual environment with uv
   uv venv --seed

   # Activate the virtual environment
   # On macOS/Linux:
   source .venv/bin/activate
   # On Windows:
   .venv\Scripts\activate
   ```

2. **Install dependencies first:**

   ```bash
   # Install dependencies from requirements.txt
   uv pip install -r requirements.txt
   ```

3. **Run the setup script:**

   ```bash
   # Run the complete setup script (installs dependencies + configures environment)
   python setup.py

   # Or run only environment setup
   python setup.py --env
   ```

This will prompt you for:

- CouchDB settings (host, port, username, password, database)
- PostgreSQL settings (host, port, database, username, password)
- Other configuration options

**Note**: We use `uv pip install -r requirements.txt` instead of `uv pip install -e .` to avoid conflicts with the setup.py script. This ensures all dependencies are available for the setup script to run properly.

### 2. Manual Setup

1. **Create and activate a virtual environment:**

   ```bash
   # Create a virtual environment with uv
   uv venv --seed

   # Activate the virtual environment
   # On macOS/Linux:
   source .venv/bin/activate
   # On Windows:
   .venv\Scripts\activate
   ```

#### Environment File Selection

The environment file loaded depends on the `ENV` variable:

| ENV Value | File Loaded      |
| --------- | ---------------- |
| dev       | .env.development |
| staging   | .env.staging     |
| prod      | .env.production  |

- For local development, create a `.env.development` file in `smart-agent/data_layer_v3/`:

  ```bash
  # .env.development example
  COUCHDB_USERNAME=your_couchdb_username_here
  COUCHDB_PASSWORD=your_couchdb_password_here
  # Add other variables as needed
  ```

- For other environments, create the corresponding file (e.g., `.env.development`, `.env.staging`, `.env.production`) with the appropriate credentials.

- Set the `ENV` environment variable to select the mode (default is `dev`):

  ```bash
  export ENV=dev   # or dev, staging, prod
  ```

- The code will automatically load the correct file based on `ENV`.

#### Using the Environment Template

1. **Ensure your virtual environment is activated:**

   ```bash
   source .venv/bin/activate  # On macOS/Linux
   # .venv\Scripts\activate   # On Windows
   ```

2. **Install dependencies:**

   ```bash
   uv pip install -r requirements.txt
   ```

3. **Copy the environment template**:

   ```bash
   cp env.example .env
   ```

4. **Edit the .env file** with your actual credentials:

   ```bash
   # CouchDB Configuration
   COUCHDB_HOST=localhost
   COUCHDB_PORT=5984
   COUCHDB_USERNAME=your_couchdb_username
   COUCHDB_PASSWORD=your_couchdb_password
   COUCHDB_DATABASE=aagmanai
   COUCHDB_USE_SSL=false

   # PouchDB Configuration
   POUCHDB_NAME=aagmanai_local
   POUCHDB_ADAPTER=idb

   # PostgreSQL Configuration (for SQL queries)
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=aagmanai
   DB_USER=postgres
   DB_PASSWORD=your_postgres_password

   # Connection Settings
   DB_TIMEOUT=30
   DB_MAX_RETRIES=3
   DB_RETRY_DELAY=1.0
   DB_POOL_SIZE=10

   # Sync Settings
   SYNC_ENABLED=true
   SYNC_INTERVAL=5000
   SYNC_LIVE=true
   SYNC_RETRY=true

   # Schema Settings
   SCHEMA_VALIDATION_ENABLED=true
   AUTO_CREATE_DESIGN_DOCS=true
   ```

## Environment Variables

### CouchDB Configuration

| Variable           | Default   | Description                                              |
| ------------------ | --------- | -------------------------------------------------------- |
| `COUCHDB_HOST`     | localhost | CouchDB server hostname                                  |
| `COUCHDB_PORT`     | 5984      | CouchDB server port                                      |
| `COUCHDB_USERNAME` | Required  | CouchDB username (set in .env.development for local dev) |
| `COUCHDB_PASSWORD` | Required  | CouchDB password (set in .env.development for local dev) |
| `COUCHDB_DATABASE` | aagmanai  | CouchDB database name                                    |
| `COUCHDB_USE_SSL`  | false     | Use SSL for CouchDB connections                          |

### PostgreSQL Configuration

| Variable      | Default   | Description                |
| ------------- | --------- | -------------------------- |
| `DB_HOST`     | localhost | PostgreSQL server hostname |
| `DB_PORT`     | 5432      | PostgreSQL server port     |
| `DB_NAME`     | aagmanai  | PostgreSQL database name   |
| `DB_USER`     | postgres  | PostgreSQL username        |
| `DB_PASSWORD` | password  | PostgreSQL password        |

### PouchDB Configuration

| Variable          | Default        | Description                            |
| ----------------- | -------------- | -------------------------------------- |
| `POUCHDB_NAME`    | aagmanai_local | Local PouchDB database name            |
| `POUCHDB_ADAPTER` | idb            | PouchDB adapter (idb, leveldb, memory) |

### Connection Settings

| Variable         | Default | Description                      |
| ---------------- | ------- | -------------------------------- |
| `DB_TIMEOUT`     | 30      | Connection timeout in seconds    |
| `DB_MAX_RETRIES` | 3       | Maximum retry attempts           |
| `DB_RETRY_DELAY` | 1.0     | Delay between retries in seconds |
| `DB_POOL_SIZE`   | 10      | Connection pool size             |

### Sync Settings

| Variable        | Default | Description                   |
| --------------- | ------- | ----------------------------- |
| `SYNC_ENABLED`  | true    | Enable CouchDB-PouchDB sync   |
| `SYNC_INTERVAL` | 5000    | Sync interval in milliseconds |
| `SYNC_LIVE`     | true    | Enable live sync              |
| `SYNC_RETRY`    | true    | Retry failed sync operations  |

### Schema Settings

| Variable                    | Default | Description                  |
| --------------------------- | ------- | ---------------------------- |
| `SCHEMA_VALIDATION_ENABLED` | true    | Enable schema validation     |
| `AUTO_CREATE_DESIGN_DOCS`   | true    | Auto-create design documents |

## Setup Script Options

The `setup.py` script provides several options:

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies first (required for setup.py)
uv pip install -r requirements.txt

# Complete setup (install dependencies + configure environment)
python setup.py

# Environment setup only
python setup.py --env

# Install dependencies only
python setup.py --install

# Check current settings
python setup.py --check
```

## Usage Examples

### Development Environment

```bash
# .env file for development
COUCHDB_HOST=localhost
COUCHDB_PORT=5984
COUCHDB_USERNAME=your_couchdb_username_here
COUCHDB_PASSWORD=your_couchdb_password_here
COUCHDB_DATABASE=aagmanai_dev

DB_HOST=localhost
DB_PORT=5432
DB_NAME=aagmanai_dev
DB_USER=postgres
DB_PASSWORD=dev_password
```

### Production Environment

```bash
# .env file for production
COUCHDB_HOST=prod-couchdb.example.com
COUCHDB_PORT=5984
COUCHDB_USERNAME=prod_user
COUCHDB_PASSWORD=secure_password_123
COUCHDB_DATABASE=aagmanai_prod
COUCHDB_USE_SSL=true

DB_HOST=prod-postgres.example.com
DB_PORT=5432
DB_NAME=aagmanai_prod
DB_USER=prod_user
DB_PASSWORD=secure_password_456
```

### Testing Environment

```bash
# .env file for testing
COUCHDB_HOST=test-couchdb.example.com
COUCHDB_PORT=5984
COUCHDB_USERNAME=test_user
COUCHDB_PASSWORD=test_password
COUCHDB_DATABASE=aagmanai_test

DB_HOST=test-postgres.example.com
DB_PORT=5432
DB_NAME=aagmanai_test
DB_USER=test_user
DB_PASSWORD=test_password
```

## Code Changes

All code now automatically uses environment variables:

### Before (Hardcoded)

```python
config = DataLayerConfig(
    couchdb_host="localhost",
    couchdb_port=5984,
    couchdb_username="your_username",
    couchdb_password="your_password"
)
```

### After (Environment-based)

```python
config = DataLayerConfig()  # Automatically reads from environment
```

### SQL Queries

```python
# Before
conn = psycopg2.connect("your_connection_string")

# After
conn = psycopg2.connect(get_postgres_connection_string())
```

## Verification

### Check Current Settings

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies first (required for setup.py)
uv pip install -r requirements.txt

python setup.py --check
```

### Test Configuration

```bash
# Ensure your virtual environment is activated
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Install dependencies first (required for imports)
uv pip install -r requirements.txt

# Test database connectivity
python -c "from data_layer_v3 import DataLayerConfig; config = DataLayerConfig(); print('Configuration loaded successfully')"
```

## Troubleshooting

### Common Issues

1. **ModuleNotFoundError: No module named 'setuptools'**

   This error occurs when trying to run `python setup.py` without installing dependencies first. The virtual environment created with `uv venv --seed` doesn't include `setuptools` by default.

   **Solution:**

   ```bash
   # Ensure your virtual environment is activated
   source .venv/bin/activate  # On macOS/Linux
   # .venv\Scripts\activate   # On Windows

   # Install dependencies first
   uv pip install -r requirements.txt

   # Then run setup.py
   python setup.py
   ```

2. **Environment file not found**

   ```bash
   # Ensure your virtual environment is activated
   source .venv/bin/activate  # On macOS/Linux
   # .venv\Scripts\activate   # On Windows

   # Install dependencies first
   uv pip install -r requirements.txt

   # Create environment file
   python setup.py --env
   ```

3. **python-dotenv not installed**

   ```bash
   # Ensure your virtual environment is activated
   source .venv/bin/activate  # On macOS/Linux
   # .venv\Scripts\activate   # On Windows

   uv pip install python-dotenv
   ```

4. **Database connection failed**

   - Check if databases are running
   - Verify credentials in `.env` file
   - Check network connectivity

5. **Permission denied**

   - Ensure database user has proper permissions
   - Check firewall settings

6. **Configuration validation errors**
   - Check that all required environment variables are set
   - Verify environment variable formats (e.g., port numbers as integers)
   - Ensure no extra spaces or special characters in values

### Security Notes

- Never commit `.env*` files to version control
- Use strong passwords in production
- Consider using SSL for production databases
- Rotate credentials regularly
- Use environment-specific files (`.env.development`, `.env.staging`, `.env.production`, etc.)

## Migration Guide

### From Hardcoded Configuration

1. **Backup your current configuration**
2. **Run setup script**: `python setup.py --env`
3. **Update your code** to remove hardcoded values
4. **Test thoroughly** with new environment-based config
5. **Deploy with environment files**

### Example Migration

**Before:**

```python
# config.py
class DataLayerConfig(BaseModel):
    couchdb_host: str = "localhost"
    couchdb_port: int = 5984
    # ... hardcoded values
```

**After:**

```python
# config.py
class DataLayerConfig(BaseModel):
    couchdb_host: str = Field(default_factory=lambda: os.getenv("COUCHDB_HOST", "localhost"))
    couchdb_port: int = Field(default_factory=lambda: int(os.getenv("COUCHDB_PORT", "5984")))
    # ... environment-based values
```

## Virtual Environment Best Practices

### Why Use Virtual Environments?

Virtual environments isolate your project dependencies from your system Python installation, preventing conflicts between different projects and ensuring reproducible builds.

### Managing Virtual Environments with uv

```bash
# Create a new virtual environment
uv venv --seed

# Activate the virtual environment
source .venv/bin/activate  # On macOS/Linux
# .venv\Scripts\activate   # On Windows

# Deactivate when done
deactivate

# Remove the virtual environment
rm -rf .venv
```

### Working with Multiple Projects

```bash
# Each project should have its own virtual environment
cd project1
uv venv --seed
source .venv/bin/activate
# Work on project1

cd ../project2
uv venv --seed
source .venv/bin/activate
# Work on project2
```

### Checking Your Environment

```bash
# Check if you're in a virtual environment
which python
# Should show: /path/to/your/project/.venv/bin/python

# Check installed packages
uv pip list

# Check Python version
python --version
```

## Support

For issues with environment setup:

1. Check the troubleshooting section above
2. Verify your `.env` file format
3. Test with the setup script: `python setup.py --check`
4. Review the logs for specific error messages
5. Ensure all required dependencies are installed: `uv pip install -r requirements.txt`
