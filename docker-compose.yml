services:
  # Backend API Service
  backend:
    build:
      context: ./backend_api_module
      dockerfile: Dockerfile
    container_name: aagman-backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend_api_module:/app
      - /app/.venv # Exclude virtual environment
    networks:
      - aagman-network
    depends_on:
      - couchdb
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        BUILD_ENV: development
    container_name: aagman-frontend
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
      - /app/node_modules # Exclude node_modules
    networks:
      - aagman-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:80/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # CouchDB Service (for local development)
  couchdb:
    image: couchdb:3.3.2
    container_name: aagman-couchdb
    ports:
      - "5985:5984" # Changed port to avoid conflicts
    environment:
      - COUCHDB_USER=admin
      - COUCHDB_PASSWORD=password
      - COUCHDB_SINGLE_NODE=true
    volumes:
      - couchdb_data:/opt/couchdb/data
    networks:
      - aagman-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5984/_up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (for production-like setup)
  nginx-proxy:
    image: nginx:alpine
    container_name: aagman-nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx-proxy.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro # For HTTPS in production
    networks:
      - aagman-network
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    profiles:
      - production

volumes:
  couchdb_data:
    driver: local

networks:
  aagman-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
