{"version": "2.0.0", "tasks": [{"label": "Run Backend API Module", "type": "shell", "command": "cd backend_api_module && source .venv/bin/activate && PYTHONPATH=. uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "options": {"cwd": "${workspaceFolder}"}}, {"label": "Activate Backend API Virtual Environment", "type": "shell", "command": "cd backend_api_module && source .venv/bin/activate", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "problemMatcher": []}]}