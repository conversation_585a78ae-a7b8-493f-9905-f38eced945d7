{
    "python.defaultInterpreterPath": "${workspaceFolder}/backend_api_module/.venv/bin/python",

    // ESLint Configuration
    "eslint.enable": true,
    "eslint.workingDirectories": ["frontend"],
    "eslint.validate": [
        "javascript",
        "javascriptreact",
        "typescript",
        "typescriptreact"
    ],

    // Auto-fix on save for frontend files
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },

    // Enable ESLint for the frontend directory specifically
    "[javascript][javascriptreact][typescript][typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll.eslint": "explicit"
        }
    },

    // File associations
    "files.associations": {
        "*.tsx": "typescriptreact",
        "*.ts": "typescript"
    },
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    "python.languageServer": "None",
    "python.analysis.indexing": true,
    "python.analysis.autoSearchPaths": true,
    "python.analysis.extraPaths": [
        "${workspaceFolder}/backend_api_module/src"
    ],
    "cursorpyright.analysis.autoImportCompletions": true,
    "cursorpyright.analysis.autoSearchPaths": true,
    "cursorpyright.analysis.extraPaths": [
      "${workspaceFolder}/backend_api_module/src"
    ],
    "cursorpyright.analysis.typeCheckingMode": "basic"
}
