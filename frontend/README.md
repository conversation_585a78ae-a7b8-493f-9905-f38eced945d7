# Āagman

Āagman is an AI‑based trading automation platform that automates order placement and real‑time monitoring of stocks across multiple brokers and financial sites (e.g., Moneycontrol). Toggle between mock and production data with ease, and navigate pages using our intuitive switch‑style UI.

---

## 🚀 Prerequisites

- Node.js ≥ 16.x
- npm ≥ 8.x

---

## 🔧 Installation

Clone the repository and install dependencies:

```bash
git clone https://github.com/kotilabs/smart-agent.git
cd smart-agent/frontend
npm install
```

---

## ⚙️ Environment Configuration

This project uses environment-specific configuration files:

- **`.env`** - Common environment file
- **`.env.development`** - Local Development
- **`.env.staging`** - Staging environment (staging.aagman.ai)
- **`.env.production`** - Production environment (aagman.ai)

### Environment Variables

Copy `.env.example` to your desired environment file and update the values:

```bash
# For local development
cp .env.example .env.development

# For production
cp .env.example .env.production
```

**Key Environment Variables:**

```dotenv
VITE_USE_MOCK=false                       # Enable mock mode for development
VITE_API_BASE_URL=http://localhost:8000   # Backend API base URL
VITE_WS_URL=ws://localhost:8000           # WebSocket URL for real-time features
VITE_FRONTEND_URL=http://localhost:5173   # Frontend application URL
VITE_NOTIFICATIONS_ENDPOINT=/api/notifications  # Notifications endpoint
VITE_PROFILE_ENDPOINT=/api/profile        # User profile endpoint
VITE_ORDERS_ENDPOINT=/api/v1/orders       # Trading orders endpoint
VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances # Monitoring alerts endpoint
VITE_CHAT_ENDPOINT=/api/v1/chatHistory    # Chat history endpoint
VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat     # WebSocket chat endpoint
VITE_HEALTH_ENDPOINT=/health              # Health check endpoint
```

> **📝 Note**: See `.env.example` for complete documentation and environment-specific examples.

> **🔧 Browser Extension Note**: The executor build uses real API connections pointing to the production server. Ensure proper CORS configuration on the server to allow browser extension connections.

---

## 🔐 Broker Login Status Detection

The application includes intelligent broker login status detection to ensure chat functionality is only available when the user is properly authenticated with their broker.

### How It Works

**Event-Driven Detection (No Constant Polling):**
- **On Extension Mount**: Performs a one-shot broker status check to determine initial login state
- **On Broker Tab Updates**: Monitors `https://kite.zerodha.com/*` page loads and detects login/logout transitions
- **Pre-Send Validation**: Before sending any chat message, validates current broker login status
- **Window Focus Re-check**: When the extension window regains focus, re-validates login status if previously logged out

**Status Persistence:**
- Broker login status is cached in Chrome extension storage to survive service worker restarts
- Status updates are broadcast via `LOGIN_REQUIRED` and `LOGIN_RESOLVED` messages
- Cache is automatically refreshed on broker page navigation events

### User Experience

**When Broker is Logged Out:**
- Chat input is disabled with message: "Please log in to your broker to continue"
- System message appears: "Please log in to zerodha to continue"
- Pre-send validation blocks message attempts with login requirement notification

**When Broker Login is Detected:**
- Chat input is re-enabled automatically
- System message appears: "logged back in, thank you"
- User can resume normal chat functionality

**Detection Scenarios:**
- ✅ Extension opens while broker is logged out → Chat disabled immediately
- ✅ User logs out while using extension → Chat disabled via tab update detection
- ✅ User logs in after being logged out → Chat re-enabled via focus re-check or broadcast
- ✅ Multiple login/logout cycles → Single thank-you message per session (no duplicates)

### Technical Implementation

**Background Service Worker:**
- `BROKER_STATUS_CHECK` message handler for on-demand status validation
- `tabs.onUpdated` listener for Zerodha page navigation events
- Debounced status checks to avoid excessive API calls
- Persistent cache in `chrome.storage.local`

**Frontend Components:**
- `WebSocketProvider`: Manages login state and system messages
- `useWebSocket`: Pre-send validation before message transmission
- `ChatInput`: Disabled state based on `brokerLoginRequired` flag

**Multi-Broker Support:**
- Currently supports Zerodha (hardcoded as default)
- Architecture supports easy extension to additional brokers
- Broker selection will be configurable via user profile settings

---

## 🏃‍♂️ Running the Application

### Development Scripts

- **Default Development** (uses current environment):

  ```bash
  npm run dev
  ```

- **Mock Data Mode** (uses local JSON files):

  ```bash
  VITE_USE_MOCK=true npm run dev
  ```

- **Local Development** (connects to localhost:3000 API):

  ```bash
  VITE_USE_MOCK=false npm run dev
  ```

- **Staging Environment** (connects to staging API):

  ```bash
  npm run staging
  ```

- **Production Environment** (connects to production API):
  ```bash
  npm run prod
  ```

### Build Scripts

- **Default Build** (production mode):

  ```bash
  npm run build
  ```

- **Executor Builds** (packages react app, copies to executor/ui directory; also creates a zip file with incremented version number if staging or prod environment is specified):

  ```bash
  npm run build:executor
  ```

  ```bash
  npm run build:executor:staging
  ```

  ```bash
  npm run build:executor:prod
  ```

  > **📝 Note**: Uses a dedicated build script (`scripts/build-executor.cjs`).

- **Build for Mock Data**:

  ```bash
  VITE_USE_MOCK=true npm run build
  ```

- **Build for Local API Server**:

  ```bash
  VITE_USE_MOCK=false npm run build
  ```

- **Build for Staging**:

  ```bash
  VITE_USE_MOCK=false npm run build:staging
  ```

- **Build for Production**:
  ```bash
  VITE_USE_MOCK=false npm run build:prod
  ```

### Code Quality & Testing

- **Lint Code**:

  ```bash
  npm run lint
  ```

### Preview & Testing

- **Preview Build**:
  ```bash
  npm run preview
  ```

---
