{
  "extends": [
    "eslint:recommended",
    "@typescript-eslint/recommended"
  ],
  "parser": "@typescript-eslint/parser",
  "plugins": [
    "@typescript-eslint",
    "react-hooks",
    "react-refresh"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_",
        "ignoreRestSiblings": true
      }
    ],
    "@typescript-eslint/no-explicit-any": "warn",
    "@typescript-eslint/ban-ts-comment": [
      "error",
      {
        "ts-expect-error": "allow-with-description",
        "ts-ignore": "allow-with-description",
        "minimumDescriptionLength": 10
      }
    ],
    "@typescript-eslint/no-empty-object-type": [
      "error",
      {
        "allowInterfaces": "with-single-extends",
        "allowObjectTypes": "never"
      }
    ],
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "react-refresh/only-export-components": [
      "warn",
      { "allowConstantExport": true }
    ],
    "no-console": ["warn", { "allow": ["warn", "error"] }],
    "no-debugger": "warn",
    "no-alert": "warn",
    "no-unused-expressions": [
      "error",
      {
        "allowShortCircuit": true,
        "allowTernary": true,
        "allowTaggedTemplates": true
      }
    ],
    "prefer-const": "error",
    "no-var": "error",
    "object-shorthand": "warn",
    "prefer-template": "warn",
    "no-duplicate-imports": "error",
    // Prevent empty catch blocks
    "no-empty": [
      "error",
      {
        "allowEmptyCatch": false
      }
    ],
    "no-useless-catch": "error",
    "@typescript-eslint/no-empty-function": [
      "error",
      {
        "allow": [
          "private-constructors",
          "protected-constructors",
          "overrideMethods",
          "arrowFunctions"
        ]
      }
    ],
    "@typescript-eslint/no-misused-promises": "off",
    "@typescript-eslint/require-await": "off"
  },
  "env": {
    "browser": true,
    "es2022": true
  },
  "ignorePatterns": [
    "dist/**",
    "build/**",
    "node_modules/**",
    "*.config.js",
    "*.config.ts",
    "vite.config.ts",
    "tailwind.config.js",
    "postcss.config.js",
    "scripts/**"
  ]
}
