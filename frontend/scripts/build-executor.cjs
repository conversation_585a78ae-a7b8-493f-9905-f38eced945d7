#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

const mode = process.argv[2] || "development";
const versionBumpType = process.argv[3] || "patch";

// Display build information
console.warn(`🚀 Building executor for mode: ${mode}`);
console.warn(`📁 Working directory: ${process.cwd()}`);
console.warn(`📄 Expected environment file: .env.${mode}`);
console.warn(`🔢 Version bump type: ${versionBumpType}`);

// Version management functions
function incrementVersion(version, bumpType = 'patch') {
  const parts = version.split('.');
  // Ensure we have at least 3 parts (major.minor.patch)
  while (parts.length < 3) {
    parts.push('0');
  }

  // Parse version parts as integers
  const major = parseInt(parts[0]);
  const minor = parseInt(parts[1]);
  const patch = parseInt(parts[2]);

  let newMajor = major;
  let newMinor = minor;
  let newPatch = patch;

  switch (bumpType.toLowerCase()) {
    case 'major':
      newMajor = major + 1;
      newMinor = 0;
      newPatch = 0;
      break;
    case 'minor':
      newMinor = minor + 1;
      newPatch = 0;
      break;
    case 'patch':
    default:
      newPatch = patch + 1;
      break;
  }

  return `${newMajor}.${newMinor}.${newPatch}`;
}

function updateManifestVersion(manifestPath, newVersion) {
  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  manifest.version = newVersion;
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
  return manifest.version;
}

function createZipFile(executorPath, version, mode) {
  const zipName = `aagman-extension-${mode}-${version}.zip`;
  const zipPath = path.join(executorPath, '..', zipName);

  console.warn(`Creating zip file: ${zipName}`);

  // Check if zip command is available
  try {
    execSync('which zip', { stdio: 'pipe' });
  } catch (error) {
    console.error("❌ 'zip' command not found. Please install zip utility:");
    console.error("   macOS: brew install zip");
    console.error("   Ubuntu/Debian: sudo apt-get install zip");
    console.error("   CentOS/RHEL: sudo yum install zip");
    return null;
  }

  // Remove existing zip if it exists
  if (fs.existsSync(zipPath)) {
    fs.unlinkSync(zipPath);
  }

  // Create zip file using system zip command
  try {
    execSync(`cd ${executorPath} && zip -r ../${zipName} . -x "*.git*" "*.DS_Store*" "node_modules/*" "*.zip"`, {
      stdio: "inherit"
    });
    console.warn(`✅ Zip file created: ${zipPath}`);
    return zipPath;
  } catch (error) {
    console.error("❌ Failed to create zip file:", error.message);
    return null;
  }
}

// Load environment variables
function loadEnvFile(filePath, required = false) {
  const env = {};

  if (!fs.existsSync(filePath)) {
    if (required) {
      console.error(`❌ Required environment file not found: ${filePath}`);
      console.error(`   Please create this file by copying from env.example:`);
      console.error(`   cp env.example ${path.basename(filePath)}`);
      console.error(`   Then update the values according to your environment.`);
      process.exit(1);
    } else {
      console.warn(`⚠️  Environment file not found: ${filePath}`);
      console.warn(`   Using default values. Consider creating this file for proper configuration.`);
      return env;
    }
  }

  try {
    const content = fs.readFileSync(filePath, "utf8");
    content.split("\n").forEach((line) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith("#")) {
        const [key, ...valueParts] = trimmedLine.split("=");
        if (key && valueParts.length > 0) {
          env[key.trim()] = valueParts.join("=").trim();
        }
      }
    });

    if (required && Object.keys(env).length === 0) {
      console.error(`❌ Environment file is empty or contains no valid variables: ${filePath}`);
      console.error(`   Please ensure the file contains properly formatted environment variables.`);
      process.exit(1);
    }

    console.warn(`✅ Loaded environment file: ${filePath} (${Object.keys(env).length} variables)`);
    return env;
  } catch (error) {
    console.error(`❌ Failed to read environment file: ${filePath}`);
    console.error(`   Error: ${error.message}`);
    if (required) {
      process.exit(1);
    }
    return env;
  }
}

// Validate mode parameter
const validModes = ['development', 'staging', 'production'];
if (!validModes.includes(mode)) {
  console.error(`❌ Invalid mode: ${mode}`);
  console.error(`   Valid modes are: ${validModes.join(', ')}`);
  console.error(`   Usage: node build-executor.cjs [development|staging|production]`);
  process.exit(1);
}

// Load environment file - required for all modes
const envFilePath = `.env.${mode}`;
const executorEnv = loadEnvFile(envFilePath, true);

// Validate required environment variables
const requiredEnvVars = [
  'VITE_API_BASE_URL',
  'VITE_WS_URL',
  'VITE_FRONTEND_URL'
];

const missingVars = requiredEnvVars.filter(varName => !executorEnv[varName]);
if (missingVars.length > 0) {
  console.error(`❌ Missing required environment variables in ${envFilePath}:`);
  missingVars.forEach(varName => {
    console.error(`   - ${varName}`);
  });
  console.error(`   Please ensure all required variables are set in your environment file.`);
  console.error(`   See env.example for reference.`);
  process.exit(1);
}

// Determine if this is a development build
const isDevelopmentBuild = mode === 'dev';

// Set environment variables for the build process
Object.entries(executorEnv).forEach(([key, value]) => {
  process.env[key] = value;
});

console.warn(
  "Building executor with environment variables:",
  executorEnv
);
if (isDevelopmentBuild) {
  console.warn("🔧 Development build detected - will enable auto-reload");
}

try {
  // Increment version in manifest.json
  const manifestPath = path.join(__dirname, '..', '..', 'executor', 'manifest.json');
  const currentVersion = JSON.parse(fs.readFileSync(manifestPath, 'utf8')).version;
  const newVersion = isDevelopmentBuild ? currentVersion : incrementVersion(currentVersion, versionBumpType);

  if (!isDevelopmentBuild) {
    console.warn(`📦 Incrementing version from ${currentVersion} to ${newVersion}`);
    updateManifestVersion(manifestPath, newVersion);
  }

  // Run TypeScript compilation
  console.warn("Running TypeScript compilation...");
  execSync("npx tsc --skipLibCheck --noEmit", { stdio: "inherit" });

  // Run Vite build
  console.warn("Running Vite build...");
  execSync(`npx vite build --mode ${mode}`, { stdio: "inherit" });

  // Create executor/ui directory and copy files
  console.warn("Copying files to executor/ui...");
  execSync("mkdir -p ../executor/ui", { stdio: "inherit" });
  execSync("cp -r dist/* ../executor/ui/", { stdio: "inherit" });

  // Run npm install in executor to bundle dependencies
  console.warn("Installing packages in exector...");
  execSync(`cd ../executor && npm install && npm run build && cd "${__dirname}"`, { stdio: "inherit" });

  // Add development auto-reload functionality
  if (isDevelopmentBuild) {
    console.warn("🔧 Adding auto-reload functionality...");

    // Create timestamp file
    const timestamp = Date.now().toString();
    fs.writeFileSync("../executor/ui/build-timestamp.txt", timestamp);

    // Copy auto-reload script
    if (fs.existsSync("scripts/dev-reload.js")) {
      fs.copyFileSync("scripts/dev-reload.js", "../executor/ui/dev-reload.js");
    }

    // Inject auto-reload script into HTML
    const htmlPath = "../executor/ui/index.html";
    if (fs.existsSync(htmlPath)) {
      let htmlContent = fs.readFileSync(htmlPath, "utf8");

      // Add auto-reload script before closing body tag
      const autoReloadScript = `  <script src="dev-reload.js"></script>\n</body>`;
      htmlContent = htmlContent.replace("</body>", autoReloadScript);

      fs.writeFileSync(htmlPath, htmlContent);
      console.warn("✅ Auto-reload script injected into HTML");
    }
  } else {
    // Create zip file with version
    const executorPath = path.join(__dirname, '..', '..', 'executor');
    const zipPath = createZipFile(executorPath, newVersion, mode);

    console.warn("✅ Executor build completed successfully!");
    console.warn(`📦 Version: ${newVersion}`);
    if (zipPath) {
      console.warn(`📦 Zip file: ${zipPath}`);
    }
  }
} catch (error) {
  console.error("❌ Build failed:", error.message);
  process.exit(1);
}
