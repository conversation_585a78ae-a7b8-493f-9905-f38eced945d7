#!/usr/bin/env node

const { execSync } = require("child_process");
const fs = require("fs");
const path = require("path");

let isBuilding = false;
let buildTimeout = null;

function runCommand(command, description) {
  console.warn(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() });
    console.warn(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
  }
}

function debouncedBuild(mode) {
  if (buildTimeout) {
    clearTimeout(buildTimeout);
  }

  buildTimeout = setTimeout(() => {
    if (isBuilding) return;

    isBuilding = true;
    console.warn("");
    console.warn("🔄 File change detected, rebuilding...");

    // Step 2: Build executor
    runCommand(`node scripts/build-executor.cjs ${mode}`, "Building executor");

    console.warn("✅ Rebuild complete! Extension updated.");
    console.warn(
      "🔄 Auto-reload enabled - extension will refresh automatically!"
    );
    console.warn("👀 Watching for changes...");
    console.warn("");

    isBuilding = false;
  }, 500); // Debounce for 500ms
}

const mode = process.argv[2] || "executor";

console.warn("🚀 Starting file watcher for extension development...");
console.warn(`📝 Mode: ${mode}`);
console.warn("");

// Initial build
runCommand(`node scripts/build-executor.cjs ${mode}`, "Initial build");

console.warn("");
console.warn("✅ Initial build complete!");
console.warn(`📁 Extension location: ${path.join(__dirname, '..', '..', 'executor')}`);
console.warn("");
console.warn("👀 Now watching for file changes...");
console.warn("🔄 Any changes to src/ will automatically rebuild the extension");
console.warn("💡 Use Ctrl+C to stop watching");
console.warn("");

// Watch for changes in src directory
const srcDir = path.resolve(__dirname, "../src");

try {
  fs.watch(srcDir, { recursive: true }, (eventType, filename) => {
    if (
      filename &&
      (filename.endsWith(".tsx") ||
        filename.endsWith(".ts") ||
        filename.endsWith(".css"))
    ) {
      console.warn(`📝 Changed: ${filename}`);
      debouncedBuild(mode);
    }
  });

  console.warn("🔍 Watching directory:", srcDir);
  console.warn(
    "📱 Ready! Make changes to your React components and see them in the extension."
  );
} catch (error) {
  console.error("❌ Failed to start file watcher:", error.message);
  process.exit(1);
}
