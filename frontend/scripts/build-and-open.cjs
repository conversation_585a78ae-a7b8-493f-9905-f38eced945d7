#!/usr/bin/env node

const { execSync } = require("child_process");

function runCommand(command, description) {
  console.warn(`🔄 ${description}...`);
  try {
    execSync(command, { stdio: "inherit", cwd: process.cwd() });
    console.warn(`✅ ${description} completed`);
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    process.exit(1);
  }
}

const mode = process.argv[2] || "executor";

console.warn("🚀 Building extension for development...");
console.warn(`📝 Mode: ${mode}`);
console.warn("");

// Build executor
runCommand(`node scripts/build-executor.cjs ${mode}`, "Building executor");

console.warn("");
console.warn("🎉 Extension built successfully!");
console.warn(`📁 Location: ${require('path').join(__dirname, '..', '..', 'executor')}`);
console.warn("");
console.warn("🔄 Next: Refresh extension in Chrome (if already loaded)");
console.warn("💡 Or run 'npm run extension:open' for setup instructions");
