#!/usr/bin/env node

const { execSync } = require("child_process");
const path = require("path");

console.warn("🔍 CHROME EXTENSION DEBUGGING");
console.warn("================================");

// Check Chrome installation
console.warn("\n1. Chrome Installation:");
try {
  const chromeVersion = execSync(
    "/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --version",
    { encoding: "utf8" }
  );
  console.warn("✅ Chrome found:", chromeVersion.trim());
} catch (error) {
  console.warn("❌ Chrome not found at expected location");
}

// Check extension directory
const extensionPath = path.resolve(__dirname, "../../executor");
console.warn("\n2. Extension Directory:");
console.warn("📁 Path:", extensionPath);

const fs = require("fs");
if (fs.existsSync(extensionPath)) {
  console.warn("✅ Extension directory exists");

  // Check manifest
  const manifestPath = path.join(extensionPath, "manifest.json");
  if (fs.existsSync(manifestPath)) {
    console.warn("✅ manifest.json exists");
    try {
      const manifest = JSON.parse(fs.readFileSync(manifestPath, "utf8"));
      console.warn("✅ Manifest is valid JSON");
      console.warn("   Name:", manifest.name);
      console.warn("   Version:", manifest.version);
    } catch (error) {
      console.warn("❌ manifest.json is invalid:", error.message);
    }
  } else {
    console.warn("❌ manifest.json missing");
  }

  // Check UI files
  const uiPath = path.join(extensionPath, "ui");
  if (fs.existsSync(uiPath)) {
    console.warn("✅ ui directory exists");
    const indexPath = path.join(uiPath, "index.html");
    if (fs.existsSync(indexPath)) {
      console.warn("✅ ui/index.html exists");
    } else {
      console.warn("❌ ui/index.html missing");
    }
  } else {
    console.warn("❌ ui directory missing");
  }
} else {
  console.warn("❌ Extension directory does not exist");
}

console.warn("\n3. Testing Chrome Extensions Page:");
console.warn("Opening Chrome to extensions page...");

try {
  execSync('open -a "Google Chrome" "chrome://extensions/"');
  console.warn("✅ Chrome command executed");
  console.warn("\n🔍 WHAT TO CHECK IN CHROME:");
  console.warn("1. Did Chrome open to chrome://extensions/ ?");
  console.warn('2. Do you see "Developer mode" toggle in top-right?');
  console.warn('3. Is "Developer mode" currently ON or OFF?');
  console.warn('4. After turning ON, do you see "Load unpacked" button?');
  console.warn("\n📋 Extension path for manual loading:");
  console.warn(extensionPath);
} catch (error) {
  console.warn("❌ Failed to open Chrome:", error.message);
}
