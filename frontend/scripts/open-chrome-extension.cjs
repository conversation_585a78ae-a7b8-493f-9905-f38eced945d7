#!/usr/bin/env node

const path = require("path");

const extensionPath = path.resolve(__dirname, "../../executor");

console.warn("✅ Extension built and ready!");
console.warn("");
console.warn("📁 Extension location:", extensionPath);
console.warn("");
console.warn("🔧 TO LOAD EXTENSION IN CHROME:");
console.warn("1️⃣  Open Chrome → chrome://extensions/");
console.warn("2️⃣  Enable 'Developer mode' (top-right toggle)");
console.warn("3️⃣  Click 'Load unpacked'");
console.warn("4️⃣  Navigate to and select the executor folder");
console.warn("5️⃣  Extension loads → Click puzzle piece icon 🧩");
console.warn("");
console.warn("🔄 AFTER CODE CHANGES:");
console.warn("• Run npm run dev (or VITE_USE_MOCK=true npm run dev) to rebuild");
console.warn("• Click refresh icon on extension card in chrome://extensions/");
console.warn("• Or press Ctrl+R in the extension's side panel");
