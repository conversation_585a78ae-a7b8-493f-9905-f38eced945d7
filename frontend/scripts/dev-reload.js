// Development-only auto-reload script for Chrome extension
// This gets injected only during development builds

const DEV_RELOAD_INTERVAL = 200; // Check every 200ms for super fast reloads
let lastReloadTime = Date.now();

// Function to check if extension needs reloading
async function checkForReload() {
  try {
    // Try to fetch a timestamp file that gets updated on each build
    // Add cache-busting parameter to ensure fresh fetch
    const response = await fetch(
      chrome.runtime.getURL("ui/build-timestamp.txt") + "?t=" + Date.now()
    );
    const buildTime = await response.text();
    const buildTimestamp = parseInt(buildTime.trim());

    if (buildTimestamp > lastReloadTime) {
      console.warn("🔄 Extension code updated, reloading side panel...");
      lastReloadTime = buildTimestamp;

      // For side panel extensions, reload the current window
      if (window.location) {
        window.location.reload();
      } else {
        // Fallback: reload entire extension
        chrome.runtime.reload();
      }
    }
  } catch (error) {
    // Ignore errors - timestamp file might not exist yet
    console.debug("Auto-reload check failed:", error.message);
  }
}

// Only run in development mode
if (typeof window !== "undefined") {
  console.warn("🔧 Development auto-reload enabled for side panel");

  // Set initial reload time to current timestamp
  fetch(chrome.runtime.getURL("ui/build-timestamp.txt"))
    .then((response) => response.text())
    .then((buildTime) => {
      lastReloadTime = parseInt(buildTime.trim());
      console.warn("📅 Initial build timestamp:", lastReloadTime);
    })
    .catch(() => {
      // Ignore errors
    });

  // Check for updates every second
  setInterval(checkForReload, DEV_RELOAD_INTERVAL);

  // Also check when the extension starts
  setTimeout(checkForReload, 2000);
}
