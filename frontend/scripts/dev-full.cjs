#!/usr/bin/env node

const { spawn } = require("child_process");

const mode = process.argv[2] || "development";

console.warn(`🚀 Starting full development environment for mode: ${mode}`);
console.warn(`📱 Extension watcher + 🌐 Web dev server`);
console.warn(`============================================`);

// Colors for different processes
const colors = {
  reset: "\x1b[0m",
  red: "\x1b[31m",
  green: "\x1b[32m",
  yellow: "\x1b[33m",
  blue: "\x1b[34m",
  magenta: "\x1b[35m",
  cyan: "\x1b[36m",
};

// Helper function to prefix output with colors
function prefixOutput(prefix, color, data) {
  const lines = data
    .toString()
    .split("\n")
    .filter((line) => line.trim());
  lines.forEach((line) => {
    console.warn(`${color}[${prefix}]${colors.reset} ${line}`);
  });
}

// Start the extension watcher
console.warn(`${colors.cyan}Starting extension watcher...${colors.reset}`);
const extensionWatcher = spawn("node", ["scripts/watch-and-build.cjs", mode], {
  stdio: "pipe",
  cwd: process.cwd(),
});

// Start the web dev server
console.warn(`${colors.green}Starting web dev server...${colors.reset}`);

console.warn(`${colors.green}Running: npm run ${mode}${colors.reset}`);
const webServer = spawn("npm", ["run", mode], {
  stdio: "pipe",
  cwd: process.cwd(),
  shell: true,
});

// Handle extension watcher output
extensionWatcher.stdout.on("data", (data) => {
  prefixOutput("EXT", colors.cyan, data);
});

extensionWatcher.stderr.on("data", (data) => {
  prefixOutput("EXT-ERR", colors.red, data);
});

// Handle web server output
webServer.stdout.on("data", (data) => {
  prefixOutput("WEB", colors.green, data);
});

webServer.stderr.on("data", (data) => {
  prefixOutput("WEB-ERR", colors.yellow, data);
});

// Handle process exits
extensionWatcher.on("close", (code) => {
  console.warn(
    `${colors.red}Extension watcher exited with code ${code}${colors.reset}`
  );
  if (webServer && !webServer.killed) {
    console.warn(`${colors.yellow}Terminating web server...${colors.reset}`);
    webServer.kill();
  }
  process.exit(code);
});

webServer.on("close", (code) => {
  console.warn(
    `${colors.red}Web server exited with code ${code}${colors.reset}`
  );
  if (extensionWatcher && !extensionWatcher.killed) {
    console.warn(
      `${colors.yellow}Terminating extension watcher...${colors.reset}`
    );
    extensionWatcher.kill();
  }
  process.exit(code);
});

// Handle Ctrl+C gracefully
process.on("SIGINT", () => {
  console.warn(
    `\n${colors.yellow}Received SIGINT, shutting down gracefully...${colors.reset}`
  );

  if (extensionWatcher && !extensionWatcher.killed) {
    extensionWatcher.kill("SIGINT");
  }

  if (webServer && !webServer.killed) {
    webServer.kill("SIGINT");
  }

  process.exit(0);
});

console.warn(`${colors.magenta}✨ Both processes started!${colors.reset}`);
console.warn(`${colors.blue}🔌 Extension: Auto-reload enabled`);
console.warn(`🌐 Web server: http://localhost:5173`);
console.warn(`🧪 Test login: Open extension → Click Login → New tab opens`);
console.warn(
  `${colors.yellow}Press Ctrl+C to stop both processes${colors.reset}`
);
