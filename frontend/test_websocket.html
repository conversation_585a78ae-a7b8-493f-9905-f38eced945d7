<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebSocket Test Setup</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      button {
        background-color: #5c54fd;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        margin: 10px 5px;
      }
      button:hover {
        background-color: #4c44ed;
      }
      .status {
        margin: 10px 0;
        padding: 10px;
        border-radius: 4px;
        font-weight: bold;
      }
      .success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
      }
      .info {
        background-color: #d1ecf1;
        color: #0c5460;
        border: 1px solid #bee5eb;
      }
      .code {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 4px;
        font-family: "Courier New", monospace;
        border: 1px solid #dee2e6;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>WebSocket Test Setup</h1>

      <div class="status info">
        <strong>Instructions:</strong> Click "Setup Test IDs" to add the
        required localStorage values for WebSocket testing.
      </div>

      <div id="status"></div>

      <h2>Step 1: Setup Test IDs</h2>
      <button onclick="setupTestIds()">Setup Test IDs</button>
      <button onclick="clearTestIds()">Clear Test IDs</button>
      <button onclick="checkTestIds()">Check Current IDs</button>

      <h2>Step 2: Environment Configuration</h2>
      <div class="code">
        <strong>For mock mode:</strong><br />
        VITE_USE_MOCK=true npm run dev
      </div>

      <div class="code">
        <strong>For local development:</strong><br />
        VITE_USE_MOCK=false npm run dev
      </div>

      <h2>Step 3: Test Features</h2>
      <div class="info">
        <strong>WebSocket Features to Test:</strong>
        <ul>
          <li>✅ Connection management with automatic reconnection</li>
          <li>✅ Tab-specific message routing (chat, orders, monitoring)</li>
          <li>✅ Action buttons with automatic tab switching</li>
          <li>✅ Message queuing when offline</li>
          <li>✅ Network-aware reconnection</li>
          <li>✅ Connection status indicators</li>
          <li>✅ Error handling and fallback mechanisms</li>
          <li>✅ Model selection for chat messages</li>
        </ul>
      </div>

      <h2>WebSocket API Test</h2>
      <div class="code">
        <strong>Expected WebSocket Message Format:</strong><br />
        {<br />
        &nbsp;&nbsp;"user_id": "test-user-123",<br />
        &nbsp;&nbsp;"conversation_id": "test-conversation-456",<br />
        &nbsp;&nbsp;"brokerName": "zerodha",<br />
        &nbsp;&nbsp;"message": "Your message here",<br />
        &nbsp;&nbsp;"typeOfMessage": "chat|orders|monitoring",<br />
        &nbsp;&nbsp;"modelId": "model-identifier",<br />
        &nbsp;&nbsp;"sender": "user"<br />
        }
      </div>

      <div class="code">
        <strong>Expected WebSocket Response Format:</strong><br />
        {<br />
        &nbsp;&nbsp;"message": "System response",<br />
        &nbsp;&nbsp;"sender": "system",<br />
        &nbsp;&nbsp;"actions": [<br />
        &nbsp;&nbsp;&nbsp;&nbsp;{<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"description": "Button Label",<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"type": "chat|orders|monitoring",<br />
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"message": "action_command"<br />
        &nbsp;&nbsp;&nbsp;&nbsp;}<br />
        &nbsp;&nbsp;]<br />
        }
      </div>
    </div>

    <script>
      function setupTestIds() {
        localStorage.setItem("user_id", "test-user-123");
        localStorage.setItem("conversation_id", "test-conversation-456");
        updateStatus("✅ Test IDs have been set up successfully!", "success");
      }

      function clearTestIds() {
        localStorage.removeItem("user_id");
        localStorage.removeItem("conversation_id");
        updateStatus("🗑️ Test IDs have been cleared.", "info");
      }

      function checkTestIds() {
        const userId = localStorage.getItem("user_id");
        const conversationId = localStorage.getItem("conversation_id");

        if (userId && conversationId) {
          updateStatus(
            `✅ Test IDs are set:<br>User ID: ${userId}<br>Conversation ID: ${conversationId}`,
            "success"
          );
        } else {
          updateStatus(
            '❌ Test IDs are not set. Please click "Setup Test IDs" first.',
            "info"
          );
        }
      }

      function updateStatus(message, type) {
        const statusDiv = document.getElementById("status");
        statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
      }

      // Check IDs on page load
      window.onload = function () {
        checkTestIds();
      };
    </script>
  </body>
</html>
