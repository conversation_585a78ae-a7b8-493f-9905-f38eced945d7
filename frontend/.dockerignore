# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production build
# dist/ # needed for docker build
build/

# Environment files
.env
.env.local
.env.development
.env.staging
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
.nyc_output/
.jest/

# Logs
*.log
logs/

# Documentation
docs/
*.md
README*

# Git
.git/
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Development
.eslintrc*
.prettierrc*
.editorconfig
scratchpad.md
