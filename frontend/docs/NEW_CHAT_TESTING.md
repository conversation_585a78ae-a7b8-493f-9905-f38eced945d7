# New Chat Functionality Testing Guide

## Overview

This guide explains how to test the new chat functionality that allows users to start a fresh conversation within the current tab by clearing message history and obtaining a new conversation_id.

## New Chat Flow

### **User Action**: Click the "New Chat" button (message-plus-circle icon)

### **System Behavior**:

1. **Clear Current Tab Messages** → UI shows empty chat
2. **API Call** → `POST /chatHistory` with `user_id` + `conversation_id: null`
3. **Receive Response** → New `conversation_id` for fresh conversation
4. **Update Session** → Replace old conversation_id with new one in localStorage
5. **Ready for Input** → User can start typing in clean slate

## Visual Elements

### **New Chat Button**

- **Location**: Sidebar, above notifications bell
- **Icon**: `message-plus-circle.svg` (purple plus icon in circle)
- **Behavior**:
  - Hover: Slight scale and gradient background
  - Click: Shows loading spinner
  - Disabled: Grayed out during loading

### **Loading States**

- **Loading**: Spinning circle animation
- **But<PERSON> text**: "Starting new chat..."
- **Disabled**: Prevents multiple clicks

## Testing Scenarios

### 1. Basic New Chat Flow

**Setup**: Have an active session with existing messages

```javascript
// Verify existing session
console.log(JSON.parse(localStorage.getItem("orderGPT_session")));
// Should show existing user_id and conversation_id
```

**Test Steps**:

1. Click the new chat button (message-plus-circle)
2. Observe loading state
3. Check network requests
4. Verify message history cleared
5. Confirm new conversation_id in localStorage

**Expected Results**:

- ✅ Loading spinner appears
- ✅ POST request to `/chatHistory` with `user_id` and `conversation_id: null`
- ✅ Messages in current tab disappear
- ✅ New conversation_id stored in localStorage
- ✅ Chat input ready for new message

### 2. Tab-Specific New Chat

**Test Flow**:

1. **Chat Tab**: Send message → Click new chat → Verify chat messages cleared
2. **Orders Tab**: Switch to orders → Click new chat → Verify order messages cleared
3. **Monitoring Tab**: Switch to monitoring → Click new chat → Verify monitoring messages cleared

**Expected**: Each tab maintains its own conversation history and new chat clears only the active tab.

### 3. Error Handling

**Test Scenarios**:

**3.1 No User Session**

```javascript
// Clear session
localStorage.removeItem("orderGPT_session");
// Click new chat button
```

**Expected**: Error message about missing user_id

**3.2 API Failure**

- Disconnect network
- Click new chat button
  **Expected**: Error message and fallback behavior

**3.3 Multiple Clicks**

- Click new chat button rapidly
  **Expected**: Only one request sent, button disabled during processing

### 4. WebSocket Integration

**Test Steps**:

1. Start new chat
2. Send a message via WebSocket
3. Verify message includes new conversation_id

**Expected WebSocket Message Format**:

```json
{
  "user_id": "existing-user-id",
  "conversation_id": "NEW-conversation-id",
  "brokerName": "zerodha",
  "message": "test message",
  "typeOfMessage": "chat",
  "modelId": "default-model",
  "sender": "user"
}
```

## Manual Testing Commands

### Check Current Session

```javascript
// Check current session data
const session = JSON.parse(localStorage.getItem("orderGPT_session"));
console.log("Current session:", session);

// Check WebSocket store state
import { useWebSocketStore } from "./src/stores/websocketStore";
const wsStore = useWebSocketStore.getState();
console.log("WebSocket user_id:", wsStore.user_id);
console.log("WebSocket conversation_id:", wsStore.conversation_id);
```

### Test New Chat Manually

```javascript
// Test new chat function directly
import { handleNewChat } from "./src/utils/navigationManager";

handleNewChat()
  .then((result) => {
    console.log("New chat result:", result);
  })
  .catch((error) => {
    console.error("New chat error:", error);
  });
```

### Verify Message Clearing

```javascript
// Before new chat
import { useWebSocketStore } from "./src/stores/websocketStore";
const store = useWebSocketStore.getState();
console.log("Messages before:", store.chatMessages.length);

// After new chat (should be 0)
console.log("Messages after:", store.chatMessages.length);
```

## Network Request Verification

### Expected API Call

```http
POST /chatHistory
Content-Type: application/json

{
  "user_id": "existing-user-id",
  "conversation_id": null,
  "type": "chat",
  "brokerName": "zerodha"
}
```

### Expected Response

```json
{
  "user_id": "existing-user-id",
  "conversation_id": "new-conversation-id",
  "history": []
}
```

## Browser DevTools Testing

### Network Tab

1. Open DevTools → Network
2. Click new chat button
3. Look for POST request to `/chatHistory`
4. Verify request payload has `conversation_id: null`
5. Check response contains new conversation_id

### Console Testing

```javascript
// Monitor WebSocket store changes
const unsubscribe = useWebSocketStore.subscribe(
  (state) => state.chatMessages,
  (messages) => console.log("Chat messages updated:", messages.length)
);

// Monitor session changes
const sessionBefore = localStorage.getItem("orderGPT_session");
// ... click new chat button ...
const sessionAfter = localStorage.getItem("orderGPT_session");
console.log("Session before:", JSON.parse(sessionBefore));
console.log("Session after:", JSON.parse(sessionAfter));
```

### Application Tab

1. Application → Local Storage
2. Check `orderGPT_session` before and after new chat
3. Verify conversation_id changes for the active tab

## Mock Mode Testing

### Development Environment

```javascript
// Mock response will be
{
  "success": true,
  "user_id": "mock-user-id",
  "conversation_id": "mock-conversation-id",
  "history": []
}
```

### Console Logs to Look For

- `"Mock: POST request"` with chatHistory data
- `"Starting new chat for zerodha chat"`
- `"Cleared messages for chat tab"`
- `"New chat session created for zerodha chat"`

## Error Scenarios Testing

### 1. No User ID

```javascript
localStorage.setItem("orderGPT_session", JSON.stringify({}));
// Click new chat → Should show error
```

### 2. Invalid Session Data

```javascript
localStorage.setItem("orderGPT_session", "invalid-json");
// Click new chat → Should handle gracefully
```

### 3. Network Failure

- Disconnect internet
- Click new chat
- Should show error message

## Success Criteria

✅ **Button Behavior**: Loading states work correctly
✅ **Message Clearing**: Current tab messages disappear
✅ **Session Update**: New conversation_id stored in localStorage
✅ **Network Request**: Correct API call with null conversation_id
✅ **WebSocket Integration**: New messages use new conversation_id
✅ **Error Handling**: Graceful error messages and fallbacks
✅ **Tab Isolation**: Each tab maintains separate conversation history
✅ **UI Feedback**: Clear visual feedback during the process

## Performance Testing

### Load Testing

- Click new chat button multiple times quickly
- Verify only one request is sent
- Check for memory leaks in message arrays

### Stress Testing

- Create many new chats in succession
- Verify localStorage doesn't grow unbounded
- Check WebSocket connection stability

## Accessibility Testing

### Keyboard Navigation

- Tab to new chat button
- Press Enter to activate
- Verify screen reader announces state changes

### Screen Reader Testing

- Button should announce "Start New Chat"
- Loading state should announce "Starting new chat..."
- Success/error states should be announced

## Production Readiness Checklist

- [ ] All error scenarios handled gracefully
- [ ] Loading states prevent user confusion
- [ ] Network requests are properly structured
- [ ] localStorage updates are atomic
- [ ] WebSocket integration is seamless
- [ ] UI feedback is clear and immediate
- [ ] Performance is acceptable under load
- [ ] Accessibility requirements are met

## Common Issues & Solutions

### Issue: Button doesn't respond

**Solution**: Check for JavaScript errors, verify imports

### Issue: Messages don't clear

**Solution**: Check WebSocket store state, verify clearCurrentTabMessages

### Issue: Wrong conversation_id used

**Solution**: Verify session update timing, check WebSocket store sync

### Issue: Multiple API calls

**Solution**: Check loading state management, verify button disable logic
