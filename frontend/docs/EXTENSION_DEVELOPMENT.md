# Extension Development Guide

This guide explains how to develop and test the Āagman Chrome extension with automatic rebuilding.

## Quick Start

### Extension Mode (Recommended)

**One-time build:**

```bash
# Development with mock data
VITE_USE_MOCK=true npm run dev

# Development with local backend
VITE_USE_MOCK=false npm run dev
```

**Auto-rebuild on file changes (Best for Development):**

```bash
# Development with mock data + file watching
VITE_USE_MOCK=true npm run dev-env-watch

# Development with local backend + file watching
VITE_USE_MOCK=false npm run dev-env-watch
```

### Traditional Web Development

If you want to develop in the traditional web browser (not extension):

```bash
# Development with mock data + Web browser
VITE_USE_MOCK=true npm run dev-env

# Development with local backend + Web browser
VITE_USE_MOCK=false npm run dev-env
```

## What Happens When You Run Commands

### `VITE_USE_MOCK=true npm run dev-env` or `VITE_USE_MOCK=false npm run dev-env` (One-time build)

1. **Build Executor**: Runs `scripts/build-executor.cjs` to compile and copy files to `executor/ui/`
2. **Ready**: Shows extension location and manual loading instructions

### `VITE_USE_MOCK=true npm run dev-env-watch` or `VITE_USE_MOCK=false npm run dev-env-watch` (Auto-rebuild)

1. **Initial Build**: Same as above
2. **File Watching**: Monitors `src/` for `.tsx`, `.ts`, `.css` changes
3. **Auto-Rebuild**: Automatically rebuilds when files change (debounced 500ms)
4. **Keeps Running**: Continues watching until you press Ctrl+C

## Manual Commands

### Just Open Chrome with Extension

```bash
npm run extension:open
```

## Extension Development Workflow

### First Time Setup (One-time only)

1. **Start Development**:

   ```bash
   VITE_USE_MOCK=true npm run dev-env-watch
   ```

   OR

   ```bash
   VITE_USE_MOCK=false npm run dev-env-watch
   ```

2. **Load Extension in Chrome** (manual - one time only):
   - Open Chrome → `chrome://extensions/`
   - Enable "Developer mode" (top-right toggle)
   - Click "Load unpacked"
   - Navigate to: `smart-agent/executor` directory
   - Click "Select" to load extension

3. **Open Extension**: Click the puzzle piece icon (🧩) in Chrome toolbar

### Development Loop (Auto-rebuild)

1. **Keep Running**: `VITE_USE_MOCK=true npm run dev-env-watch` or `VITE_USE_MOCK=false npm run dev-env-watch` should still be running
2. **Make Changes**: Edit files in `src/` directory
3. **Auto-Rebuild**: Files automatically rebuild when saved!
4. **Refresh Extension**: Click refresh icon on extension card in `chrome://extensions/` or press Ctrl+R in the extension panel
5. **Test**: See your changes immediately in the extension

### Development Loop (Manual rebuild)

1. **Make Changes**: Edit files in `src/` directory
2. **Rebuild**: Run `VITE_USE_MOCK=true npm run dev-env` or `VITE_USE_MOCK=false npm run dev-env`
3. **Refresh Extension**: Click refresh icon on extension card in `chrome://extensions/` or press Ctrl+R in the extension panel
4. **Test**: See your changes in the extension

## Troubleshooting

### Chrome Doesn't Open Automatically

If Chrome doesn't open automatically, follow these manual steps:

1. Open Chrome
2. Go to `chrome://extensions/`
3. Enable "Developer mode" (top right toggle)
4. Click "Load unpacked" and select the `executor` directory
5. Click the extension icon to open the side panel

### Extension Not Loading

- Make sure the `executor/ui/` directory has built files
- Check if `executor/manifest.json` exists
- Try reloading the extension in Chrome

### Build Errors

- Make sure you're in the `frontend/` directory
- Check if all dependencies are installed: `npm install`
- Rebuild: `VITE_USE_MOCK=true npm run dev-env-watch` or `VITE_USE_MOCK=false npm run dev-env-watch`

## Directory Structure

```
frontend/
├── scripts/
│   ├── build-executor.cjs       # Builds extension files
│   ├── open-chrome-extension.js # Opens Chrome with extension
│   └── build-and-open.js        # Main orchestration script
└── package.json                 # Updated with new commands

executor/
├── ui/                        # Built extension files (auto-generated)
├── manifest.json              # Extension manifest
└── ...                        # Other extension files
```

## Command Reference

| Command                          | Description                                |
| -------------------------------- | ------------------------------------------ |
| `npm run dev`                    | Start development server                   |
| `npm run staging`                | Start staging server                       |
| `npm run prod`                   | Start production server                    |
| `npm run build`                  | Build for development                      |
| `npm run build:staging`          | Build for staging                          |
| `npm run build:prod`             | Build for production                       |
| `npm run build:executor`         | Build executor for development             |
| `npm run build:executor:staging` | Build executor for staging                 |
| `npm run build:executor:prod`    | Build executor for production              |
| `npm run dev-env`                | Full dev environment for development       |
| `npm run dev-env:staging`        | Full dev environment for staging           |
| `npm run dev-env:prod`           | Full dev environment for production        |
| `npm run dev-env-watch`          | Watch mode dev environment for development |
| `npm run dev-env-watch:staging`  | Watch mode dev environment for staging     |
| `npm run dev-env-watch:prod`     | Watch mode dev environment for production  |
| `npm run extension:open`         | Show extension loading instructions        |
