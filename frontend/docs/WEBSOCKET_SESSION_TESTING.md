# WebSocket Session Management Testing Guide

## Overview

This guide explains how to test the updated WebSocket session management system that properly handles user_id and conversation_id in localStorage.

## ✅ Key Improvements Made

### **1. Proper localStorage Management**

- **user_id**: Only stored/updated if not already present
- **conversation_id**: Only stored/updated if not already present
- **New Chat**: Properly removes conversation_id from localStorage while preserving user_id

### **2. Session ID Update Logic**

```javascript
// Only update if we don't already have the values
const shouldUpdateUserId = response.user_id && !currentState.user_id;
const shouldUpdateConversationId =
  response.conversation_id && !currentState.conversation_id;
```

### **3. Enhanced Logging**

- Clear console logs for session initialization
- localStorage operations are logged
- WebSocket session updates are logged

## 🧪 Testing Scenarios

### **Scenario 1: Fresh User (No localStorage)**

**Setup:**

```javascript
localStorage.removeItem("orderGPT_session");
```

**Expected Flow:**

1. Open app → No session data found
2. Send first message → WebSocket response provides user_id + conversation_id
3. Both IDs stored in localStorage and WebSocket store
4. Future messages use existing IDs

**Console Logs to Look For:**

```
No user session found in localStorage
Session: Received user_id from WebSocket: [user_id]
Session: Received conversation_id from WebSocket: [conversation_id]
Stored user_id in localStorage: [user_id]
Stored conversation_id in localStorage: [conversation_id] for zerodha chat
```

### **Scenario 2: Existing User, Same Tab**

**Setup:**

```javascript
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "existing-user-123",
    conversations: {
      zerodha: {
        chat: "existing-chat-456",
      },
    },
  })
);
```

**Expected Flow:**

1. Open app → Session loaded from localStorage
2. Send message → Uses existing IDs, no session updates
3. WebSocket response shouldn't update existing IDs

**Console Logs to Look For:**

```
Initializing session from localStorage: existing-user-123
Loading conversation_id from localStorage: existing-chat-456
// No "Session: Received" logs (IDs not updated)
```

### **Scenario 3: New Chat Functionality**

**Setup:** Start with existing session (Scenario 2)

**Expected Flow:**

1. Click "New Chat" button
2. conversation_id cleared from store and localStorage
3. user_id preserved
4. Send first message after new chat
5. WebSocket response provides new conversation_id
6. New conversation_id stored, user_id unchanged

**Console Logs to Look For:**

```
Cleared conversation_id from WebSocket store
Removed conversation_id from localStorage for zerodha chat
Session: Received conversation_id from WebSocket: [new_conversation_id]
Stored conversation_id in localStorage: [new_conversation_id] for zerodha chat
// No user_id updates (preserved)
```

### **Scenario 4: Tab Switching**

**Expected Flow:**

1. Switch from chat to orders tab
2. No session found for orders → conversation_id empty
3. Send first message in orders
4. New conversation_id for orders received and stored
5. Switch back to chat → original conversation_id loaded

**Console Logs to Look For:**

```
No conversation_id found for zerodha orders
Session: Received conversation_id from WebSocket: [orders_conversation_id]
Stored conversation_id in localStorage: [orders_conversation_id] for zerodha orders
```

## 📱 Manual Testing Commands

### **Check Current Session State**

```javascript
// Check localStorage
console.log(
  "localStorage:",
  JSON.parse(localStorage.getItem("orderGPT_session") || "{}")
);

// Check WebSocket store
import { useWebSocketStore } from "./src/stores/websocketStore";
const store = useWebSocketStore.getState();
console.log("WebSocket Store:", {
  user_id: store.user_id,
  conversation_id: store.conversation_id,
});
```

### **Clear Session for Testing**

```javascript
localStorage.removeItem("orderGPT_session");
// Refresh page
```

### **Set Up Test Session**

```javascript
localStorage.setItem(
  "orderGPT_session",
  JSON.stringify({
    user_id: "test-user-123",
    conversations: {
      zerodha: {
        chat: "test-chat-456",
        orders: "test-orders-789",
      },
    },
  })
);
// Refresh page
```

### **Test New Chat**

```javascript
// Click new chat button or call directly
import { handleNewChat } from "./src/utils/navigationManager";
handleNewChat().then((result) => console.log("New chat result:", result));
```

## ✅ Success Criteria

### **localStorage Behavior**

- [ ] user_id only set if not already present
- [ ] conversation_id only set if not already present
- [ ] New chat removes conversation_id but preserves user_id
- [ ] Tab-specific conversation_ids are maintained

### **WebSocket Store Behavior**

- [ ] Loads existing session on initialization
- [ ] Only updates IDs when they don't exist
- [ ] Properly clears conversation_id during new chat
- [ ] Maintains tab-specific conversation state

### **Message Flow**

- [ ] Messages send with correct user_id/conversation_id
- [ ] New users get IDs from first WebSocket response
- [ ] Existing users don't get unnecessary ID updates
- [ ] New chat starts with fresh conversation_id

## 🐛 Common Issues & Debugging

### **Issue: IDs keep getting overwritten**

**Debug**: Check console for "Session: Received" logs when they shouldn't appear
**Solution**: Verify the update logic is checking existing values

### **Issue: New chat doesn't work**

**Debug**: Check if conversation_id is properly cleared from both store and localStorage
**Console Command**:

```javascript
// After clicking new chat
console.log(
  "Store conversation_id:",
  useWebSocketStore.getState().conversation_id
);
console.log(
  "localStorage:",
  JSON.parse(localStorage.getItem("orderGPT_session"))
);
```

### **Issue: Session not loading on refresh**

**Debug**: Check initializeSession logs and localStorage data
**Console Command**:

```javascript
import { getSessionData } from "./src/utils/sessionManager";
console.log("Session data:", getSessionData());
```

### **Issue: Tab switching creates wrong sessions**

**Debug**: Verify tab-specific conversation_id logic
**Console Command**: Check the localStorage structure after switching tabs

## 📊 Expected localStorage Structure

### **Complete Session**

```json
{
  "user_id": "user-uuid-123",
  "conversations": {
    "zerodha": {
      "chat": "chat-uuid-456",
      "orders": "orders-uuid-789",
      "monitoring": "monitoring-uuid-012"
    },
    "groww": {
      "chat": "groww-chat-uuid-345"
    }
  }
}
```

### **After New Chat (Chat Tab)**

```json
{
  "user_id": "user-uuid-123",
  "conversations": {
    "zerodha": {
      // "chat": removed
      "orders": "orders-uuid-789",
      "monitoring": "monitoring-uuid-012"
    }
  }
}
```

## 🎯 Testing Checklist

- [ ] Fresh user gets both IDs from WebSocket
- [ ] Existing user loads IDs from localStorage
- [ ] Existing user doesn't get ID overwrites
- [ ] New chat clears conversation_id only
- [ ] New chat preserves user_id
- [ ] Tab switching maintains separate conversation_ids
- [ ] localStorage structure remains consistent
- [ ] Console logs are informative and accurate
- [ ] Session persists across page reloads
- [ ] Multiple broker support works correctly
