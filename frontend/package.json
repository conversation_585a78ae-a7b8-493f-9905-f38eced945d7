{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"lint": "eslint .", "preview": "vite preview", "dev": "npm run development", "development": "vite --mode development", "staging": "vite --mode staging", "prod": "npm run production", "production": "vite --mode production", "build": "npm run build:development", "build:dev": "npm run build:development", "build:development": "tsc -b && vite build --mode development", "build:staging": "tsc -b && vite build --mode staging", "build:prod": "npm run build:production", "build:production": "tsc -b && vite build --mode production", "build:executor": "npm run build:executor:development:patch", "build:executor:dev": "npm run build:executor:development:patch", "build:executor:development": "node scripts/build-executor.cjs development patch", "build:executor:staging": "node scripts/build-executor.cjs staging patch", "build:executor:prod": "npm run build:executor:production:patch", "build:executor:production": "node scripts/build-executor.cjs production patch", "build:executor:development:patch": "node scripts/build-executor.cjs development patch", "build:executor:development:minor": "node scripts/build-executor.cjs development minor", "build:executor:development:major": "node scripts/build-executor.cjs development major", "build:executor:staging:patch": "node scripts/build-executor.cjs staging patch", "build:executor:staging:minor": "node scripts/build-executor.cjs staging minor", "build:executor:staging:major": "node scripts/build-executor.cjs staging major", "build:executor:production:patch": "node scripts/build-executor.cjs production patch", "build:executor:production:minor": "node scripts/build-executor.cjs production minor", "build:executor:production:major": "node scripts/build-executor.cjs production major", "dev-env": "npm run dev-env:development", "dev-env:dev": "npm run dev-env:development", "dev-env:development": "node scripts/dev-full.cjs development", "dev-env:staging": "node scripts/dev-full.cjs staging", "dev-env:prod": "npm run dev-env:production", "dev-env:production": "node scripts/dev-full.cjs production", "dev-env-watch": "npm run dev-env-watch:development", "dev-env-watch:dev": "npm run dev-env-watch:development", "dev-env-watch:development": "node scripts/watch-and-build.cjs development", "dev-env-watch:staging": "node scripts/watch-and-build.cjs staging", "dev-env-watch:prod": "npm run dev-env-watch:production", "dev-env-watch:production": "node scripts/watch-and-build.cjs production", "extension:open": "node scripts/open-chrome-extension.cjs"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-radio-group": "^1.3.7", "firebase": "^12.0.0", "posthog-js": "^1.261.0", "pouchdb": "^7.3.0", "pouchdb-find": "^7.2.2", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-tabs": "^1.1.12", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/chrome": "^0.1.1", "@types/node": "^24.2.1", "@types/pouchdb": "^6.4.2", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "buffer": "^6.0.3", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "events": "^3.3.0", "global": "^4.4.0", "globals": "^16.2.0", "inherits": "^2.0.4", "postcss": "^8.5.6", "prettier": "^3.6.2", "process": "^0.11.10", "shadcn-ui": "^0.9.5", "tailwindcss": "^3.4.17", "terser": "^5.32.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "util": "^0.12.5", "vite": "^5.4.19", "vite-plugin-node-polyfills": "^0.24.0", "vitest": "^3.2.4", "zustand": "^5.0.6"}}