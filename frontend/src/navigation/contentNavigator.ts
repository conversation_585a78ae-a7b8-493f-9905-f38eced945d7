import { useSidebarStore, type <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../stores/sidebarStore";
import { useAuthStore } from "../stores/authStore";

// Result returned by each content navigation case
interface ContentNavResult {
  actions?: Array<() => void>;
  target: SidebarTab<PERSON>ey;
}

// Evaluate current sidebar tab and decide next based on conditions
export function getContentNavigationOutcome(): ContentNavResult | null {
  const sidebar = useSidebarStore.getState();
  const current = sidebar.activeTab;

  switch (current) {
    case "chat":
      // Chat tab - simplified UI navigation
      return {
        actions: [
          () =>
            console.warn(
              "Navigated to chat - session will be handled by WebSocket"
            ),
        ],
        target: "chat",
      };

    case "orders":
      // Orders tab - check for active orders only
      return {
        actions: [
          () => {
            console.warn("Navigated to orders");
            if (sidebar.activeOrders > 0) {
              console.warn("Active orders detected:", sidebar.activeOrders);
            }
          },
        ],
        target: "orders",
      };

    case "monitoring":
      // Monitoring tab - check for alerts only
      return {
        actions: [
          () => {
            console.warn("Navigated to monitoring");
            if (sidebar.monitoringAlerts > 0) {
              console.warn(
                "Monitoring alerts detected:",
                sidebar.monitoringAlerts
              );
            }
          },
        ],
        target: "monitoring",
      };

    case "notifications":
      // Notifications tab - fetch notifications and store in store
      return {
        actions: [
          async () => {
            try {
              console.warn("Fetching notifications...");
              const { authenticatedApiClient } = useAuthStore.getState();
              if (!authenticatedApiClient) {
                throw new Error("Authenticated API client not available");
              }
              const data = await authenticatedApiClient.getNotifications();
              const notifications = data.notifications || [];
              sidebar.setNotifications(notifications);
              console.warn("Notifications loaded:", notifications.length);
            } catch (error) {
              console.error("Failed to fetch notifications:", error);
              sidebar.setNotifications([]);
            }
          },
        ],
        target: "notifications",
      };

    case "profile":
      // Profile tab - fetch profile data and store in store
      return {
        actions: [
          async () => {
            try {
              console.warn("Fetching profile data...");
              const { authenticatedApiClient } = useAuthStore.getState();
              if (!authenticatedApiClient) {
                throw new Error("Authenticated API client not available");
              }
              const data = await authenticatedApiClient.getProfile();
              if (data.user && data.brokers) {
                sidebar.setProfileData(data);
                console.warn("Profile data loaded:", data.user.name);
              }
            } catch (error) {
              console.error("Failed to fetch profile data:", error);
              sidebar.setProfileData({
                user: {
                  userId: "user-123",
                  name: "Sushmita Swain",
                  email: "<EMAIL>",
                  phone: "9987548963",
                  avatar: "S",
                  joinDate: "January 2024",
                  tradingExperience: "Intermediate",
                  preferredMarkets: ["Stocks", "Options", "Futures"],
                },
                brokers: [],
              });
            }
          },
        ],
        target: "profile",
      };

    default:
      return { target: "chat" };
  }
}

// Switch to specific content tab
export function navigateToContent(tab: SidebarTabKey): void {
  const sidebar = useSidebarStore.getState();
  sidebar.setActiveTab(tab);

  // Execute actions specific to the new tab
  const outcome = getContentNavigationOutcome();
  if (outcome) {
    outcome.actions?.forEach((fn) => fn());
  }
}
