/* eslint-disable @typescript-eslint/no-explicit-any */
type WorkerMessage = {
  type:
    | "CONNECT"
    | "DISCONNECT"
    | "SEND_MESSAGE"
    | "NETWORK_STATUS_CHANGE"
    | "WAKE";
  payload: any;
};

type MainThreadMessage = {
  type: "CONNECTION_STATUS" | "WEBSOCKET_MESSAGE" | "ERROR";
  payload: any;
};

// Declare Vite-defined variables
declare const __WS_URL__: string;
declare const __WS_CHAT_ENDPOINT__: string;

// Read from Vite-defined variables
const wsUrl = __WS_URL__;
const wsEndpoint = __WS_CHAT_ENDPOINT__;

// Check if we're in mock mode - FORCE TO FALSE FOR DEBUGGING
const isMockMode = false; // Always use real WebSocket, never mock

class WebSocketWorker {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 3;
  private reconnectTimeout = 1000;
  private readonly maxReconnectDelayMs = 30000;
  private isReconnecting = false;
  private manualClose = false;
  private userId: string = "";
  private isConnected = false;
  private messageQueue: any[] = [];
  private lastMessageTime: number | null = null;
  private lastNetworkStatus: boolean | null = null;
  private currentTab: "chat" | "orders" | "monitoring" = "chat";
  private healthCheckIntervalId: number | null = null;

  constructor() {
    this.lastNetworkStatus = navigator.onLine;
    self.onmessage = this.handleMessage.bind(this);
    console.warn("🚀 [Worker] WebSocket Worker STARTED successfully!");
    console.warn("[Worker] Initialized with:", { wsUrl, wsEndpoint });
    console.warn("[Worker] Ready to receive messages from main thread");
  }

  private handleMessage(event: MessageEvent<WorkerMessage>) {
    const { type, payload } = event.data;
    console.warn("[Worker] Received message:", { type, payload });

    switch (type) {
      case "CONNECT":
        this.userId = payload.userId;
        console.warn("[Worker] Connecting with userId (cookie-auth)", {
          userId: this.userId,
        });

        // Reset reconnection state for a fresh connect attempt
        this.reconnectAttempts = 0;
        this.reconnectTimeout = 1000;
        this.isReconnecting = false;
        this.manualClose = false;
        this.connect();
        break;

      case "DISCONNECT":
        console.warn("[Worker] Disconnecting");
        this.disconnect();
        break;

      case "SEND_MESSAGE":
        console.warn("[Worker] Sending message:", payload);
        // Update current tab for error tracking
        if (payload.typeOfMessage) {
          this.currentTab = payload.typeOfMessage;
        }
        if (!this.isConnected) {
          console.warn("[Worker] Not connected, queueing message");
          this.messageQueue.push(payload);
          this.connect(); // Try to reconnect
        } else {
          this.sendMessage(payload);
        }
        break;

      case "NETWORK_STATUS_CHANGE":
        console.warn("[Worker] Network status changed:", payload.isOnline);

        // Only process if the status actually changed
        if (this.lastNetworkStatus === payload.isOnline) {
          console.warn("[Worker] Network status unchanged, skipping");
          return;
        }

        this.lastNetworkStatus = payload.isOnline;

        if (
          payload.isOnline &&
          !this.isConnected &&
          !this.isReconnecting &&
          this.ws?.readyState !== WebSocket.CONNECTING
        ) {
          console.warn(
            "[Worker] Network is back online, attempting to reconnect"
          );
          this.connect();
        } else if (!payload.isOnline) {
          console.warn("[Worker] Network is offline");
          this.isConnected = false;
        }
        break;
      case "WAKE":
        console.warn("[Worker] Received WAKE signal; ensuring connection...");
        // Reset reconnection guard to allow immediate attempt
        this.isReconnecting = false;
        // If not open, attempt to connect; if connecting, no-op; if open, optionally noop
        if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
          this.connect();
        }
        break;
    }
  }

  private connect() {
    // If in mock mode, don't attempt WebSocket connection
    if (isMockMode) {
      console.warn("[Worker] Mock mode enabled, skipping WebSocket connection");
      this.postMessage({
        type: "CONNECTION_STATUS",
        payload: { connected: false, mockMode: true },
      });
      return;
    }

    try {
      if (this.ws) {
        console.warn("[Worker] WebSocket already exists, checking state...");
        if (this.ws.readyState === WebSocket.OPEN && this.isConnected) {
          console.warn("[Worker] WebSocket already connected");
          return;
        }
        if (this.ws.readyState === WebSocket.CONNECTING) {
          console.warn("[Worker] WebSocket is already connecting, skipping");
          return;
        }
        // If not open, close it so we can create a new one
        this.ws.close();
        this.ws = null;
      }

      // Build WebSocket URL (cookie-only auth)
      const fullUrl = `${wsUrl}${wsEndpoint}`;

      console.warn("[Worker] Connecting to:", fullUrl);

      this.ws = new WebSocket(fullUrl);

      this.ws.onopen = () => {
        console.warn("[Worker] WebSocket connected");
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.reconnectTimeout = 1000;
        this.isReconnecting = false;
        this.manualClose = false;
        this.lastMessageTime = Date.now();

        // Start/refresh a lightweight health-check timer to detect stale sockets after sleep
        if (this.healthCheckIntervalId) {
          try {
            clearInterval(this.healthCheckIntervalId);
          } catch (_) {
            /* ignore cleanup errors */
          }
          this.healthCheckIntervalId = null;
        }
        // Do not send pings (server schema-specific). Just ensure reconnect if socket becomes non-OPEN silently.
        this.healthCheckIntervalId = setInterval(() => {
          try {
            if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
              console.warn(
                "[Worker] Health-check detected non-open socket; attempting reconnect"
              );
              this.connect();
            }
          } catch (e) {
            console.warn("[Worker] Health-check error:", e);
          }
        }, 10000) as unknown as number;

        this.postMessage({
          type: "CONNECTION_STATUS",
          payload: { connected: true },
        });

        // Process any queued messages
        while (this.messageQueue.length > 0) {
          const message = this.messageQueue.shift();
          if (message) {
            this.sendMessage(message);
          }
        }
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.warn("[Worker] Received WebSocket message:", data);
          this.lastMessageTime = Date.now();

          // Check if this is an error response from the backend
          if (data.error || data.status === "error") {
            console.warn("[Worker] Backend error detected:", data);
            this.postMessage({
              type: "ERROR",
              payload: {
                message: data.error || "Backend error occurred",
                details: data.details || data,
                tab: this.currentTab, // Include current tab for error routing
              },
            });
          } else {
            // Include the current tab information in the response
            this.postMessage({
              type: "WEBSOCKET_MESSAGE",
              payload: {
                ...data,
                typeOfMessage: this.currentTab,
              },
            });
          }
        } catch (error) {
          console.error("[Worker] Failed to parse message:", error);
          this.postMessage({
            type: "ERROR",
            payload: {
              message: "Failed to parse message",
              tab: this.currentTab,
            },
          });
        }
      };

      this.ws.onclose = (event) => {
        console.warn("[Worker] WebSocket closed:", event);
        this.isConnected = false;
        if (this.healthCheckIntervalId) {
          try {
            clearInterval(this.healthCheckIntervalId);
          } catch (_) {
            /* ignore cleanup errors */
          }
          this.healthCheckIntervalId = null;
        }

        this.postMessage({
          type: "CONNECTION_STATUS",
          payload: { connected: false, code: event.code },
        });

        // Attempt to reconnect unless we closed intentionally
        if (!this.manualClose && !this.isReconnecting) {
          this.attemptReconnect();
        }
      };

      this.ws.onerror = (error) => {
        console.error("[Worker] WebSocket error:", error);

        // Only send error if we're not already connected or connecting
        if (!this.isConnected && this.ws?.readyState !== WebSocket.OPEN) {
          this.isConnected = false;
          this.postMessage({
            type: "ERROR",
            payload: {
              message: "WebSocket connection failed",
              url: `${wsUrl}${wsEndpoint}`,
              timestamp: Date.now(),
              connectionAttempt: this.reconnectAttempts + 1,
              tab: this.currentTab,
              details: {
                readyState: this.ws?.readyState,
                readyStateText: this.ws
                  ? this.ws.readyState === WebSocket.CONNECTING
                    ? "CONNECTING"
                    : this.ws.readyState === WebSocket.OPEN
                      ? "OPEN"
                      : this.ws.readyState === WebSocket.CLOSING
                        ? "CLOSING"
                        : this.ws.readyState === WebSocket.CLOSED
                          ? "CLOSED"
                          : "UNKNOWN"
                  : "UNKNOWN",
                isOnline: navigator.onLine,
                userId: this.userId || "",
                lastMessageTime: this.lastMessageTime || null,
              },
            },
          });
        }
      };
    } catch (error) {
      console.error("[Worker] Connection error:", error);
      this.isConnected = false;
      this.postMessage({
        type: "ERROR",
        payload: {
          message:
            error instanceof Error
              ? error.message
              : "Connection error occurred",
          timestamp: Date.now(),
          tab: this.currentTab,
        },
      });
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn("[Worker] Max reconnection attempts reached");
      this.postMessage({
        type: "CONNECTION_STATUS",
        payload: {
          connected: false,
          message: "Max reconnection attempts reached",
          reason: "MAX_RECONNECTS",
          reconnectAttempts: this.reconnectAttempts,
        },
      });
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;

    console.warn(
      `[Worker] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`
    );
    this.postMessage({
      type: "CONNECTION_STATUS",
      payload: {
        connected: false,
        attempt: this.reconnectAttempts,
      },
    });

    const jitter = Math.floor(Math.random() * 250);
    const delay = Math.min(
      this.reconnectTimeout + jitter,
      this.maxReconnectDelayMs
    );
    setTimeout(() => {
      this.connect();
      this.reconnectTimeout = Math.min(
        this.reconnectTimeout * 2,
        this.maxReconnectDelayMs
      ); // Exponential backoff with cap
    }, delay);
  }

  private sendMessage(message: any) {
    if (!this.ws) {
      console.warn("[Worker] No WebSocket instance");
      this.messageQueue.push(message);
      this.connect();
      return;
    }

    if (!this.isConnected || this.ws.readyState !== WebSocket.OPEN) {
      console.warn(
        "[Worker] WebSocket not connected, readyState:",
        this.ws.readyState,
        "isConnected:",
        this.isConnected
      );

      // Queue the message
      console.warn("[Worker] Queueing message");
      this.messageQueue.push(message);

      // If we're CONNECTING, just wait for onopen
      if (this.ws.readyState === WebSocket.CONNECTING) {
        console.warn("[Worker] WebSocket is connecting, message queued");
        return;
      }

      // If CLOSED or CLOSING, try to reconnect
      if (this.ws.readyState >= WebSocket.CLOSING) {
        console.warn(
          "[Worker] WebSocket is closed/closing, attempting to reconnect..."
        );
        this.connect();
      }
      return;
    }

    try {
      console.warn("[Worker] Sending WebSocket message:", message);
      this.ws.send(JSON.stringify(message));
    } catch (error) {
      console.error("[Worker] Send error:", error);
      this.messageQueue.push(message);
      this.postMessage({
        type: "ERROR",
        payload: {
          message:
            error instanceof Error ? error.message : "Send error occurred",
          timestamp: Date.now(),
          tab: this.currentTab,
        },
      });
    }
  }

  private disconnect() {
    if (this.ws) {
      console.warn("[Worker] Closing WebSocket connection");
      this.manualClose = true;
      this.ws.close(1000, "Normal closure");
      this.ws = null;
    }
    this.isConnected = false;
    this.isReconnecting = false;
    this.userId = "";
    this.messageQueue = [];
    if (this.healthCheckIntervalId) {
      try {
        clearInterval(this.healthCheckIntervalId);
      } catch (_) {
        /* ignore cleanup errors */
      }
      this.healthCheckIntervalId = null;
    }
  }

  private postMessage(message: MainThreadMessage) {
    console.warn("[Worker] Posting message to main thread:", message);
    (self as any).postMessage(message);
  }
}

// Initialize the worker
new WebSocketWorker();

// Export empty object to make it a module
export {};
