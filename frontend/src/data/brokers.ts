import ZerodhaIcon from "../assets/zerodha.svg";
import GrowwIcon from "../assets/groww.svg";
import UpstoxIcon from "../assets/upstox.svg";

export interface Broker {
  id: string;
  name: string;
  icon: string;
  url: string;
}

export const brokers: Broker[] = [
  {
    id: "zerodha",
    name: "Zerodha",
    icon: ZerodhaIcon,
    url: "https://kite.zerodha.com",
  },
  {
    id: "groww",
    name: "Groww",
    icon: GrowwIcon,
    url: "https://groww.in",
  },
  {
    id: "upstox",
    name: "Upstox",
    icon: UpstoxIcon,
    url: "https://pro.upstox.com",
  },
];

export function getBrokerUrl(brokerId: string): string {
  const broker = brokers.find((b) => b.id === brokerId);
  return broker?.url || "https://kite.zerodha.com";
}

export function getBrokerById(brokerId: string): Broker | undefined {
  return brokers.find((b) => b.id === brokerId);
}
