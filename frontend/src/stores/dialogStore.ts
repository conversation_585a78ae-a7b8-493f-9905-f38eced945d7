import { create } from "zustand";

export type DialogPageKey = "all-monitoring" | "all-orders" | "edit-profile";

export interface MonitoringAlert {
  id: string;
  description: string;
  symbol: string;
  triggerPrice: string;
  currentPrice: string;
  progress: string;
  progressPercent: number;
  status: "pending" | "inProgress" | "triggered" | "stopped" | (string & {});
  orderType: string;
  stopLoss: string;
  product: string;
  // Structured fields for UI (avoid parsing description)
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  conditionOperator?: string;
  conditionValue?: string | number;
  limitPrice?: string;
}

export interface Order {
  id: string;
  symbol: string;
  type: "buy" | "sell";
  quantity: number;
  price: string;
  status: "pending" | "inProgress" | "executed" | "cancelled" | (string & {});
  broker_status?: string;
  timestamp: string;
  orderType: string;
  product: string;
  broker: string;
  primitive?: string; // NEW: display action/primitive label (e.g., BUY, SELL, MONITOR)
  triggerPrice?: string | number;
  limitPrice?: string | number;
  execution_details?: Record<string, string>; // NEW: detailed execution info
}

export interface DialogState {
  // Dialog visibility and current page
  isDialogOpen: boolean;
  currentPage: DialogPageKey | null;
  dialogTitle: string;

  // Dialog navigation
  setDialogOpen: (open: boolean) => void;
  setCurrentPage: (page: DialogPageKey) => void;
  setDialogTitle: (title: string) => void;

  // Data states
  monitoringAlerts: MonitoringAlert[];
  orders: Order[];
  isLoading: boolean;

  // Data actions
  setMonitoringAlerts: (alerts: MonitoringAlert[]) => void;
  setOrders: (orders: Order[]) => void;
  setLoading: (loading: boolean) => void;

  // Monitoring-specific actions
  stopAlert: (id: string) => void;

  // Combined actions
  openDialog: (page: DialogPageKey) => void;
  closeDialog: () => void;
}

const getDialogTitle = (page: DialogPageKey): string => {
  switch (page) {
    case "all-monitoring":
      return "Market monitors";
    case "all-orders":
      return "All Orders";
    case "edit-profile":
      return "Edit Profile";
    default:
      return "";
  }
};

export const useDialogStore = create<DialogState>((set, get) => ({
  // Dialog state
  isDialogOpen: false,
  currentPage: null,
  dialogTitle: "",

  // Data state
  monitoringAlerts: [],
  orders: [],
  isLoading: false,

  // Dialog actions
  setDialogOpen: (open: boolean) => set({ isDialogOpen: open }),
  setCurrentPage: (page: DialogPageKey) =>
    set({
      currentPage: page,
      dialogTitle: getDialogTitle(page),
    }),
  setDialogTitle: (title: string) => set({ dialogTitle: title }),

  // Data actions
  setMonitoringAlerts: (alerts: MonitoringAlert[]) =>
    set({ monitoringAlerts: alerts }),
  setOrders: (orders: Order[]) => set({ orders }),
  setLoading: (loading: boolean) => set({ isLoading: loading }),

  // Monitoring actions
  stopAlert: (id: string) => {
    const { monitoringAlerts } = get();
    const updatedAlerts = monitoringAlerts.map((alert) =>
      alert.id === id ? { ...alert, status: "stopped" as const } : alert
    );
    set({ monitoringAlerts: updatedAlerts });
  },

  // Combined actions
  openDialog: (page: DialogPageKey) => {
    set({
      isDialogOpen: true,
      currentPage: page,
      dialogTitle: getDialogTitle(page),
    });
  },

  closeDialog: () => {
    set({
      isDialogOpen: false,
      currentPage: null,
      dialogTitle: "",
    });
  },
}));
