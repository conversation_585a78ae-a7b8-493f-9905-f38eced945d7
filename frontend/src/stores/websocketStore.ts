/* eslint-disable @typescript-eslint/no-explicit-any */
import { create } from "zustand";
import { getCurrentBroker } from "../utils/sessionManager";

// Message types
export interface WebSocketResponse {
  user_id: string;
  conversation_id: string;
  textMessage: string; // Backend sends textMessage, not message
  messageType:
    | "order_confirmation"
    | "monitor_order"
    | "chat_response"
    | "order_planning"
    | "order_execution";
  primitives?: PrimitiveType[];
  actions?: Array<{
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }>;
  sender: "user" | "system";
  typeOfMessage?: "chat" | "orders" | "monitoring"; // Optional since backend might not send this
  // Execution-progress augmentation (UI-only)
  executionOrders?: ExecutionOrderStatus[];
  executionCompleted?: boolean;
  // Optional group fields when present (plan confirmation etc.)
  group_human_friendly_explanation?: string;
  group_clarification_message?: string;
}

export interface MessageHistoryData {
  sender: "user";
  message?: string;
  textMessage?: string;
}

export interface MessageHistory {
  id: string;
  timestamp: number;
  data: WebSocketResponse | MessageHistoryData;
}

export interface PrimitiveType {
  action?: string;
  arguments?: Record<string, unknown>;
  human_friendly_explanation?: string;
  need_more_info?: string[];
  clarification?: string;
}

// Minimal order status shape for execution-progress rendering
export interface ExecutionOrderStatus {
  id: string;
  symbol: string;
  status: string;
  quantity?: number;
  price?: string;
  orderType?: string;
  product?: string;
  broker?: string;
}

interface WebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  user_id: string;
  conversation_id: string | null;
  selectedModelId: string;
  chatMessages: MessageHistory[];
  orderMessages: MessageHistory[];
  monitoringMessages: MessageHistory[];
  chatQueue: WebSocketResponse[];
  orderQueue: WebSocketResponse[];
  monitoringQueue: WebSocketResponse[];
  connectionStatus: "disconnected" | "connecting" | "connected" | "error";
  initializedTabs: Record<string, boolean>; // Track which tabs have been initialized
  waitingForResponse: "chat" | "orders" | "monitoring" | null; // Track which tab is waiting for response
  brokerLoginRequired: boolean; // Global broker login requirement flag
  // Chat input blocking during order execution
  chatInputBlocked: boolean;
  // Lightweight registry for execution bubbles: execId -> { monitoringMessageId?, ordersMessageId? }
  executionMessageRegistry: Record<
    string,
    { monitoringMessageId?: string; ordersMessageId?: string }
  >;
}

interface WebSocketActions {
  setConnectionStatus: (status: boolean) => void;
  setError: (error: string | null) => void;
  addMessage: (
    message: MessageHistory | WebSocketResponse,
    typeOfMessage: "chat" | "orders" | "monitoring",
    shouldOverwrite?: boolean
  ) => string; // return the message id
  initializeSession: () => void;
  updateSessionForTab: (
    typeOfMessage: "chat" | "orders" | "monitoring"
  ) => void;
  clearMessages: (typeOfMessage: "chat" | "orders" | "monitoring") => void;
  clearCurrentTabMessages: (
    typeOfMessage: "chat" | "orders" | "monitoring"
  ) => void;
  setSelectedModel: (modelId: string) => void;
  markTabAsInitialized: (tab: "chat" | "orders" | "monitoring") => void;
  isTabInitialized: (tab: "chat" | "orders" | "monitoring") => boolean;
  setWaitingForResponse: (tab: "chat" | "orders" | "monitoring" | null) => void;
  isWaitingForResponse: (tab: "chat" | "orders" | "monitoring") => boolean;
  setUserId: (userId: string) => void;
  setConversationId: (conversationId: string) => void;
  setBrokerLoginRequired: (required: boolean) => void;
  setChatInputBlocked: (blocked: boolean) => void;
  updateMessageById: (
    typeOfMessage: "chat" | "orders" | "monitoring",
    messageId: string,
    updater: (oldData: WebSocketResponse | MessageHistoryData) => any
  ) => void;
  registerExecutionMessage: (
    execId: string,
    entry: { monitoringMessageId?: string; ordersMessageId?: string }
  ) => void;
}

export const useWebSocketStore = create<WebSocketState & WebSocketActions>(
  (set, get) => ({
    // State - keeping all of these since useWebSocket needs them
    isConnected: false,
    isConnecting: false,
    error: null,
    user_id: localStorage.getItem("user_id") || "",
    conversation_id: (() => {
      const currentBroker = getCurrentBroker();
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      return conversations?.[currentBroker]?.chat || ""; // Default to chat type
    })(),
    selectedModelId: "mock-llm-v1",
    chatMessages: [],
    orderMessages: [],
    monitoringMessages: [],
    chatQueue: [],
    orderQueue: [],
    monitoringQueue: [],
    connectionStatus: "disconnected",
    initializedTabs: {},
    waitingForResponse: null,
    brokerLoginRequired: false,
    chatInputBlocked: false,
    executionMessageRegistry: {},

    // Actions
    setConnectionStatus: (status: boolean) => {
      console.warn("Setting connection status:", status);
      set({
        isConnected: status,
        isConnecting: false,
        connectionStatus: status ? "connected" : "disconnected",
      });
    },

    setError: (error: string | null) => {
      console.warn("Setting error:", error);
      set({
        error,
        // Set connection status to error if there's an error, otherwise keep current status
        connectionStatus: error
          ? "error"
          : get().isConnected
            ? "connected"
            : "disconnected",
      });
    },

    addMessage: (
      message: any,
      typeOfMessage: "chat" | "orders" | "monitoring",
      shouldOverwrite: boolean = false
    ) => {
      console.warn(`Adding ${typeOfMessage} message:`, message);

      // If message is already in the correct format, use it directly
      if (message.id && message.timestamp && message.data) {
        switch (typeOfMessage) {
          case "chat":
            set((state) => ({
              chatMessages: shouldOverwrite
                ? [message]
                : [...state.chatMessages, message],
            }));
            break;
          case "orders":
            set((state) => ({
              orderMessages: shouldOverwrite
                ? [message]
                : [...state.orderMessages, message],
            }));
            break;
          case "monitoring":
            set((state) => ({
              monitoringMessages: shouldOverwrite
                ? [message]
                : [...state.monitoringMessages, message],
            }));
            break;
        }

        // Clear waiting state when system message arrives
        if (message.data?.sender === "system") {
          set({ waitingForResponse: null });
        }
        return message.id as string;
      }

      // Otherwise, wrap it in the MessageHistory format
      const messageWithId: MessageHistory = {
        id: Date.now().toString() + Math.random(),
        timestamp: Date.now(),
        data: message,
      };

      switch (typeOfMessage) {
        case "chat":
          set((state) => ({
            chatMessages: shouldOverwrite
              ? [messageWithId]
              : [...state.chatMessages, messageWithId],
          }));
          break;
        case "orders":
          set((state) => ({
            orderMessages: shouldOverwrite
              ? [messageWithId]
              : [...state.orderMessages, messageWithId],
          }));
          break;
        case "monitoring":
          set((state) => ({
            monitoringMessages: shouldOverwrite
              ? [messageWithId]
              : [...state.monitoringMessages, messageWithId],
          }));
          break;
      }

      // Clear waiting state when system message arrives
      if (messageWithId.data?.sender === "system") {
        set({ waitingForResponse: null });
      }
      return messageWithId.id;
    },

    clearMessages: (typeOfMessage: "chat" | "orders" | "monitoring") => {
      switch (typeOfMessage) {
        case "chat":
          set({ chatMessages: [] });
          break;
        case "orders":
          set({ orderMessages: [] });
          break;
        case "monitoring":
          set({ monitoringMessages: [] });
          break;
      }
    },

    clearCurrentTabMessages: (
      typeOfMessage: "chat" | "orders" | "monitoring"
    ) => {
      // Clear messages for the current tab
      switch (typeOfMessage) {
        case "chat":
          set({ chatMessages: [], chatQueue: [] });
          break;
        case "orders":
          set({ orderMessages: [], orderQueue: [] });
          break;
        case "monitoring":
          set({ monitoringMessages: [], monitoringQueue: [] });
          break;
      }

      // Clear conversation_id from nested structure
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      const currentBroker = getCurrentBroker();

      if (conversations[currentBroker]) {
        // Remove just this type's conversation_id
        delete conversations[currentBroker][typeOfMessage];

        // If broker object is now empty, remove it too
        if (Object.keys(conversations[currentBroker]).length === 0) {
          delete conversations[currentBroker];
        }

        // Save the updated conversations back to localStorage
        localStorage.setItem("conversations", JSON.stringify(conversations));
      }

      // Update store state
      set({ conversation_id: "" });
    },

    setSelectedModel: (modelId: string) => {
      set({ selectedModelId: modelId });
    },

    markTabAsInitialized: (tab: "chat" | "orders" | "monitoring") => {
      set((state) => ({
        initializedTabs: {
          ...state.initializedTabs,
          [tab]: true,
        },
      }));
    },

    isTabInitialized: (tab: "chat" | "orders" | "monitoring") => {
      return get().initializedTabs[tab] || false;
    },

    setWaitingForResponse: (tab: "chat" | "orders" | "monitoring" | null) => {
      set({ waitingForResponse: tab });
    },

    isWaitingForResponse: (tab: "chat" | "orders" | "monitoring") => {
      return get().waitingForResponse === tab;
    },

    initializeSession: () => {
      console.warn("Initializing session");
      const user_id = localStorage.getItem("user_id") || "";
      const currentBroker = getCurrentBroker();

      // Get conversation_id from nested structure
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};
      const conversation_id = conversations?.[currentBroker]?.chat || ""; // Default to chat type

      // Only update store state, localStorage is handled by useWebSocket
      set({ user_id, conversation_id });
    },

    updateSessionForTab: (typeOfMessage: "chat" | "orders" | "monitoring") => {
      console.warn("Updating session for tab:", typeOfMessage);
      const currentBroker = getCurrentBroker();

      // Get current conversations structure
      const conversationsStr = localStorage.getItem("conversations");
      const conversations = conversationsStr
        ? JSON.parse(conversationsStr)
        : {};

      // Get conversation_id from nested structure
      const conversation_id =
        conversations?.[currentBroker]?.[typeOfMessage] || "";

      // Only update store state, localStorage is handled by useWebSocket
      set({ conversation_id });
    },

    // 🔧 FIX: Add actions to immediately update user_id and conversation_id
    setUserId: (userId: string) => {
      console.warn("Setting user_id in WebSocket store:", userId);
      set({ user_id: userId });
    },

    setConversationId: (conversationId: string) => {
      console.warn(
        "Setting conversation_id in WebSocket store:",
        conversationId
      );
      set({ conversation_id: conversationId });
    },

    setBrokerLoginRequired: (required: boolean) => {
      set({ brokerLoginRequired: !!required });
    },

    setChatInputBlocked: (blocked: boolean) => {
      set({ chatInputBlocked: !!blocked });
    },

    updateMessageById: (
      typeOfMessage: "chat" | "orders" | "monitoring",
      messageId: string,
      updater: (oldData: WebSocketResponse | MessageHistoryData) => any
    ) => {
      const updateArray = (arr: MessageHistory[]) =>
        arr.map((msg) =>
          msg.id === messageId ? { ...msg, data: updater(msg.data) } : msg
        );

      switch (typeOfMessage) {
        case "chat":
          set((state) => ({ chatMessages: updateArray(state.chatMessages) }));
          break;
        case "orders":
          set((state) => ({ orderMessages: updateArray(state.orderMessages) }));
          break;
        case "monitoring":
          set((state) => ({
            monitoringMessages: updateArray(state.monitoringMessages),
          }));
          break;
      }
    },
    registerExecutionMessage: (execId, entry) => {
      set((state) => ({
        executionMessageRegistry: {
          ...state.executionMessageRegistry,
          [execId]: {
            ...(state.executionMessageRegistry[execId] || {}),
            ...entry,
          },
        },
      }));
    },
  })
);
