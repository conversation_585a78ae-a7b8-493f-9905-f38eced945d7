import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  type User,
  onAuthStateChanged,
  signOut as firebaseSignOut,
  getIdToken,
  RecaptchaVerifier,
  signInWithPhoneNumber,
  type ConfirmationResult,
  getAuth,
} from "firebase/auth";
import { auth } from "../services/firebase";
import { AuthService } from "../services/authService";
import { createAuthenticatedApiClient } from "../utils/apiClient";
import { isRunningInExtension } from "../utils/extensionDetector";

// Global flag to prevent multiple RecaptchaVerifier creation
let isCreatingRecaptcha = false;

// Helper function to clean up reCAPTCHA DOM elements
const cleanupRecaptchaElements = () => {
  const container = document.getElementById("recaptcha-container");
  if (container) {
    container.innerHTML = "";
  }

  // Remove any existing reCAPTCHA elements from the DOM
  const existingRecaptchaElements = document.querySelectorAll(
    '[id*="recaptcha"], [class*="recaptcha"], iframe[src*="recaptcha"]'
  );
  existingRecaptchaElements.forEach((element) => {
    try {
      if (element.id !== "recaptcha-container") {
        element.remove();
      }
    } catch (e) {
      console.warn("Could not remove existing reCAPTCHA element:", e);
    }
  });
};

export interface AuthUser {
  uid: string;
  email?: string | null;
  phoneNumber?: string | null;
  emailVerified: boolean;
  displayName?: string | null;
}

interface AuthState {
  // Auth state
  user: AuthUser | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  preventAutoRedirect: boolean; // Flag to prevent automatic redirect to ChatHomePage (for web flow)

  // Firebase specific
  firebaseUser: User | null;
  idToken: string | null;

  // User profile data (from our database)
  userProfile: {
    name?: string;
    phone?: string;
  } | null;
  userDbDetails: {
    couchdb_url: string;
    couchdb_user: string;
    couchdb_password: string;
    couchdb_database: string;
  } | null;

  // Connection state
  connectionAttempts: number;
  maxConnectionAttempts: number;

  // Phone auth state
  recaptchaVerifier: RecaptchaVerifier | null;
  confirmationResult: ConfirmationResult | null;

  // Login progress state
  isLoginInProgress: boolean;
  loginTabId: number | null;

  // Authenticated API client (created with signOut callback)
  authenticatedApiClient: ReturnType<
    typeof createAuthenticatedApiClient
  > | null;

  // Actions
  setUser: (user: User | null) => void;
  setIdToken: (token: string | null) => void;
  setUserProfile: (profile: { name?: string; phone?: string } | null) => void;
  setUserDbDetails: (
    dbDetails: {
      couchdb_url: string;
      couchdb_user: string;
      couchdb_password: string;
      couchdb_database: string;
    } | null
  ) => void;
  setLoading: (loading: boolean) => void;
  setAuthenticated: (authenticated: boolean) => void;
  setPreventAutoRedirect: (prevent: boolean) => void;
  incrementConnectionAttempts: () => void;
  resetConnectionAttempts: () => void;
  signOut: () => Promise<void>;
  refreshToken: () => Promise<string | null>;

  // Phone authentication
  setupRecaptcha: () => void;
  sendOTP: (phoneNumber: string) => Promise<void>;
  sendOTPWithoutRecaptcha: (phoneNumber: string) => Promise<void>;
  verifyOTP: (code: string) => Promise<User>;
  cleanupRecaptcha: () => void;

  // Login progress actions
  setLoginInProgress: (inProgress: boolean) => void;
  setLoginTabId: (tabId: number | null) => void;
  clearLoginProgress: () => void;

  // Initialization
  initialize: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      isAuthenticated: false,
      isLoading: true,
      preventAutoRedirect: false,
      firebaseUser: null,
      idToken: null,
      userProfile: null,
      userDbDetails: null,
      connectionAttempts: 0,
      maxConnectionAttempts: 3,
      recaptchaVerifier: null,
      confirmationResult: null,
      isLoginInProgress: false,
      loginTabId: null,
      authenticatedApiClient: null,

      // Actions
      setUser: (firebaseUser: User | null) => {
        if (firebaseUser) {
          const authUser: AuthUser = {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            phoneNumber: firebaseUser.phoneNumber,
            emailVerified: firebaseUser.emailVerified,
            displayName: firebaseUser.displayName,
          };

          const currentState = get();
          const shouldPreventAuth = currentState.preventAutoRedirect;

          if (shouldPreventAuth) {
            set({
              user: authUser,
              firebaseUser,
              isAuthenticated: false,
              isLoading: false,
            });
          } else {
            set({
              user: authUser,
              firebaseUser,
              isAuthenticated: true,
              isLoading: false,
            });
          }

          if (!isRunningInExtension()) {
            // Web context: mint/refresh session cookie
            getAuth()
              .currentUser?.getIdToken()
              .then((token) => {
                get().setIdToken(token);
                if (token) {
                  AuthService.sessionLogin(token).catch((e) =>
                    console.warn("sessionLogin failed:", e)
                  );
                }
              })
              .catch((error) => {
                console.error("Failed to get initial ID token:", error);
              });
          } else {
            // Extension context: also mint server session cookie using current Firebase user
            getAuth()
              .currentUser?.getIdToken()
              .then((token) => {
                get().setIdToken(token);
                if (token) {
                  AuthService.sessionLogin(token).catch((e) =>
                    console.warn("sessionLogin failed:", e)
                  );
                } else {
                  console.warn(
                    "[authStore] No ID token available in extension context"
                  );
                }
              })
              .catch((error) => {
                console.error(
                  "[authStore] Failed to get ID token in extension:",
                  error
                );
              });
          }
        } else {
          const currentState = get();
          const wasAuthenticated = currentState.isAuthenticated;
          if (wasAuthenticated && currentState.user) {
            set({
              firebaseUser: null,
              isLoading: false,
            });
          } else {
            set({
              user: null,
              firebaseUser: null,
              isAuthenticated: false,
              isLoading: false,
              idToken: null,
              userProfile: null,
              userDbDetails: null,
            });
          }
        }
      },

      setIdToken: (token: string | null) => {
        set({ idToken: token });
      },

      setUserProfile: (profile: { name?: string; phone?: string } | null) => {
        set({ userProfile: profile });
      },

      setUserDbDetails: (
        dbDetails: {
          couchdb_url: string;
          couchdb_user: string;
          couchdb_password: string;
          couchdb_database: string;
        } | null
      ) => {
        set({ userDbDetails: dbDetails });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setAuthenticated: (authenticated: boolean) => {
        set({ isAuthenticated: authenticated });
      },

      setPreventAutoRedirect: (prevent: boolean) => {
        set({ preventAutoRedirect: prevent });
      },

      incrementConnectionAttempts: () => {
        set((state) => ({
          connectionAttempts: state.connectionAttempts + 1,
        }));
      },

      resetConnectionAttempts: () => {
        set({ connectionAttempts: 0 });
      },

      signOut: async () => {
        try {
          // Best-effort server-side logout to clear session cookie
          try {
            await AuthService.sessionLogout();
          } catch (e) {
            console.warn("sessionLogout failed:", e);
          }
          await firebaseSignOut(auth);
          set({
            user: null,
            firebaseUser: null,
            isAuthenticated: false,
            idToken: null,
            userProfile: null,
            userDbDetails: null,
            connectionAttempts: 0,
          });
        } catch (error) {
          console.error("Sign out error:", error);
          throw error;
        }
      },

      refreshToken: async (): Promise<string | null> => {
        try {
          if (isRunningInExtension()) {
            console.warn("[authStore] Cannot refresh token in extension.");
            return null;
          }
          const { firebaseUser } = get();
          if (!firebaseUser) {
            return null;
          }

          // Force refresh the token
          const token = await getIdToken(firebaseUser, true);
          get().setIdToken(token);
          return token;
        } catch (error) {
          console.error("Token refresh error:", error);
          return null;
        }
      },

      // Phone authentication methods
      setupRecaptcha: () => {
        try {
          // Check if we already have a valid reCAPTCHA verifier
          const { recaptchaVerifier: existingVerifier } = get();
          if (existingVerifier) {
            console.warn("reCAPTCHA verifier already exists, reusing it");
            return; // Don't create a new one
          }

          // Check if we're already in the process of creating one
          if (isCreatingRecaptcha) {
            console.warn("reCAPTCHA creation already in progress, waiting...");
            return;
          }

          // Set the global flag
          isCreatingRecaptcha = true;

          // Clean up any existing reCAPTCHA instance more thoroughly
          cleanupRecaptchaElements();

          // Wait a bit to ensure DOM cleanup is complete
          setTimeout(() => {
            try {
              const recaptchaVerifier = new RecaptchaVerifier(
                auth,
                "recaptcha-container",
                {
                  size: "invisible",
                  callback: () => {
                    console.warn("reCAPTCHA solved");
                    // reCAPTCHA solved - Firebase will handle cleanup
                    console.warn(
                      "reCAPTCHA solved - cleanup handled by Firebase"
                    );
                  },
                  "expired-callback": () => {
                    console.warn("reCAPTCHA expired");
                  },
                  "error-callback": (error: unknown) => {
                    console.warn("reCAPTCHA error:", error);
                    // Firebase will handle cleanup on error
                  },
                }
              );

              // Don't watch for changes - Firebase will handle showing reCAPTCHA
              // The container will be shown by Firebase when needed

              set({ recaptchaVerifier });
              console.warn("reCAPTCHA verifier created successfully");
            } catch (error) {
              console.error("Error creating reCAPTCHA verifier:", error);
              // If it's a DUPE error, try to clear and retry once
              if (error instanceof Error && error.message.includes("DUPE")) {
                console.warn(
                  "DUPE error detected, attempting cleanup and retry"
                );
                get().cleanupRecaptcha();
                // Don't retry immediately to avoid infinite loops
                throw new Error(
                  "reCAPTCHA verifier already exists. Please try again in a moment."
                );
              }
              throw error;
            } finally {
              // Always reset the flag
              isCreatingRecaptcha = false;
            }
          }, 100); // Small delay to ensure DOM cleanup
        } catch (error) {
          console.error("Error setting up reCAPTCHA:", error);
          // Reset the flag on error
          isCreatingRecaptcha = false;
          throw error;
        }
      },

      sendOTP: async (phoneNumber: string): Promise<void> => {
        try {
          const { recaptchaVerifier } = get();
          if (!recaptchaVerifier) {
            throw new Error("reCAPTCHA not initialized");
          }

          const confirmationResult = await signInWithPhoneNumber(
            auth,
            phoneNumber,
            recaptchaVerifier
          );

          set({ confirmationResult });
          console.warn("OTP sent successfully");
        } catch (error) {
          console.error("Error sending OTP:", error);
          throw error;
        }
      },

      sendOTPWithoutRecaptcha: async (phoneNumber: string): Promise<void> => {
        try {
          // Setup invisible reCAPTCHA automatically if needed
          const actions = get();
          if (!actions.recaptchaVerifier) {
            console.warn("No reCAPTCHA verifier found, setting up one");
            actions.setupRecaptcha();

            // Wait a bit for the async setup to complete
            await new Promise((resolve) => setTimeout(resolve, 200));
          }

          const { recaptchaVerifier } = get();
          if (!recaptchaVerifier) {
            throw new Error("Failed to initialize reCAPTCHA");
          }

          const confirmationResult = await signInWithPhoneNumber(
            auth,
            phoneNumber,
            recaptchaVerifier
          );

          set({ confirmationResult });
          console.warn("OTP sent successfully without manual reCAPTCHA setup");
        } catch (error) {
          console.error("Error sending OTP:", error);
          // If it's a DUPE error, provide a more helpful message
          if (error instanceof Error && error.message.includes("DUPE")) {
            throw new Error(
              "reCAPTCHA verification failed. Please refresh the page and try again."
            );
          }
          throw error;
        }
      },

      verifyOTP: async (code: string): Promise<User> => {
        try {
          const { confirmationResult } = get();
          if (!confirmationResult) {
            throw new Error("No confirmation result available");
          }

          const result = await confirmationResult.confirm(code);
          const user = result.user;

          if (!user) {
            throw new Error("No user returned from confirmation");
          }

          console.warn("OTP verified successfully");
          return user;
        } catch (error) {
          console.error("Error verifying OTP:", error);
          throw error;
        }
      },

      cleanupRecaptcha: () => {
        const { recaptchaVerifier } = get();
        if (recaptchaVerifier) {
          try {
            recaptchaVerifier.clear();
            console.warn("reCAPTCHA verifier cleared successfully");
          } catch (error) {
            console.error("Error clearing reCAPTCHA:", error);
          }
        }

        // Clean up DOM elements more thoroughly
        cleanupRecaptchaElements();

        console.warn("Cleaning up reCAPTCHA - DOM elements removed");

        // Reset the global creation flag
        isCreatingRecaptcha = false;

        set({ recaptchaVerifier: null, confirmationResult: null });
      },

      // Login progress actions
      setLoginInProgress: (inProgress: boolean) => {
        set({ isLoginInProgress: inProgress });
      },
      setLoginTabId: (tabId: number | null) => {
        set({ loginTabId: tabId });
      },
      clearLoginProgress: () => {
        set({ isLoginInProgress: false, loginTabId: null });
      },

      initialize: () => {
        // Create authenticated API client with signOut callback
        const authenticatedApiClient = createAuthenticatedApiClient(() =>
          get().signOut()
        );
        set({ authenticatedApiClient });

        // Set up Firebase auth state listener
        const unsubscribe = onAuthStateChanged(auth, (user) => {
          console.warn(
            "Firebase auth state changed:",
            user?.uid || "signed out"
          );
          get().setUser(user);
        });

        // Set up token refresh interval (refresh every 50 minutes)
        const tokenRefreshInterval = setInterval(
          async () => {
            const { isAuthenticated } = get();
            if (isAuthenticated) {
              try {
                await get().refreshToken();
                console.warn("Token refreshed automatically");
              } catch (error) {
                console.error("Automatic token refresh failed:", error);
              }
            }
          },
          50 * 60 * 1000
        ); // 50 minutes

        // Cleanup function (not used in this implementation but good practice)
        return () => {
          unsubscribe();
          clearInterval(tokenRefreshInterval);
        };
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        userProfile: state.userProfile,
        userDbDetails: state.userDbDetails,
        // Don't persist authenticatedApiClient - it will be recreated on init
      }),
    }
  )
);

// Initialize auth store on module load
if (typeof window !== "undefined") {
  useAuthStore.getState().initialize();
}

// Utility functions
// No public token getter required in cookie-only mode.

export const isTokenExpired = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp < currentTime;
  } catch (error) {
    console.error("Failed to parse token:", error);
    return true;
  }
};

export const shouldRefreshToken = (token: string): boolean => {
  try {
    const payload = JSON.parse(atob(token.split(".")[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    return payload.exp - currentTime < 300;
  } catch (error) {
    console.error("Failed to parse token:", error);
    return true;
  }
};
