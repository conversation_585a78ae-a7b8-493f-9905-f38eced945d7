import { create } from "zustand";

export type SidebarTabKey =
  | "chat"
  | "orders"
  | "monitoring"
  | "notifications"
  | "profile";

export interface Notification {
  id: string;
  variant: "positive" | "negative";
  title: string;
  time: string;
  description: string;
  actionText: string;
  isRead: boolean;
}

export interface User {
  userId: string;
  name: string;
  email: string;
  phone: string;
  avatar: string;
  joinDate: string;
  tradingExperience: string;
  preferredMarkets: string[];
}

export interface Broker {
  id: string;
  name: string;
  logo: string;
  isConnected: boolean;
  description: string;
}

export interface ProfileData {
  user: User;
  brokers: Broker[];
}

interface SidebarState {
  activeTab: SidebarTabKey;
  setActiveTab: (tab: SidebarTabKey) => void;
  // Future: Could add tab-specific state here
  unreadNotifications: number;
  activeOrders: number;
  monitoringAlerts: number; // new monitoring count since last seen
  notifications: Notification[];
  setNotifications: (notifications: Notification[]) => void;
  clearNotifications: () => void;

  // Profile data
  profileData: ProfileData | null;
  setProfileData: (profileData: ProfileData) => void;

  // Monitoring new-count tracking
  lastSeenMonitoringAt: number | null;
  setLastSeenMonitoringAt: (ts: number) => void;
  setMonitoringAlertsCount: (count: number) => void;
}

export const useSidebarStore = create<SidebarState>((set) => ({
  activeTab: "chat",
  unreadNotifications: 5,
  activeOrders: 3,
  monitoringAlerts: 0,
  notifications: [],
  profileData: null,
  lastSeenMonitoringAt: null,

  setActiveTab: (tab: SidebarTabKey) => set({ activeTab: tab }),
  setNotifications: (notifications: Notification[]) => {
    const unreadCount = notifications.filter((n) => !n.isRead).length;
    set({ notifications, unreadNotifications: unreadCount });
  },
  clearNotifications: () => set({ unreadNotifications: 0 }),
  setProfileData: (profileData: ProfileData) => set({ profileData }),

  setLastSeenMonitoringAt: (ts: number) => set({ lastSeenMonitoringAt: ts }),
  setMonitoringAlertsCount: (count: number) => set({ monitoringAlerts: count }),
}));
