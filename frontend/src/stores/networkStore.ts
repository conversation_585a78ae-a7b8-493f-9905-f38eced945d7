import { create } from "zustand";

interface NetworkState {
  isOnline: boolean;
  setOnlineStatus: (status: boolean) => void;
  checkNetworkStatus: () => boolean;
}

export const useNetworkStore = create<NetworkState>((set) => ({
  // Start with true (online) as default to avoid false negatives
  isOnline: true,

  setOnlineStatus: (status: boolean) => set({ isOnline: status }),

  checkNetworkStatus: () => {
    if (typeof navigator !== "undefined" && typeof window !== "undefined") {
      const status = navigator.onLine;
      set({ isOnline: status });
      return status;
    }
    // If navigator is not available, assume online
    set({ isOnline: true });
    return true;
  },
}));
