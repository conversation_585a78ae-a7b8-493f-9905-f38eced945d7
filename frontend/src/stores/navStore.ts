import { create } from "zustand";

export type PageKey = "login" | "home" | "chat" | "profile" | "settings";

interface NavState {
  stack: <PERSON>Key[];
  isAuthenticated: boolean;
  unreadCount: number;
  suppressAutoNavigation: boolean; // Flag to prevent automatic navigation
  push: (page: <PERSON><PERSON>ey) => void;
  pop: () => void;
  clearUnread: () => void;
  setAuthenticated: (isAuth: boolean) => void;
  setSuppressAutoNavigation: (suppress: boolean) => void;
}

export const useNavStore = create<NavState>((set) => ({
  stack: ["login"],
  isAuthenticated: false,
  unreadCount: 0,
  suppressAutoNavigation: false,
  push: (page: <PERSON>Key) => set((state) => ({ stack: [...state.stack, page] })),
  pop: () =>
    set((state) => ({ stack: state.stack.slice(0, state.stack.length - 1) })),
  clearUnread: () => set({ unreadCount: 0 }),
  setAuthenticated: (isAuth: boolean) => set({ isAuthenticated: isAuth }),
  setSuppressAutoNavigation: (suppress: boolean) =>
    set({ suppressAutoNavigation: suppress }),
}));
