import { useNavStore } from "./stores/navStore";
import { useAuthStore } from "./stores/authStore";
import ChatPage from "./pages/ChatPage";

import SettingsPage from "./pages/SettingsPage";
import ChatHomePage from "./pages/ChatHomePage";
import LoginPage from "./pages/LoginPage";
import WebSocketProvider from "./components/WebSocketProvider";
import NavigationProvider from "./components/NavigationProvider";
import { LoginModeProvider } from "./contexts/LoginModeContext";
import { useEffect } from "react";

function App() {
  const { stack } = useNavStore();
  const { isAuthenticated, isLoading, preventAutoRedirect } = useAuthStore();
  const currentPage = stack[stack.length - 1];

  // 🚀 EARLY AUTH CLEARING: Handle fresh_login and logout actions before any routing
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get("mode");
    const action = urlParams.get("action");

    if (
      mode === "extension" &&
      (action === "fresh_login" || action === "logout")
    ) {
      console.warn(
        `[App] 🔄 ${action} action detected - clearing auth state before routing`
      );

      const authStore = useAuthStore.getState();

      // Clear all localStorage auth data
      localStorage.removeItem("firebase_id_token");
      localStorage.removeItem("firebase_uid");
      localStorage.removeItem("sessionUserId");
      localStorage.removeItem("authToken");
      localStorage.removeItem("selectedBroker");

      // Clear Firebase auth state immediately
      authStore.signOut().catch(console.error);

      if (action === "fresh_login") {
        // For fresh login, ensure we stay for login flow
        authStore.setPreventAutoRedirect(true);
        console.warn(
          `[App] ✅ Auth cleared for fresh_login - proceeding to login flow`
        );
      } else if (action === "logout") {
        // For logout, reset to default and close tab
        authStore.setPreventAutoRedirect(false);
        console.warn(
          `[App] ✅ Auth cleared for logout - closing tab in 1 second`
        );
        setTimeout(() => {
          console.warn(`[App] ✅ Logout complete - closing tab`);
          window.close();
        }, 1000);
      }
    } else {
      // 🚀 FIX ISSUE 2: Pure web flow - ensure authenticated users NEVER go to ChatHomePage
      const authStore = useAuthStore.getState();
      const currentPath = window.location.pathname;
      const isRootPath = currentPath === "/" || currentPath === "";

      // If this is pure web mode (no extension mode param) and user is authenticated
      if (!mode && authStore.isAuthenticated && isRootPath) {
        // Pure web flow ALWAYS stays on WebLoginPage (Step 4) - never goes to ChatHomePage
        console.warn(
          "[App] 🔒 Pure web user authenticated - always staying on Step 4 (broker selection)"
        );
        authStore.setPreventAutoRedirect(true);
        console.warn(
          "[App] 🔒 Set preventAutoRedirect=true for pure web flow - ChatHomePage is extension-only"
        );
      }
    }
  }, []); // Run only once on mount

  // Show loading screen while auth is initializing
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // If not authenticated, or if authenticated but preventAutoRedirect is true (web flow), show login page
  if (!isAuthenticated || (isAuthenticated && preventAutoRedirect)) {
    return (
      <LoginModeProvider>
        <LoginPage />
      </LoginModeProvider>
    );
  }

  const renderPage = () => {
    switch (currentPage) {
      case "login":
        return <ChatHomePage />; // Redirect to home if authenticated
      case "home":
        return <ChatHomePage />;
      case "chat":
        return <ChatPage />;
      case "profile":
        return <ChatHomePage />; // Profile is handled via sidebar, redirect to home
      case "settings":
        return <SettingsPage />;
      default:
        return <ChatHomePage />;
    }
  };

  return (
    <LoginModeProvider>
      <NavigationProvider>
        <WebSocketProvider>
          <div>{renderPage()}</div>
        </WebSocketProvider>
      </NavigationProvider>
    </LoginModeProvider>
  );
}

export default App;
