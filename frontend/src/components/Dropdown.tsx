import React, { useState } from "react";
import { cn } from "../utils/cn";
import ChevronDownIcon from "../assets/chevron-down.svg";

interface DropdownOption {
  value: string;
  label: string;
  icon?: string;
}

interface DropdownProps {
  options: DropdownOption[];
  value?: string;
  onSelect: (value: string) => void;
  placeholder?: string;
  className?: string;
  icon?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  options,
  value,
  onSelect,
  placeholder = "Select option",
  className,
  icon,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const selectedOption = options.find((option) => option.value === value);

  const toggleDropdown = () => setIsOpen(!isOpen);

  const handleSelect = (optionValue: string) => {
    onSelect(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={cn("relative", className)}>
      <button
        onClick={toggleDropdown}
        className="bg-white border border-[#dee4f0] rounded-lg px-3 py-2 flex items-center gap-1 text-[#181e29] text-sm font-normal leading-[20px] hover:bg-gray-50 transition-colors"
      >
        {icon && <img src={icon} alt="" className="w-5 h-5" />}
        {selectedOption?.icon && (
          <img src={selectedOption.icon} alt="" className="w-5 h-5" />
        )}
        <span className="px-0.5">{selectedOption?.label || placeholder}</span>
        <img
          src={ChevronDownIcon}
          alt=""
          className={cn(
            "w-4 h-4 transition-transform duration-200",
            isOpen ? "rotate-180" : ""
          )}
        />
      </button>

      {isOpen && (
        <>
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute top-full left-0 mt-1 w-full bg-white border border-[#dee4f0] rounded-lg shadow-lg z-20 max-h-60 overflow-y-auto">
            {options.map((option) => (
              <button
                key={option.value}
                onClick={() => handleSelect(option.value)}
                className={cn(
                  "w-full px-3 py-2 text-left text-sm font-normal leading-[20px] hover:bg-gray-50 transition-colors flex items-center gap-2",
                  value === option.value
                    ? "bg-[#f6eafc] text-[#a330e5]"
                    : "text-[#181e29]"
                )}
              >
                {option.icon && (
                  <img src={option.icon} alt="" className="w-5 h-5" />
                )}
                {option.label}
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

export default Dropdown;
