import React, { useState } from "react";
import * as Popover from "@radix-ui/react-popover";
import { cn } from "../utils/cn";
import ChevronDown from "../assets/chevron-down.svg";
import Avatar from "./Avatar";
import type { Model } from "../types/chat";

const models: Model[] = [
  {
    id: "mock-llm-v1",
    name: "mock-llm-v1",
    displayName: "Mock LLM v1",
    avatar: "https://via.placeholder.com/20x20/3b82f6/ffffff?text=M",
  },
  {
    id: "deepseek-r1",
    name: "deepseek-r1-distill-llama-70b",
    displayName: "deepseek-r1...",
    avatar: "https://via.placeholder.com/20x20/9333ea/ffffff?text=DS",
  },
  {
    id: "gpt-4",
    name: "gpt-4-turbo",
    displayName: "GPT-4 Turbo",
    avatar: "https://via.placeholder.com/20x20/10b981/ffffff?text=GPT",
  },
  {
    id: "claude-3",
    name: "claude-3-sonnet",
    displayName: "Claude 3 Sonnet",
    avatar: "https://via.placeholder.com/20x20/f59e0b/ffffff?text=C",
  },
];

interface ModelDropdownProps {
  selectedModel?: Model;
  onModelChange?: (model: Model) => void;
  className?: string;
}

const ModelDropdown: React.FC<ModelDropdownProps> = ({
  selectedModel = models[0],
  onModelChange,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleModelSelect = (model: Model) => {
    onModelChange?.(model);
    setIsOpen(false);
  };

  return (
    <Popover.Root open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <button
          className={cn(
            "bg-[#f6eafc] flex flex-row gap-1 items-center justify-start p-2 rounded-2xl hover:bg-[#f0e4f8] transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500",
            className
          )}
        >
          {/* Avatar */}
          <Avatar
            title={selectedModel.displayName}
            size="sm"
            variant="gradient"
          />

          {/* Model Name */}
          <span className="font-['Inter:Regular',_sans-serif] font-normal text-[#43556e] text-sm leading-normal text-nowrap">
            {selectedModel.displayName}
          </span>

          {/* Chevron Down */}
          <div className="w-3 h-3 sm:w-4 sm:h-4">
            <img
              src={ChevronDown}
              alt="chevron down"
              className={cn(
                "w-full h-full transition-transform duration-200",
                isOpen && "rotate-180"
              )}
            />
          </div>
        </button>
      </Popover.Trigger>

      <Popover.Portal>
        <Popover.Content
          className="w-56 sm:w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
          sideOffset={8}
          align="start"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          <div className="p-2 max-h-48 sm:max-h-60 overflow-y-auto">
            {models.map((model) => (
              <button
                key={model.id}
                onClick={() => handleModelSelect(model)}
                className={cn(
                  "w-full flex items-center gap-3 px-3 py-2 text-sm rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors",
                  selectedModel.id === model.id && "bg-gray-100"
                )}
              >
                {/* Avatar */}
                <Avatar title={model.displayName} size="sm" variant="solid" />

                <div className="flex flex-col items-start flex-1 min-w-0">
                  <span className="font-medium text-gray-900 truncate">
                    {model.displayName}
                  </span>
                  <span className="text-xs text-gray-500 truncate w-full">
                    {model.name}
                  </span>
                </div>

                {selectedModel.id === model.id && (
                  <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                )}
              </button>
            ))}
          </div>
        </Popover.Content>
      </Popover.Portal>
    </Popover.Root>
  );
};

export default ModelDropdown;
