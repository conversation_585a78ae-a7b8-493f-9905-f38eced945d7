import React, { useEffect } from "react";
import { useNavStore, type <PERSON><PERSON><PERSON> } from "../stores/navStore";
import { useNetworkStore } from "../stores/networkStore";

const NavigationProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { stack, push, pop } = useNavStore();
  const { checkNetworkStatus } = useNetworkStore();

  // Effect for Network Initialization - Run once
  useEffect(() => {
    checkNetworkStatus();
  }, [checkNetworkStatus]);

  // Effect for Initial URL Load
  useEffect(() => {
    const path = window.location.pathname.substring(1) as <PERSON><PERSON>ey;
    const validPages: PageKey[] = ["home", "chat", "profile", "settings"];
    // Only push if the stack is in its initial state, to avoid double-adding on hot-reloads.
    if (
      path &&
      validPages.includes(path) &&
      stack.length === 1 &&
      stack[0] === "home"
    ) {
      push(path);
    }
  }, [push, stack]);

  // Effect for Browser Back/Forward Navigation
  useEffect(() => {
    const handlePopState = () => {
      const newPath = window.location.pathname.substring(1) as <PERSON><PERSON><PERSON>;
      const stackIndex = stack.lastIndexOf(newPath);

      if (stackIndex !== -1) {
        // If the page is in our history, pop until we get to it.
        while (stack[stack.length - 1] !== newPath && stack.length > 1) {
          pop();
        }
      } else {
        // If the page isn't in our known stack (e.g., browser forward),
        // a simple push is a reasonable default.
        push(newPath);
      }
    };

    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [stack, push, pop]);

  return <>{children}</>;
};

export default NavigationProvider;
