import React from "react";
import { cn } from "../utils/cn";
import CheckCircleIcon from "../assets/flowerTick.svg";

interface ExecutionHeaderProps {
  executionCompleted?: boolean;
  isMonitoring?: boolean;
  hasMonitoringStarted?: boolean;
}

const COLORS = {
  cardBg: "#ffffff",
  cardBorder: "#dee4f0",
  textPrimary: "#181e29",
  textSecondary: "#43556e",
  spinner: "#5c54fd",
};

const Spinner: React.FC = () => (
  <span
    className={cn(
      "w-5 h-5 inline-block rounded-full border-2 border-solid animate-spin"
    )}
    style={{ borderColor: COLORS.spinner, borderTopColor: "transparent" }}
  />
);

const ExecutionHeader: React.FC<ExecutionHeaderProps> = ({
  executionCompleted,
  isMonitoring,
  hasMonitoringStarted,
}) => {
  let iconNode: React.ReactNode = <Spinner />;
  let text = "Executing orders";

  if (isMonitoring && !executionCompleted) {
    // Show tick when monitoring has fully started (all alerts in progress)
    if (hasMonitoringStarted) {
      iconNode = <img src={CheckCircleIcon} alt="status" className="w-5 h-5" />;
      text = "Set up done! Monitoring started.";
    } else {
      iconNode = <Spinner />;
      text = "Setting up a conditional order...";
    }
  }
  if (executionCompleted) {
    iconNode = <img src={CheckCircleIcon} alt="status" className="w-5 h-5" />;
    text = "Order placed!";
  }

  return (
    <div className={cn("w-full flex items-center gap-2 px-3 pb-3 ")}>
      {iconNode}
      <div
        className="text-sm font-medium font-['Inter'] leading-tight"
        style={{ color: COLORS.textPrimary }}
      >
        {text}
      </div>
    </div>
  );
};

export default ExecutionHeader;
