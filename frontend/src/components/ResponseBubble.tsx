import React from "react";
import { cn } from "../utils/cn";
import CTAButton from "./CTAButton";
import Avatar from "./Avatar";
import ExecutionOrderCard from "./ExecutionOrderCard";
import ExecutionHeader from "./ExecutionHeader";
import ĀagmanIcon from "../assets/Āagman.svg";

interface ExecutionOrderStatus {
  id: string;
  symbol: string;
  status: string;
  broker_status?: string; // Optional: broker-specific status text
  quantity?: number;
  price?: string;
  orderType?: string;
  product?: string;
  broker?: string;
  action_id?: string;
  primitive?: string; // NEW: display action/primitive label (e.g., BUY, SELL, MONITOR)
  // Additional fields that may be present
  exchange?: string;
  timestamp?: string;
  isMonitoring?: boolean;
  triggerPrice?: string | number;
  limitPrice?: string | number;
  currentPrice?: string | number;
  conditionOperator?: string;
  conditionValue?: string | number;
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  execution_details?: Record<string, string>; // NEW: detailed execution info
}

interface ResponseBubbleProps {
  message: string;
  primitives?: string[]; // Now just expects transformed strings
  actions?: Array<{
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }>;
  onActionClick?: (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => void;
  className?: string;
  // Optional execution list to render live status
  executionOrders?: ExecutionOrderStatus[];
  executionCompleted?: boolean;
  monitoringStarted?: boolean;
  // When provided, this text replaces primitives rendering using the same styles
  primitivesOverrideText?: string;
}

const ResponseBubble: React.FC<ResponseBubbleProps> = ({
  message: _message,
  primitives,
  actions,
  onActionClick,
  className,
  executionOrders,
  executionCompleted,
  monitoringStarted,
  primitivesOverrideText,
}) => {
  const hasExecution =
    Array.isArray(executionOrders) && executionOrders.length > 0;
  const showList = hasExecution;

  return (
    <div className={cn("flex flex-col items-start w-full", className)}>
      {/* Header row with Avatar and "Āagman" text */}
      <div className="flex items-center gap-2">
        <Avatar title="AI" icon={ĀagmanIcon} size="md" variant="aagman" />
        <span className="font-['Inter'] text-sm text-[#43556e]">Āagman</span>
      </div>

      {/* <div className="relative w-3 h-3 rounded-lg bg-gradient-to-r from-[#5c54fd] to-[#a330e5] flex items-center justify-center shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border-2 border-[rgba(255,255,255,0.12)]">
            <div className="absolute inset-0 rounded-lg shadow-[0px_0px_0px_1px_inset_rgba(16,24,40,0.18),0px_-2px_0px_0px_inset_rgba(16,24,40,0.05)]" />
            <div className="flex items-center gap-2">
            <img
              src={ĀagmanIcon}
              alt="Āagman"
              className="size-4 relative z-10"
            />
            <span className="font-['Inter'] text-sm text-[#43556e]">Āagman</span>
            </div>
          </div> */}

      {/* Message bubble */}
      <div className="bg-transparent text-[#181e29] rounded-[16px] rounded-tl-[4px] py-2 w-full w-full">
        {/* Raw Text - disabled for now */}
        {/* Raw Text - disabled for now */}
        {/* {false && (
          <div className="font-['Inter'] text-[16px] leading-[24px] font-normal whitespace-pre-wrap">
            {message}
          </div>
        )} */}

        {/* Primitives as Unordered List or Override Text */}
        {(primitivesOverrideText || (primitives && primitives.length > 0)) && (
          <div className="space-y-1">
            {(
              (primitivesOverrideText
                ? [primitivesOverrideText]
                : primitives) || []
            ).map((text, index) => (
              <div
                key={index}
                className="font-['Inter'] text-[14px] leading-[20px] font-normal text-[#43556e]"
              >
                {String(text || "")
                  .split("\n")
                  .map((line, idx2) => (
                    <div key={idx2}>{line}</div>
                  ))}
              </div>
            ))}
          </div>
        )}

        {/* Live/Final Execution Status List */}
        {showList && (
          <div className="mt-3 w-full">
            <ExecutionHeader
              executionCompleted={executionCompleted}
              isMonitoring={(executionOrders || []).some(
                (o) => o.isMonitoring === true
              )}
              hasMonitoringStarted={Boolean(monitoringStarted)}
            />
            <div className="space-y-2 w-full">
              {(executionOrders || []).map((o) => (
                <div key={o.action_id || o.id || `${o.symbol}-${o.status}`}>
                  <ExecutionOrderCard
                    symbol={o.symbol}
                    status={o.broker_status || o.status}
                    quantity={o.quantity}
                    price={o.price}
                    orderType={o.orderType}
                    product={o.product}
                    broker={o.broker}
                    exchange={o.exchange}
                    timestamp={o.timestamp}
                    primitive={o.primitive}
                    // Monitoring-specific props (if present)
                    triggerPrice={o.triggerPrice}
                    currentPrice={o.currentPrice}
                    limitPrice={o.limitPrice}
                    isMonitoring={o.isMonitoring}
                    conditionOperator={o.conditionOperator}
                    conditionValue={o.conditionValue}
                    onTriggerAction={o.onTriggerAction}
                    onTriggerQuantity={o.onTriggerQuantity}
                    onTriggerSymbol={o.onTriggerSymbol}
                    execution_details={o.execution_details}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {actions && actions.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-3">
            {actions.map((action, index) => (
              <CTAButton
                key={index}
                onClick={() => onActionClick?.(action)}
                className="text-[12px] leading-[14px] px-3 py-2 bg-white hover:bg-[#4c44ed] transition-colors border border-[#7D76FD] hover:text-white"
                style={{ color: "var(--violet-500, #8B5CF6)" }}
              >
                {action.description}
              </CTAButton>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ResponseBubble;
