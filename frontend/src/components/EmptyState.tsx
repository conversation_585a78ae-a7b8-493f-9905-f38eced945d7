import React from "react";
import { cn } from "../utils/cn";
import NoOrdersIcon from "../assets/NoOrders.svg";
import NotificationsEmptyIcon from "../assets/NotificationsEmpty.svg";
import IsOfflineIcon from "../assets/IsOffline.svg";

export interface EmptyStateProps {
  title?: string;
  description?: string;
  className?: string;
  type?: "orders" | "monitoring" | "notifications" | "offline";
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  className,
  type = "orders",
}) => {
  // Default content based on type
  const defaultContent = {
    orders: {
      title: "No Orders Yet!",
      description:
        "You haven't placed any orders. Once you do, they'll appear here for easy tracking and updates.",
    },
    monitoring: {
      title: "No Orders in Monitoring",
      description:
        "You're not tracking any orders at the moment. Once you start monitoring an order, you'll see real-time updates and insights here.",
    },
    notifications: {
      title: "No Notifications",
      description:
        "You're all caught up! New notifications about your orders and monitoring alerts will appear here.",
    },
    offline: {
      title: "Oops! No Connection",
      description:
        "It looks like you're offline. Please check your internet connection and try again.",
    },
  };

  // Select appropriate SVG based on type
  const getSvgIcon = () => {
    switch (type) {
      case "offline":
        return IsOfflineIcon;
      case "notifications":
        return NotificationsEmptyIcon;
      case "orders":
      case "monitoring":
      default:
        return NoOrdersIcon;
    }
  };

  // Get alt text based on type
  const getAltText = () => {
    switch (type) {
      case "offline":
        return "Offline illustration";
      case "notifications":
        return "No notifications illustration";
      case "orders":
        return "No orders illustration";
      case "monitoring":
        return "No monitoring illustration";
      default:
        return "Empty state illustration";
    }
  };

  const content = defaultContent[type];
  const displayTitle = title || content.title;
  const displayDescription = description || content.description;

  return (
    <div
      className={cn(
        "flex flex-col items-center justify-center px-4 py-8 bg-[#f4f6fa] min-h-[400px]",
        className
      )}
    >
      {/* Illustration */}
      <div className="mb-4">
        <img
          src={getSvgIcon()}
          alt={getAltText()}
          className="w-[220px] h-[220px] shrink-0"
        />
      </div>

      {/* Text Content */}
      <div className="flex flex-col items-center gap-1 max-w-[318px] text-center">
        {/* Title */}
        <h3 className="font-['Inter'] font-semibold text-[18px] leading-[26px] text-[#181e29] mb-1">
          {displayTitle}
        </h3>

        {/* Description */}
        <p className="font-['Inter'] font-normal text-[14px] leading-[20px] text-[#43556e] text-center">
          {displayDescription}
        </p>
      </div>
    </div>
  );
};

export default EmptyState;
