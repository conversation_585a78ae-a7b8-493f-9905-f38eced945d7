import React from "react";
import { cn } from "../utils/cn";

interface AvatarProps {
  title: string;
  icon?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "gradient" | "solid" | "aagman";
  backgroundColor?: string;
  textColor?: string;
}

const Avatar: React.FC<AvatarProps> = ({
  title,
  icon,
  className,
  size = "md",
  variant = "gradient",
  backgroundColor = "bg-[#9333ea]",
  textColor = "text-white",
}) => {
  // Generate initials from title
  const getInitials = (text: string): string => {
    const cleanText = text.trim();
    if (cleanText.length === 0) return "";
    if (cleanText.length === 1) return cleanText.toUpperCase();

    // Split by spaces and take first 2 characters if multiple words
    const words = cleanText.split(/\s+/);
    if (words.length > 1) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }

    // Take first 2 characters if single word
    return cleanText.substring(0, 2).toUpperCase();
  };

  // Size classes
  const sizeClasses = {
    sm: "w-4 h-4 sm:w-5 sm:h-5",
    md: "w-5 h-5 sm:w-6 sm:h-6",
    lg: "w-8 h-8 sm:w-10 sm:h-10",
  };

  // Text size classes
  const textSizeClasses = {
    sm: "text-[0.5rem] sm:text-[0.625rem]",
    md: "text-xs sm:text-sm",
    lg: "text-sm sm:text-base",
  };

  if (icon) {
    // Render with icon
    if (variant === "aagman") {
      return (
        <div className={cn("relative", sizeClasses[size], className)}>
          <div
            className={cn(
              "relative w-full h-full rounded-lg bg-gradient-to-r from-[#5c54fd] to-[#a330e5] flex items-center justify-center shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border-2 border-[rgba(255,255,255,0.12)]"
            )}
          >
            <div className="absolute inset-0 rounded-lg shadow-[0px_0px_0px_1px_inset_rgba(16,24,40,0.18),0px_-2px_0px_0px_inset_rgba(16,24,40,0.05)]" />
            <img
              src={icon}
              alt={title}
              className="w-[70%] h-[70%] object-contain relative z-10"
            />
          </div>
        </div>
      );
    }

    return (
      <div className={cn("relative", sizeClasses[size], className)}>
        <div
          className={cn(
            "w-full h-full rounded-full flex items-center justify-center",
            variant === "gradient"
              ? "bg-gradient-to-r from-[#9333ea] to-[#7c3aed]"
              : backgroundColor
          )}
        >
          <img
            src={icon}
            alt={title}
            className="w-[70%] h-[70%] object-contain"
          />
        </div>
      </div>
    );
  }

  // Render with initials
  if (variant === "gradient") {
    return (
      <div className={cn("relative", sizeClasses[size], className)}>
        <div
          className={cn(
            "w-full h-full rounded-full flex items-center justify-center",
            backgroundColor
          )}
        >
          <div className="w-[85%] h-[85%] bg-gradient-to-r from-[#9333ea] to-[#7c3aed] rounded-full flex items-center justify-center">
            <span className={cn("font-bold", textSizeClasses[size], textColor)}>
              {getInitials(title)}
            </span>
          </div>
        </div>
      </div>
    );
  }

  // Simple solid variant
  return (
    <div className={cn("relative", sizeClasses[size], className)}>
      <div
        className={cn(
          "w-full h-full rounded-full flex items-center justify-center",
          backgroundColor
        )}
      >
        <span className={cn("font-bold", textSizeClasses[size], textColor)}>
          {getInitials(title)}
        </span>
      </div>
    </div>
  );
};

export default Avatar;
