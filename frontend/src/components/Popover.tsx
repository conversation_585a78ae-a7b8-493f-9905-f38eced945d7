import React from "react";
import * as RadixPopover from "@radix-ui/react-popover";
import { cn } from "../utils/cn";

const Popover = RadixPopover.Root;

const PopoverTrigger = RadixPopover.Trigger;

const PopoverContent: React.FC<RadixPopover.PopoverContentProps> = ({
  className,
  children,
  align = "center",
  sideOffset = 4,
  ...props
}) => {
  return (
    <RadixPopover.Portal>
      <RadixPopover.Content
        align={align}
        sideOffset={sideOffset}
        className={cn(
          // Base Card styling
          "rounded-2xl overflow-hidden border bg-white shadow-lg",
          // Popover specific styling
          "z-50 min-w-[8rem] p-4",
          // Animation classes
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[side=bottom]:slide-in-from-top-2",
          "data-[side=left]:slide-in-from-right-2",
          "data-[side=right]:slide-in-from-left-2",
          "data-[side=top]:slide-in-from-bottom-2",
          className
        )}
        {...props}
      >
        {children}
        <RadixPopover.Arrow className="fill-white" />
      </RadixPopover.Content>
    </RadixPopover.Portal>
  );
};

const PopoverClose = RadixPopover.Close;

// Compound component with all parts
const PopoverComponent = Object.assign(Popover, {
  Trigger: PopoverTrigger,
  Content: PopoverContent,
  Close: PopoverClose,
});

export {
  PopoverComponent as Popover,
  PopoverTrigger,
  PopoverContent,
  PopoverClose,
};

export default PopoverComponent;

/*
 * Custom Radix UI Popover Component
 *
 * Features:
 * - ✅ Same styling as Card component (rounded-2xl, overflow-hidden, border, bg-white)
 * - ✅ Modular system (Popover.Trigger, Popover.Content, Popover.Close)
 * - ✅ Clicking outside closes the popover automatically
 * - ✅ Background doesn't block other parts of the page
 * - ✅ className override support for custom styling
 * - ✅ Smooth animations with fade and zoom effects
 * - ✅ Arrow pointing to trigger element
 * - ✅ Keyboard navigation support
 * - ✅ Accessibility features built-in
 *
 * Usage Examples:
 *
 * // Basic usage with compound component pattern:
 * import { Popover } from './Popover';
 *
 * <Popover>
 *   <Popover.Trigger asChild>
 *     <button>Click me</button>
 *   </Popover.Trigger>
 *   <Popover.Content>
 *     <div>
 *       <h3>Title</h3>
 *       <p>Content here</p>
 *     </div>
 *   </Popover.Content>
 * </Popover>
 *
 * // With custom styling:
 * <Popover>
 *   <Popover.Trigger asChild>
 *     <button className="bg-blue-500 text-white px-4 py-2 rounded">
 *       Open Menu
 *     </button>
 *   </Popover.Trigger>
 *   <Popover.Content className="w-80 p-6 bg-gray-50">
 *     <div>Custom styled content</div>
 *   </Popover.Content>
 * </Popover>
 *
 * // With close button:
 * <Popover>
 *   <Popover.Trigger asChild>
 *     <button>Settings</button>
 *   </Popover.Trigger>
 *   <Popover.Content>
 *     <div>
 *       <h3>Settings</h3>
 *       <p>Configure your preferences</p>
 *       <Popover.Close asChild>
 *         <button className="mt-2 px-3 py-1 bg-gray-200 rounded">
 *           Close
 *         </button>
 *       </Popover.Close>
 *     </div>
 *   </Popover.Content>
 * </Popover>
 *
 * // Alternative import syntax:
 * import { Popover, PopoverTrigger, PopoverContent, PopoverClose } from './Popover';
 *
 * <Popover>
 *   <PopoverTrigger asChild>
 *     <button>Open</button>
 *   </PopoverTrigger>
 *   <PopoverContent align="start" sideOffset={8}>
 *     <div>Content with custom alignment</div>
 *   </PopoverContent>
 * </Popover>
 *
 * Props:
 * - PopoverContent:
 *   - className: Override default styling
 *   - align: "start" | "center" | "end" (default: "center")
 *   - sideOffset: number (default: 4)
 *   - children: React.ReactNode
 *   - ...all other Radix PopoverContent props
 *
 * - PopoverTrigger:
 *   - asChild: boolean (recommended: true)
 *   - children: React.ReactNode
 *   - ...all other Radix PopoverTrigger props
 *
 * - PopoverClose:
 *   - asChild: boolean (recommended: true)
 *   - children: React.ReactNode
 *   - ...all other Radix PopoverClose props
 */
