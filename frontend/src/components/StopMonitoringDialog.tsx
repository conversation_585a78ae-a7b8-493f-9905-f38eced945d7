import React from "react";
import {
  CenteredDialog,
  CenteredDialogContent,
  CenteredDialogClose,
  CenteredDialogTrigger,
} from "./CenteredDialog";
import CTAButton from "./CTAButton";
import TrashIcon from "../assets/trash-icon.svg";

// Design tokens resolved from Figma (via figma-variables-resolver and node 4557:50819)
// Colors: Text/Neutral/Primary #181e29, Text/Neutral/Secondary #43556e
// Button Error BG #f03142, Button Error Border #f35a68, Error/100 #FEE4E2, Border/Neutral/Primary #dee4f0
// Typography: Heading xs 18/26 bold, Body sm 14/20 regular, Button md 16/24 medium
// Radius: 8 (rounded-lg)
// Gaps: 8, 24, 32, 40

export interface StopMonitoringDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  targetName: string; // e.g., "NIFTY 50" or "SENSEX"
  handleSubmit: () => void; // called on "Yes, Stop"
  onCancel?: () => void;
  confirmLabel?: string; // defaults to "Yes, Stop"
  cancelLabel?: string; // defaults to "Cancel"
  children: React.ReactNode; // Trigger element
}

const StopMonitoringDialog: React.FC<StopMonitoringDialogProps> = ({
  open,
  onOpenChange,
  targetName,
  handleSubmit,
  onCancel,
  confirmLabel = "Yes, Stop",
  cancelLabel = "Cancel",
  children,
}) => {
  return (
    <CenteredDialog open={open} onOpenChange={onOpenChange}>
      <CenteredDialogTrigger> {children} </CenteredDialogTrigger>
      <CenteredDialogContent className="w-[384px] h-auto p-0 bg-white rounded-lg outline outline-2 outline-[#dee4f0]">
        {/* Decorative grid background */}
        <div className="pointer-events-none select-none absolute inset-0 opacity-10">
          <div className="absolute left-1/2 top-[-202px] -translate-x-1/2 w-[480px] h-[480px]">
            <div className="absolute inset-0 bg-[radial-gradient(ellipse_50%_50%_at_50%_50%,_black_0%,_rgba(0,0,0,0)_100%)]" />
            {/* Thin grid lines mimic via borders; kept subtle by opacity above */}
            <div className="absolute inset-0" />
          </div>
        </div>

        {/* Close button (top-right) */}
        <CenteredDialogClose asChild>
          <button
            aria-label="Close"
            className="w-6 h-6 absolute top-4 right-4 flex items-center justify-center rounded hover:bg-gray-100"
          />
        </CenteredDialogClose>

        {/* Content */}
        <div className="w-full px-6 pt-10 pb-6 flex flex-col items-center gap-10">
          {/* Header */}
          <div className="w-full flex flex-col items-center gap-6">
            {/* Featured icon */}
            <div className="w-16 h-16 rounded-[28px] bg-[#FEE4E2] items-center justify-center place-items-center flex">
              <div className="  w-9 h-9 flex items-center justify-center overflow-hidden rounded-[48px]">
                <img src={TrashIcon} alt="Delete" className="w-7 h-7" />
              </div>
            </div>

            {/* Title + description */}
            <div className="w-full flex flex-col items-center gap-2 text-center">
              <h2 className="font-['Inter'] font-bold text-[18px] leading-[26px] text-[#181e29]">
                {`Stop Monitoring ${targetName}?`}
              </h2>
              <p className="font-['Inter'] text-[14px] leading-[20px] text-[#43556e]">
                {`You’re about to stop monitoring ${targetName}. This means you will no longer get updates or insights for it.`}
                <br />
                {`Do you still want to stop?`}
              </p>
            </div>
          </div>

          {/* Actions */}
          <div className="w-full flex flex-row gap-6 justify-end">
            {/* Cancel */}
            <CTAButton
              onClick={() => {
                onCancel?.();
                onOpenChange(false);
              }}
              className="flex-1 h-12 bg-white text-[#43556e] border border-[#dee4f0] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
              textColor="text-[#43556e]"
            >
              {cancelLabel}
            </CTAButton>

            {/* Confirm */}
            <CTAButton
              onClick={() => {
                handleSubmit();
                onOpenChange(false);
              }}
              className="flex-1 h-12 bg-[#f03142] border-2 border-[#f35a68]"
            >
              {confirmLabel}
            </CTAButton>
          </div>
        </div>
      </CenteredDialogContent>
    </CenteredDialog>
  );
};

export default StopMonitoringDialog;

/*
Usage:

// Ensure Tailwind is configured. If you bring Figma tokens into Tailwind,
// map them in tailwind.config.js theme.extend (colors, spacing, radii, shadows)
// using values from `frontend/figma-variables-resolver.js`.

import React from 'react';
import StopMonitoringDialog from './StopMonitoringDialog';

export const Example = () => {
  const [open, setOpen] = React.useState(true);
  return (
    <StopMonitoringDialog
      open={open}
      onOpenChange={setOpen}
      targetName="NIFTY 50"
      handleSubmit={() => console.log('Stopped!')}
    />
  );
};
*/
