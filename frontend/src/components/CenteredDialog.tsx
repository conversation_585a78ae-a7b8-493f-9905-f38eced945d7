import React from "react";
import * as RadixDialog from "@radix-ui/react-dialog";
import { cn } from "../utils/cn";
import XIcon from "../assets/x.svg";

const CenteredDialog = RadixDialog.Root;
const CenteredDialogTrigger = RadixDialog.Trigger;

const CenteredDialogContent: React.FC<RadixDialog.DialogContentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <RadixDialog.Portal>
      <RadixDialog.Overlay className="fixed inset-0 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 z-50" />
      <RadixDialog.Content
        {...props}
        className={cn(
          "fixed left-1/2 top-1/2 z-50 w-[512px] h-[600px] -translate-x-1/2 -translate-y-1/2",
          "bg-white rounded-2xl outline outline-2 outline-gray-200",
          "data-[state=open]:animate-in data-[state=closed]:animate-out",
          "data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
          "data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95",
          "data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%]",
          "data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]",
          "flex flex-col overflow-hidden",
          className
        )}
      >
        {/* Close Button */}
        <RadixDialog.Close asChild>
          <button className="w-6 h-6 absolute top-3 right-3 cursor-pointer z-20 flex items-center justify-center hover:bg-gray-100 rounded transition-colors">
            <img src={XIcon} alt="Close" className="w-6 h-6" />
          </button>
        </RadixDialog.Close>

        {/* Accessibility: Hidden title and description for screen readers */}
        <RadixDialog.Title className="sr-only">
          Login Dialog
        </RadixDialog.Title>
        <RadixDialog.Description className="sr-only">
          Login to your account
        </RadixDialog.Description>

        {/* Content */}
        <div className="relative z-10 w-full h-full flex flex-col p-12">
          {children}
        </div>
      </RadixDialog.Content>
    </RadixDialog.Portal>
  );
};

const CenteredDialogHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  ...props
}) => (
  <div
    className={cn(
      "flex items-center justify-between p-6 border-b border-gray-200",
      className
    )}
    {...props}
  />
);

const CenteredDialogTitle: React.FC<
  React.HTMLAttributes<HTMLHeadingElement>
> = ({ className, ...props }) => (
  <RadixDialog.Title
    className={cn("text-xl font-semibold text-gray-900", className)}
    {...props}
  />
);

const CenteredDialogClose = RadixDialog.Close;

export {
  CenteredDialog,
  CenteredDialogTrigger,
  CenteredDialogContent,
  CenteredDialogHeader,
  CenteredDialogTitle,
  CenteredDialogClose,
};
