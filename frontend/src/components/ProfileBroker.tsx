import React from "react";
import Card from "./Card";
import Pill from "./Pill";
import { cn } from "../utils/cn";

interface ProfileBrokerProps {
  brokerName: string;
  brokerLogo: string;
  isConnected: boolean;
  description: string;
  onViewDetails?: () => void;
  onConnect?: () => void;
  className?: string;
}

const ProfileBroker: React.FC<ProfileBrokerProps> = ({
  brokerName,
  brokerLogo,
  isConnected,
  description,
  onViewDetails,
  onConnect,
  className,
}) => {
  return (
    <Card
      className={cn(
        "shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_2px_0px_rgba(0,0,0,0.08)] border-[#dee4f0]",
        className
      )}
    >
      <div className="flex flex-col gap-2 p-3">
        {/* Header with broker info and status */}
        <div className="flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 flex-1">
              <div
                className="w-8 h-8 rounded-lg bg-center bg-cover bg-no-repeat shrink-0"
                style={{ backgroundImage: `url('${brokerLogo}')` }}
              />
              <div className="font-semibold text-base leading-6 text-[#181e29] flex-1">
                {brokerName}
              </div>
            </div>
            <div className="shrink-0">
              {isConnected ? (
                <Pill>Connected</Pill>
              ) : (
                <div className="inline-flex items-center justify-start gap-1 rounded-2xl border border-[#d1d5db] bg-[#f9fafb] py-0.5 pl-1.5 pr-2">
                  <div className="relative h-2 w-2">
                    <div className="absolute left-[1px] top-[1px] h-1.5 w-1.5 rounded-full bg-[#6b7280]"></div>
                  </div>
                  <div className="text-center text-xs font-normal text-[#6b7280]">
                    Not Connected
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="font-normal text-sm leading-5 text-[#43556e]">
            {description}
          </div>
        </div>

        {/* Divider */}
        <div className="h-px bg-[#dee4f0] w-full" />

        {/* Action buttons */}
        <div className="flex justify-end pt-1 gap-2">
          {!isConnected && onConnect && (
            <button
              onClick={onConnect}
              className="bg-[#5c54fd] rounded-lg overflow-hidden font-medium text-sm leading-5 text-white hover:bg-[#4a42e6] transition-colors px-3 py-1"
            >
              Connect
            </button>
          )}
          <button
            onClick={onViewDetails}
            className="bg-white rounded-lg overflow-hidden font-medium text-sm leading-5 text-[#5c54fd] hover:bg-gray-50 transition-colors px-3 py-1"
          >
            View Details
          </button>
        </div>
      </div>
    </Card>
  );
};

export default ProfileBroker;

/*
 * Usage Example:
 *
 * import ProfileBroker from './ProfileBroker';
 *
 * const MyComponent = () => {
 *   return (
 *     <>
 *       <ProfileBroker
 *         brokerName="Zerodha"
 *         brokerLogo="/assets/zerodha-logo.png"
 *         isConnected={true}
 *         description="Integrating with Zerodha's brokerage platform allows you to seamlessly manage your trading activities."
 *         onViewDetails={() => console.warn('View Zerodha details')}
 *       />
 *
 *       <ProfileBroker
 *         brokerName="Groww"
 *         brokerLogo="/assets/groww-logo.png"
 *         isConnected={false}
 *         description="Connect with Groww to access comprehensive investment and trading services."
 *         onViewDetails={() => console.warn('View Groww details')}
 *       />
 *
 *       <ProfileBroker
 *         brokerName="Upstox"
 *         brokerLogo="/assets/upstox-logo.png"
 *         isConnected={true}
 *         description="Upstox integration provides advanced trading tools and real-time market data."
 *         onViewDetails={() => console.warn('View Upstox details')}
 *       />
 *     </>
 *   );
 * };
 *
 * Design Variables (from Figma):
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Button Text: #5c54fd
 * - Border: #dee4f0
 * - Background: #ffffff
 * - Success Green: #1f8b4d
 * - Success Light: #e9f7ef
 * - Success Border: #a9dfbf
 * - Grey Text/Dot: #6b7280
 * - Grey Light: #f9fafb
 * - Grey Border: #d1d5db
 */
