import React, { useState } from "react";
import { cn } from "../utils/cn";
import Avatar from "./Avatar";
import { useSidebarStore, type SidebarTabKey } from "../stores/sidebarStore";
import { navigateToContent } from "../navigation/contentNavigator";
import { handleNewChat } from "../utils/navigationManager";
import { useDialogStore } from "../stores/dialogStore";

// Icon imports from your assets folder
import ChevronRightDouble from "../assets/chevron-right-double.svg";
import MessageDotsCircle from "../assets/message-dots-circle.svg";
import MessagePlusCircle from "../assets/message-plus-circle.svg";
import BookClosed from "../assets/book-closed.svg";
import SpinningDots from "../assets/spinning-dots.svg";
import Bell01 from "../assets/bell-01.svg";

// Modular Notification Bell component
const NotificationBell = ({ count }: { count: number }) => (
  <div className="relative h-6 w-6">
    <img src={Bell01} alt="Notifications" className="h-full w-full" />
    {count > 0 && (
      <div className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-xs text-white">
        {count}
      </div>
    )}
  </div>
);

// Modular Monitoring Icon component
const MonitoringIcon = ({ count }: { count: number }) => (
  <div className="relative h-6 w-6">
    <img
      src={SpinningDots}
      alt="Monitoring"
      className="h-full w-full animate-spin"
      style={{ animationDuration: "3s" }}
    />
    <div className="absolute inset-0 flex items-center justify-center bg-clip-text text-transparent bg-gradient-to-b from-[#5c54fd] to-[#a330e5] text-sm font-semibold">
      {count}
    </div>
  </div>
);

const navItems = [
  {
    name: "Chat",
    key: "chat" as SidebarTabKey,
    icon: () => (
      <div className="h-6 w-6">
        <img src={MessageDotsCircle} alt="Chat" className="h-full w-full" />
      </div>
    ),
  },
  {
    name: "Orders",
    key: "orders" as SidebarTabKey,
    icon: () => (
      <div className="h-6 w-6">
        <img src={BookClosed} alt="Orders" className="h-full w-full" />
      </div>
    ),
  },
  {
    name: "Monitoring",
    key: "monitoring" as SidebarTabKey,
    icon: MonitoringIcon,
    count: 1, // Will be overridden with actual count
  },
];

const Sidebar: React.FC<{ className?: string }> = ({ className }) => {
  const { activeTab, unreadNotifications, monitoringAlerts } =
    useSidebarStore();
  const [isNewChatLoading, setIsNewChatLoading] = useState(false);
  const { openDialog } = useDialogStore();

  const handleTabClick = (tabKey: SidebarTabKey) => {
    // For Chat, Notifications, Profile: keep navigation behavior
    if (
      tabKey === "chat" ||
      tabKey === "notifications" ||
      tabKey === "profile"
    ) {
      navigateToContent(tabKey);
      return;
    }

    // For Orders/Monitoring: don't navigate; open the corresponding dialog
    if (tabKey === "orders") {
      // navigateToContent(tabKey); // Commented out per requirement to not navigate
      openDialog("all-orders");
      return;
    }
    if (tabKey === "monitoring") {
      // navigateToContent(tabKey); // Commented out per requirement to not navigate
      openDialog("all-monitoring");
      return;
    }
  };

  const handleNewChatClick = async () => {
    if (isNewChatLoading) return; // Prevent multiple clicks

    setIsNewChatLoading(true);

    try {
      const result = await handleNewChat();

      if (result.success) {
        console.warn("✅ New chat session created successfully");
        // Optional: Show success feedback to user
      } else {
        console.error("❌ Failed to create new chat session:", result.error);
        // Optional: Show error message to user
        console.error(`Failed to start new chat: ${result.error}`);
      }
    } catch (error) {
      console.error("❌ Error creating new chat session:", error);
      console.error("Failed to start new chat. Please try again.");
    } finally {
      setIsNewChatLoading(false);
    }
  };

  return (
    <div
      className={cn(
        "flex h-screen w-fit flex-col justify-between border-r bg-gradient-to-b from-[#a330e50f] to-[#5c54fd0f] py-6",
        className
      )}
    >
      <div className="flex flex-col items-center gap-8">
        <button>
          <img src={ChevronRightDouble} alt="Collapse" className="h-8 w-8" />
        </button>
        <nav className="flex flex-col items-center gap-2">
          {navItems.map((item) => (
            <button
              key={item.name}
              onClick={() => handleTabClick(item.key)}
              className={cn(
                " p-4 transition-colors hover:bg-white/50",
                activeTab === item.key && "bg-white"
              )}
              // Add a border to the left if active, mimicking the figma highlight
              style={
                activeTab === item.key
                  ? { borderLeft: "4px solid #A330E5" }
                  : {}
              }
            >
              <item.icon
                count={
                  (item.key === "monitoring" ? monitoringAlerts : item.count) ??
                  0
                }
              />
            </button>
          ))}
        </nav>
      </div>

      <div className="flex flex-col items-center gap-2">
        {/* New Chat Button - positioned above notifications */}
        <button

          onClick={handleNewChatClick}
          disabled={isNewChatLoading}
          className={cn(
            "p-4 transition-all duration-200 rounded-lg group relative hidden",
            isNewChatLoading
              ? "bg-gray-100 cursor-not-allowed"
              : "hover:bg-white/50 hover:bg-gradient-to-r hover:from-[#A330E5]/10 hover:to-[#5C54FD]/10"
          )}
          title={isNewChatLoading ? "Starting new chat..." : "Start New Chat"}
        >
          {isNewChatLoading ? (
            <div className="h-6 w-6 flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-2 border-[#A330E5] border-t-transparent"></div>
            </div>
          ) : (
            <img
              src={MessagePlusCircle}
              alt="Start New Chat"
              className="h-6 w-6 transition-transform group-hover:scale-110"
            />
          )}
        </button>

        <button
          onClick={() => handleTabClick("notifications")}
          className={cn(
            " p-4 transition-colors hover:bg-white/50",
            activeTab === "notifications" && "bg-white"
          )}
          style={
            activeTab === "notifications"
              ? { borderLeft: "4px solid #A330E5" }
              : {}
          }
        >
          <NotificationBell count={unreadNotifications} />
        </button>
        <button
          onClick={() => handleTabClick("profile")}
          className={cn(
            "p-4 transition-colors hover:bg-white/50",
            activeTab === "profile" && "bg-white"
          )}
          style={
            activeTab === "profile" ? { borderLeft: "4px solid #A330E5" } : {}
          }
        >
          <Avatar
            title="Sairaaj"
            size="lg"
            variant="solid"
            backgroundColor="bg-purple-600"
          />
        </button>
      </div>
    </div>
  );
};

export default Sidebar;

/*
 * Usage Example:
 *
 * import Sidebar from './Sidebar';
 *
 * const App = () => {
 *   return (
 *     <div className="flex">
 *       <Sidebar />
 *       <main className="flex-1 p-8">
 *         // Your main content here
 *       </main>
 *     </div>
 *   );
 * };
 */
