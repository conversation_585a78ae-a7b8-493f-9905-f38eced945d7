import React from "react";
import { cn } from "../utils/cn";

type LoadingBarsProps = React.HTMLAttributes<HTMLDivElement>

// This sub-component creates a single shimmering bar.
const ShimmerBar = ({ widthClass }: { widthClass: string }) => (
  <div
    className={cn(
      "relative h-4 overflow-hidden rounded-3xl bg-[#efeeff]",
      widthClass
    )}
  >
    <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-[#f6eafc] to-transparent"></div>
  </div>
);

const LoadingBars: React.FC<LoadingBarsProps> = ({ className, ...props }) => {
  return (
    <div className={cn("flex flex-col gap-2.5", className)} {...props}>
      <ShimmerBar widthClass="w-full" />
      <ShimmerBar widthClass="w-10/12" />
      <ShimmerBar widthClass="w-8/12" />
    </div>
  );
};

export default LoadingBars;

/*
 * Usage Example:
 *
 * import LoadingBars from './LoadingBars';
 *
 * const MyComponent = () => {
 *   return (
 *     <div className="w-64">
 *       <LoadingBars />
 *     </div>
 *   );
 * };
 *
 * Note: The colors and styles are based on the Figma design.
 * The component will fill the width of its parent container.
 */
