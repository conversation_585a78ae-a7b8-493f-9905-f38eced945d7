import React from "react";
import * as RadixDialog from "@radix-ui/react-dialog";
import { cn } from "../utils/cn";
import Card from "./Card";

// Assuming you have a 'chevron-down' icon in your assets

const NotificationPanel = RadixDialog.Root;
const NotificationPanelTrigger = RadixDialog.Trigger;

const NotificationPanelContent: React.FC<RadixDialog.DialogContentProps> = ({
  className,
  children,
  ...props
}) => (
  <RadixDialog.Portal>
    {/* No Overlay for a non-modal experience */}
    <RadixDialog.Content
      {...props}
      className={cn(
        "fixed top-4 left-1/2 z-50 w-[90vw] max-w-lg -translate-x-1/2", // Re-added '-translate-x-1/2'
        "data-[state=open]:animate-fade-in-from-top data-[state=closed]:animate-fade-out-to-top",
        className
      )}
    >
      <Card className="rounded-[20px] border border-gray-200 bg-white/80 p-3.5 shadow-xl backdrop-blur-sm">
        {children}
      </Card>
    </RadixDialog.Content>
  </RadixDialog.Portal>
);

const NotificationPanelClose = RadixDialog.Close;

export {
  NotificationPanel,
  NotificationPanelTrigger,
  NotificationPanelContent,
  NotificationPanelClose,
};


/*
 * Usage Example:
 *
 * import {
 *   NotificationPanel,
 *   NotificationPanelTrigger,
 *   NotificationPanelContent,
 *   NotificationPanelClose,
 * } from './NotificationPanel';
 * import ĀagmanLogo from './ĀagmanLogo';
 * import ChevronDownIcon from '../assets/chevron-down.svg';
 * import BellIcon from '../assets/bell-01.svg'; // Example trigger icon
 *
 * const MyComponent = () => {
 *   return (
 *     <NotificationPanel>
 *       <NotificationPanelTrigger asChild>
 *         <button className="rounded-full p-2 hover:bg-gray-100">
 *           <img src={BellIcon} alt="Open Notifications" className="h-6 w-6" />
 *         </button>
 *       </NotificationPanelTrigger>
 *       <NotificationPanelContent>
 *         <div className="flex flex-col gap-3">
 *           <div className="flex items-center justify-between">
 *             <div className="flex items-center gap-1.5">
 *               <ĀagmanLogo />
 *               <p className="text-xs text-[#665beb]">Āagman</p>
 *               <p className="text-xs text-gray-500">now</p>
 *             </div>
 *             <NotificationPanelClose>
 *               <img src={ChevronDownIcon} alt="Close" className="h-4 w-4 rotate-180" />
 *             </NotificationPanelClose>
 *           </div>
 *           <div className="flex flex-col gap-1">
 *             <p className="text-[15px] font-bold text-gray-800">Infosys order executed</p>
 *             <p className="text-[13px] text-gray-600">
 *               NIFTY touched 25,000. We have successfully placed 100 shares of Infosys as per your setup.
 *             </p>
 *           </div>
 *         </div>
 *       </NotificationPanelContent>
 *     </NotificationPanel>
 *   );
 * };
 */
