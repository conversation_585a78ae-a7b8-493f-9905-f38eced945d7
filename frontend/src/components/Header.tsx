import React from "react";
import Card from "./Card";
import Pill from "./Pill";
import { cn } from "../utils/cn";
import ĀagmanIcon from "../assets/Āagman.svg";
import ClockSnoozeIcon from "../assets/clock-snooze.svg";
import PinIcon from "../assets/pin.svg";

interface HeaderProps {
  brokerName?: string;
  className?: string;
  onClockClick?: () => void;
  onPinClick?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  brokerName = "Zerodha",
  className,
  onClockClick,
  onPinClick,
}) => {
  return (
    <Card className={cn("border-b-2 border-white  bg-white", className)}>
      <div className="flex flex-row items-center p-4 gap-4">
        {/* Logo Section */}
        <div className="flex-1 flex flex-row items-start gap-2">
          {/* <PERSON>agman <PERSON>go with Gradient Background */}
          <div className="relative w-8 h-8 rounded-lg bg-gradient-to-r from-[#5c54fd] to-[#a330e5] flex items-center justify-center shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border-2 border-[rgba(255,255,255,0.12)]">
            <div className="absolute inset-0 rounded-lg shadow-[0px_0px_0px_1px_inset_rgba(16,24,40,0.18),0px_-2px_0px_0px_inset_rgba(16,24,40,0.05)]" />
            <img
              src={ĀagmanIcon}
              alt="Āagman"
              className="w-[21.333px] h-[21.333px] relative z-10"
            />
          </div>

          {/* Text and Pill Section */}
          <div className="flex flex-col gap-1 justify-center">
            <h1 className="font-bold text-[18px] leading-[26px] text-[#181e29] whitespace-nowrap">
              Āagman
            </h1>
            <Pill className="text-[12px] leading-[16px]">{brokerName}</Pill>
          </div>
        </div>

        {/* Icons Section */}
        <div className="flex flex-row gap-4 invisible">
          {/* Clock Snooze Icon */}
          <button
            onClick={onClockClick}
            className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded transition-colors"
          >
            <img src={ClockSnoozeIcon} alt="Clock Snooze" className="w-6 h-6" />
          </button>

          {/* Pin Icon */}
          <button
            onClick={onPinClick}
            className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded transition-colors relative"
          >
            <img src={PinIcon} alt="Pin" className="w-6 h-6" />
            {/* Small notification dot (optional) */}
            {/* <div className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full transform translate-x-1 -translate-y-1" /> */}
          </button>
        </div>
      </div>
    </Card>
  );
};

export default Header;
