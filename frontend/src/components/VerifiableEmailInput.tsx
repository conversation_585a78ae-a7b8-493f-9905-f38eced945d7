import React from "react";
import Input from "./Input";
import CheckCircleIcon from "../assets/check-circle.svg";
import { cn } from "../utils/cn";

interface VerifiableEmailInputProps {
  label?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  isValidEmail: boolean;
  verificationState: "unverified" | "verifying" | "otp-sent" | "verified";
  onVerifyClick: () => void;
  className?: string;
  errorMessage?: string;
  otpValue?: string; // To determine when to show verify button again
}

const VerifiableEmailInput: React.FC<VerifiableEmailInputProps> = ({
  label = "Email Address",
  value,
  onChange,
  placeholder = "Enter email address",
  isValidEmail,
  verificationState,
  onVerifyClick,
  className,
  errorMessage,
  otpValue = "",
}) => {
  const renderVerificationButton = () => {
    if (!isValidEmail) return null;

    switch (verificationState) {
      case "unverified":
        return (
          <button
            onClick={onVerifyClick}
            className="text-[#27ae60] text-sm font-medium hover:underline"
          >
            Verify
          </button>
        );

      case "verifying":
        return (
          <div className="flex items-center pt-1">
            <div className="w-5 h-5 border-2 border-[#27ae60] border-t-transparent rounded-full animate-spin" />
          </div>
        );

      case "otp-sent":
        return otpValue.length === 6 ? (
          <button
            onClick={onVerifyClick}
            className="text-[#27ae60] text-sm font-medium hover:underline"
          >
            Verify
          </button>
        ) : null;

      case "verified":
        return (
          <div className="flex items-center pt-1">
            <img src={CheckCircleIcon} alt="Verified" className="w-5 h-5" />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("space-y-2 w-full", className)}>
      <div className="relative w-full">
        <Input
          variant="text"
          label={label}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          type="email"
          className="w-full"
          inputClassName={cn(
            "pr-16 w-full", // Space for verification button/icon
            !isValidEmail && value && "border-red-500"
          )}
        />
        <div className="absolute right-3 top-1/2 pt-0.5 ">
          {renderVerificationButton()}
        </div>
      </div>
      {errorMessage && <p className="text-red-500 text-sm">{errorMessage}</p>}
    </div>
  );
};

export default VerifiableEmailInput;
