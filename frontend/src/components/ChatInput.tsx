import React, { useState, useRef, useEffect } from "react";
import { cn } from "../utils/cn";

interface ChatInputProps {
  placeholder?: string;
  onSend?: (message: string) => void;
  disabled?: boolean;
  className?: string;
  value?: string;
  onChange?: (value: string) => void;
  // Optional reason to display when disabled
  disabledReason?: string;
  isWaitingForResponse?: boolean;
}

const ChatInput: React.FC<ChatInputProps> = ({
  placeholder = "Ask anything ...",
  isWaitingForResponse = false,
  onSend,
  disabled = false,
  className,
  value: controlledValue,
  onChange,
  disabledReason,
}) => {
  const [internalValue, setInternalValue] = useState("");
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const value = controlledValue !== undefined ? controlledValue : internalValue;
  const setValue = onChange || setInternalValue;

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [value]);

  const handleSend = () => {
    if (value.trim() && !disabled) {
      onSend?.(value.trim());
      setValue("");
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (disabled) return;
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const canSend = value.trim().length > 0 && !disabled;

  return (
    <div
      className={cn(
        "flex flex-col gap-1.5 min-h-[80px] max-h-[200px] items-start justify-start w-full",
        className
      )}
    >
      <div
        className={cn(
          "w-full rounded-2xl transition-all duration-200",
          !disabled &&
            "focus-within:bg-gradient-to-r focus-within:from-[#5c54fd] focus-within:to-[#a330e5] focus-within:p-[2px] focus-within:border-1 focus-within:border-[rgba(255,255,255,0.12)] focus-within:shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
        )}
      >
        <div
          className={cn(
            "flex-1 bg-white w-full rounded-2xl relative border shadow-[0px_2px_4px_0px_rgba(0,0,0,0.08),0px_0px_2px_0px_rgba(0,0,0,0.08)]",
            disabled
              ? "border-red-200"
              : "focus-within:border-transparent border-[#dee4f0]"
          )}
        >
          {/* Prominent inline notice when disabled */}
          {disabled && (
            <div className="absolute inset-0 bg-white/80 rounded-2xl flex items-center justify-center p-4">
              <div className="text-center">
                <div className="text-red-600 font-semibold">
                  Input temporarily disabled
                </div>
                <div className="text-sm text-red-500">
                  {disabledReason || "Please wait..."}
                </div>
              </div>
            </div>
          )}
          <div className="flex flex-col items-end justify-center h-full p-3">
            {/* Textarea */}
            <textarea
              ref={textareaRef}
              value={value}
              onChange={(e) => setValue(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={disabled}
              className="w-full resize-none bg-transparent font-['Inter:Regular',_sans-serif] font-normal text-base sm:text-base leading-normal text-[#181e29] placeholder:text-[#8c9fbd] focus:outline-none flex-1 min-h-0"
              style={{
                minHeight: "1.5rem",
                maxHeight: "4.5rem",
              }}
            />

            {/* Send Button */}
            <div className="flex justify-end w-full pt-2">
              <button
                onClick={handleSend}
                disabled={!canSend}
                className={cn(
                  "border border-[#dee4f0] rounded-lg p-2 shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] transition-all duration-200",
                  isWaitingForResponse
                    ? "bg-[#5C54FD] cursor-wait"
                    : canSend
                      ? "bg-[#5C54FD] hover:bg-[#4C44E8] active:scale-95 cursor-pointer"
                      : "bg-[#e9edf6] opacity-50 cursor-not-allowed"
                )}
              >
                <div
                  className={cn(
                    "w-5 h-5 sm:w-6 sm:h-6 flex items-center justify-center",
                    canSend || isWaitingForResponse
                      ? "text-white"
                      : "text-[#8C9FBD] opacity-50"
                  )}
                >
                  {isWaitingForResponse ? (
                    <div className="bg-white size-3 rounded-sm" />
                  ) : (
                    <svg
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      aria-hidden="true"
                      className="size-6 sm:w-5 sm:h-5"
                    >
                      <path
                        d="M10.4995 13.5002L20.9995 3.00017M10.6271 13.8282L13.2552 20.5862C13.4867 21.1816 13.6025 21.4793 13.7693 21.5662C13.9139 21.6415 14.0862 21.6416 14.2308 21.5664C14.3977 21.4797 14.5139 21.1822 14.7461 20.5871L21.3364 3.69937C21.5461 3.16219 21.6509 2.8936 21.5935 2.72197C21.5437 2.57292 21.4268 2.45596 21.2777 2.40616C21.1061 2.34883 20.8375 2.45364 20.3003 2.66327L3.41258 9.25361C2.8175 9.48584 2.51997 9.60195 2.43326 9.76886C2.35809 9.91354 2.35819 10.0858 2.43353 10.2304C2.52043 10.3972 2.81811 10.513 3.41345 10.7445L10.1715 13.3726C10.2923 13.4196 10.3527 13.4431 10.4036 13.4794C10.4487 13.5115 10.4881 13.551 10.5203 13.5961C10.5566 13.647 10.5801 13.7074 10.6271 13.8282Z"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  )}
                </div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatInput;
