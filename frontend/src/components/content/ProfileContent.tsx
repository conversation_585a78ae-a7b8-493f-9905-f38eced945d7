import React, { useState } from "react";
import { useSidebarStore } from "../../stores/sidebarStore";
import { useAuthStore } from "../../stores/authStore";
import ProfileUser from "../ProfileUser";
import { openDialog } from "../../navigation/dialogNavigator";

const ProfileContent: React.FC = () => {
  const { profileData } = useSidebarStore();
  const { signOut } = useAuthStore();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleEditProfile = () => {
    openDialog("edit-profile");
  };

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      console.warn("[Extension] 🚪 Starting logout process (no web tab)...");
      await signOut();
      localStorage.removeItem("firebase_uid");
      localStorage.removeItem("sessionUserId");
      localStorage.removeItem("extension_login_completed");
      console.warn("[Extension] ✅ Logout completed successfully");
    } catch (error) {
      console.error("[Extension] ❌ Logout failed:", error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  if (!profileData) {
    return (
      <div className="flex-1 flex items-center justify-center bg-[#f4f6fa]">
        <div className="text-[#6d82a6] text-sm">Loading profile...</div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-[#f4f6fa]">
      {/* Content Container */}
      <div className="flex-1 flex flex-col gap-6 p-4">
        {/* Page Title */}
        <h1 className="font-bold text-[18px] leading-[26px] text-[#181e29]">
          Profile
        </h1>

        {/* Profile Content */}
        <div className="flex flex-col gap-8">
          {/* User Profile Card */}
          <ProfileUser
            className="hidden"
            name={profileData.user.name}
            phone={profileData.user.phone}
            email={profileData.user.email}
            onEdit={handleEditProfile}
          />

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="mt-4 inline-flex items-center justify-center gap-2 bg-white rounded-lg px-4 py-2 border border-[#f35a68] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] hover:bg-gray-50 transition-colors text-[#f03142] font-medium"
          >
            {isLoggingOut ? "Logging out..." : "Logout"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProfileContent;
