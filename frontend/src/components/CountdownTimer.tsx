import React, { useState, useEffect } from "react";
import { cn } from "../utils/cn";

interface CountdownTimerProps {
  onResend: () => void;
  isResending?: boolean;
}

const CountdownTimer: React.FC<CountdownTimerProps> = ({
  onResend,
  isResending = false,
}) => {
  const [timeRemaining, setTimeRemaining] = useState(120); // 2 minutes in seconds
  const [isActive, setIsActive] = useState(true);

  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isActive && timeRemaining > 0) {
      interval = setInterval(() => {
        setTimeRemaining((prev) => {
          if (prev <= 1) {
            setIsActive(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isActive, timeRemaining]);

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  const handleResend = () => {
    if (!isResending) {
      setTimeRemaining(120);
      setIsActive(true);
      onResend();
    }
  };

  if (isActive && timeRemaining > 0) {
    return (
      <div className="text-center text-sm">
        <span className="text-[#43556e]">Resend code in </span>
        <span className="text-[#5c54fd] font-semibold">
          {formatTime(timeRemaining)}
        </span>
      </div>
    );
  }

  return (
    <div className="text-center text-sm">
      <span className="text-[#43556e]">Didn't get a code? </span>
      <button
        onClick={handleResend}
        disabled={isResending}
        className={cn(
          "text-[#5c54fd] font-semibold hover:underline transition-colors",
          isResending && "opacity-50 cursor-not-allowed"
        )}
      >
        Click to resend
      </button>
    </div>
  );
};

export default CountdownTimer;
