import React from "react";
import { cn } from "../utils/cn";
import { formatNumber } from "../utils/formatNumber";
import Card from "./Card";

// Assuming these icons exist in your assets folder.
import TrendDownIcon from "../assets/trend-down-01.svg";
import ArrowRightIcon from "../assets/arrow-right.svg";

// First row of the card
const TopRow: React.FC<{ stockName: string; value: string | number }> = ({
  stockName,
  value,
}) => (
  <div className="flex w-full items-center justify-between font-medium text-gray-900">
    <p>{stockName}</p>
    <p>₹{formatNumber(value)}</p>
  </div>
);

// Second row of the card, with conditional trend icon
const BottomRow: React.FC<{
  details: string;
  percentageChange: number;
}> = ({ details, percentageChange }) => {
  const isPositive = percentageChange >= 0;

  return (
    <div className="flex w-full items-center justify-between text-xs">
      <p className="text-gray-500">{details}</p>
      <div className="flex items-center gap-2">
        <img
          src={TrendDownIcon}
          alt="Trend"
          className={cn(
            "h-4 w-4 transform transition-transform duration-300",
            isPositive ? "scale-y-[-1]" : "scale-y-1"
          )}
          style={{
            filter: isPositive
              ? "hue-rotate(100deg) saturate(3)" // A trick to turn red into green
              : "none",
          }}
        />
        <p className={cn(isPositive ? "text-green-600" : "text-red-600")}>
          {Math.abs(percentageChange).toFixed(1)}%
        </p>
      </div>
    </div>
  );
};

interface OrderCardProps {
  stockName: string;
  value: string | number;
  details: string;
  percentageChange: number;
  onClick: () => void;
  className?: string;
}

const OrderCard: React.FC<OrderCardProps> = ({
  stockName,
  value,
  details,
  percentageChange,
  onClick,
  className,
}) => {
  return (
    <button
      onClick={onClick}
      className={cn("group w-full text-left", className)}
    >
      <Card className="flex w-full flex-row items-center justify-between gap-4 border-gray-100 p-3 outline-none transition-colors group-active:bg-gray-200">
        <div className="flex flex-1 flex-col gap-1">
          <TopRow stockName={stockName} value={value} />
          <BottomRow details={details} percentageChange={percentageChange} />
        </div>
        <img src={ArrowRightIcon} alt="Go" className="h-5 w-5" />
      </Card>
    </button>
  );
};

export default OrderCard;

/*
 * Usage Example:
 *
 * import OrderCard from './OrderCard';
 *
 * const MyComponent = () => {
 *   return (
 *     <div className="space-y-4">
 *       <OrderCard
 *         stockName="INFOSYS"
 *         value="25000.55"
 *         details="BSE | 100/100"
 *         percentageChange={5.4}
 *         onClick={() => alert('Positive clicked')}
 *       />
 *       <OrderCard
 *         stockName="TECHM"
 *         value="12345"
 *         details="NSE | 50/50"
 *         percentageChange={-2.1}
 *         onClick={() => alert('Negative clicked')}
 *       />
 *     </div>
 *   );
 * };
 */
