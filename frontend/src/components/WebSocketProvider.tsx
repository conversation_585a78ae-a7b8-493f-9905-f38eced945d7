/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
/* eslint-disable no-empty */
/* eslint-disable react-refresh/only-export-components */
import React, { useCallback, useEffect, useRef } from "react";
import { useNetworkStore } from "../stores/networkStore";
import {
  useWebSocketStore,
  type WebSocketResponse,
} from "../stores/websocketStore";
import { useAuthStore } from "../stores/authStore";
import { useSidebarStore } from "../stores/sidebarStore";
import { getCurrentBroker } from "../utils/sessionManager";
import { executionPouchDBSyncService } from "../services/ExecutionPouchDBSyncService";
import {
  getTestFirebaseUID,
  shouldSkipAuthValidation,
} from "../config/testConfig";
import type {
  MainThreadMessage,
  TradePrimitive,
  ChromeExtensionContext,
  ConnectionStatus,
  ErrorPayload,
} from "../types";
import {
  buildMonitoringPlaceholders,
  buildOrderPlaceholders,
  isMonitoringPrimitive,
  isOrderPrimitive,
} from "../utils/executionPlaceholders";
import {
  buildOrdersUpdater,
  buildMonitoringUpdater,
} from "../utils/executionUpdaters";

// Single reusable mapper for primitive/type to display label
export const mapPrimitiveToLabel = (input: string): string => {
  const key = String(input || "")
    .toLowerCase()
    .replace(/\s+/g, "")
    .replace(/_/g, "");
  switch (key) {
    case "buy":
      return "BUY";
    case "sell":
      return "SELL";
    case "sellall":
      return "SELL ALL";
    case "placebuylimitorder":
      return "BUY LIMIT";
    case "placeselllimitorder":
      return "SELL LIMIT";
    case "placebuystoplossmarketorder":
      return "BUY SL-M";
    case "placesellstoplossmarketorder":
      return "SELL SL-M";
    case "placebuystoplosslimitorder":
      return "BUY SL-L";
    case "placesellstoplosslimitorder":
      return "SELL SL-L";
    case "exitallpositions":
      return "EXIT ALL";
    default:
      return String(input || "").toUpperCase();
  }
};

// Chrome extension API helper - now properly typed

// 🚀 Order execution is now handled directly in the WebSocket worker

// This component handles the global WebSocket connection
const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isOnline } = useNetworkStore();
  const { addMessage, setConnectionStatus, updateSessionForTab } =
    useWebSocketStore();
  const { setBrokerLoginRequired } = useWebSocketStore.getState();
  const { activeTab } = useSidebarStore();
  const { isAuthenticated, setPreventAutoRedirect, signOut } = useAuthStore();
  const hasInitialized = useRef(false);

  // Guard to ensure we only sign out once on WS failure
  const hasForcedLogoutRef = useRef(false);

  // Global monitoring watcher to keep sidebar count updated
  const hasGlobalMonitoringWatcherRef = useRef(false);
  // Track latest execution message id to update UI
  const latestExecutionMessageIdRef = useRef<string | null>(null);
  // Short-lived polling for execution updates (fallback when change feed misses)
  const executionPollIdRef = useRef<number | null>(null);
  // Track current execution id to scope updates strictly
  const latestExecutionRequestIdRef = useRef<string | null>(null);

  // Monitoring-specific refs
  const latestMonitoringMessageIdRef = useRef<string | null>(null);
  const latestMonitoringExecIdRef = useRef<string | null>(null);
  // Login status tracking
  const lastLoginStatus = useRef<boolean | null>(null);
  const loginPollId = useRef<ReturnType<typeof setInterval> | null>(null);

  // Robust login status detection with fallbacks
  const checkLoginStatus = async (): Promise<boolean | null> => {
    const chromeAPI = (globalThis as any).chrome;

    // 1. Try extension API first (most reliable)
    if (chromeAPI?.runtime?.sendMessage) {
      try {
        const resp: { success?: boolean; data?: { required: boolean } } =
          await new Promise((resolve) => {
            chromeAPI.runtime.sendMessage(
              { type: "GET_LOGIN_STATUS" },
              resolve
            );
          });

        if (resp?.success && resp?.data?.required !== undefined) {
          return resp.data.required;
        }
      } catch (error) {
        console.error("[WebSocketProvider] Extension API check failed:", error);
      }
    }

    // 2. Fallback: Direct DOM check (if on broker page)
    if (window.location.hostname.includes("zerodha.com")) {
      try {
        const loginForm = document.querySelector(".login-form form");
        return loginForm !== null;
      } catch (error) {
        console.error("[WebSocketProvider] DOM check failed:", error);
      }
    }

    return null; // Unknown state
  };

  // Start continuous login monitoring
  useEffect(() => {
    // Continuous login status monitoring
    const checkAndUpdateLoginStatus = async () => {
      try {
        const loginRequired = await checkLoginStatus();

        if (loginRequired !== null) {
          const previousStatus = lastLoginStatus.current;

          // Only update if status has changed or is initially unknown
          if (previousStatus !== loginRequired) {
            lastLoginStatus.current = loginRequired;

            // Update store state
            setBrokerLoginRequired(loginRequired);
          }
        }
      } catch (error) {
        console.error("[WebSocketProvider] Login status check failed:", error);
      }
    };

    // Immediate mount-time check with fallback: assume logged out if unknown
    const initialCheckWithFallback = async () => {
      try {
        const loginRequired = await checkLoginStatus();
        if (loginRequired !== null) {
          lastLoginStatus.current = loginRequired;
          setBrokerLoginRequired(loginRequired);
        } else {
          // Could not determine — assume logged out for safety
          lastLoginStatus.current = true;
          setBrokerLoginRequired(true);
        }
      } catch (_) {
        // On any error, assume logged out for safety
        lastLoginStatus.current = true;
        setBrokerLoginRequired(true);
      }
    };

    initialCheckWithFallback();

    // Start continuous polling every 2 seconds
    loginPollId.current = setInterval(checkAndUpdateLoginStatus, 2000);

    return () => {
      if (loginPollId.current) {
        clearInterval(loginPollId.current);
        loginPollId.current = null;
      }
    };
  }, [setBrokerLoginRequired]);

  // Bootstrap poll to hydrate initial monitoring count on first load
  const monCountBootstrapPollIdRef = useRef<number | null>(null);
  // Retry init if UID not yet available
  const execInitRetryPollIdRef = useRef<number | null>(null);

  // ===== Global execution registry and single watchers/pollers =====
  type ExecRegistryEntry = {
    updateAlerts?: (alerts: any[]) => void;
    updateOrders?: (orders: any[]) => void;
    messageIds?: { monitoringMessageId?: string; ordersMessageId?: string };
  };
  const execRegistryRef = useRef<Map<string, ExecRegistryEntry>>(new Map());
  const watchersStartedRef = useRef<{ monitoring: boolean; orders: boolean }>({
    monitoring: false,
    orders: false,
  });
  const pollersStartedRef = useRef<{ monitoring: boolean; orders: boolean }>({
    monitoring: false,
    orders: false,
  });
  const monitoringGlobalPollIdRef = useRef<number | null>(null);
  const ordersGlobalPollIdRef = useRef<number | null>(null);

  const ensureGlobalWatchersAndPollersStarted = useCallback(() => {
    try {
      if (!watchersStartedRef.current.monitoring) {
        executionPouchDBSyncService.watchMonitoringUpdates((alerts) => {
          try {
            execRegistryRef.current.forEach((entry) => {
              try {
                entry.updateAlerts?.(alerts);
              } catch (_) { }
            });
          } catch (_) { }
        });
        watchersStartedRef.current.monitoring = true;
      }
    } catch (_) { }

    try {
      if (!watchersStartedRef.current.orders) {
        executionPouchDBSyncService.watchOrderUpdates((orders) => {
          try {
            execRegistryRef.current.forEach((entry) => {
              try {
                entry.updateOrders?.(orders);
              } catch (_) { }
            });
          } catch (_) { }
        });
        watchersStartedRef.current.orders = true;
      }
    } catch (_) { }

    try {
      if (!pollersStartedRef.current.monitoring) {
        if (monitoringGlobalPollIdRef.current) {
          try {
            clearInterval(monitoringGlobalPollIdRef.current);
          } catch (_) { }
          monitoringGlobalPollIdRef.current = null;
        }
        const intervalMs = 1500;
        monitoringGlobalPollIdRef.current = setInterval(async () => {
          try {
            const alerts =
              await executionPouchDBSyncService.getMonitoringAlerts();
            execRegistryRef.current.forEach((entry) => {
              try {
                entry.updateAlerts?.(alerts);
              } catch (_) { }
            });
          } catch (_) { }
        }, intervalMs) as unknown as number;
        pollersStartedRef.current.monitoring = true;
      }
    } catch (_) { }

    try {
      if (!pollersStartedRef.current.orders) {
        if (ordersGlobalPollIdRef.current) {
          try {
            clearInterval(ordersGlobalPollIdRef.current);
          } catch (_) { }
          ordersGlobalPollIdRef.current = null;
        }
        const intervalMs = 1500;
        ordersGlobalPollIdRef.current = setInterval(async () => {
          try {
            const orders = await executionPouchDBSyncService.getOrders();
            execRegistryRef.current.forEach((entry) => {
              try {
                entry.updateOrders?.(orders);
              } catch (_) { }
            });
          } catch (_) { }
        }, intervalMs) as unknown as number;
        pollersStartedRef.current.orders = true;
      }
    } catch (_) { }
  }, []);

  // Reactively bridge zustand execution registry into provider exec registry
  const executionMessageRegistry = useWebSocketStore(
    (s) => s.executionMessageRegistry
  );
  useEffect(() => {
    const registry = executionMessageRegistry || {};
    const execIds = Object.keys(registry);

    execIds.forEach((execId) => {
      if (execRegistryRef.current.has(execId)) return; // already bridged
      const entry = registry[execId] || {};
      const monitoringMessageId = entry.monitoringMessageId as
        | string
        | undefined;
      const ordersMessageId = entry.ordersMessageId as string | undefined;

      // Orders updater
      if (ordersMessageId) {
        const updateFromOrders = buildOrdersUpdater(execId, ordersMessageId);
        // Register in provider registry so fan-out can call it
        const prev = execRegistryRef.current.get(execId) || {};
        execRegistryRef.current.set(execId, {
          ...prev,
          updateOrders: updateFromOrders,
          messageIds: {
            ...(prev as any).messageIds,
            ordersMessageId,
          },
        });
        // Seed once; ongoing updates from fan-out
        executionPouchDBSyncService
          .getOrders()
          .then((orders) => {
            try {
              updateFromOrders(orders);
            } catch (_) { }
          })
          .catch(() => { });
      }

      // Monitoring updater
      if (monitoringMessageId) {
        const updateFromAlerts = buildMonitoringUpdater(
          execId,
          monitoringMessageId
        );
        const prev = execRegistryRef.current.get(execId) || {};
        execRegistryRef.current.set(execId, {
          ...prev,
          updateAlerts: updateFromAlerts,
          messageIds: {
            ...(prev as any).messageIds,
            monitoringMessageId,
          },
        });
        executionPouchDBSyncService
          .getMonitoringAlerts()
          .then((alerts) => {
            try {
              updateFromAlerts(alerts);
            } catch (_) { }
          })
          .catch(() => { });
      }
    });

    // Ensure fan-out watchers and pollers are running after bridging
    try {
      ensureGlobalWatchersAndPollersStarted();
    } catch (_) { }
  }, [executionMessageRegistry, ensureGlobalWatchersAndPollersStarted]);

  // Store execution request in PouchDB instead of Chrome API
  const storeExecutionRequestInPouchDB = useCallback(
    async (
      primitives: TradePrimitive[],
      preferredExecId?: string
    ): Promise<string | undefined> => {
      try {
        console.warn(
          "[WebSocketProvider] 💾 Storing execution request in PouchDB"
        );

        const firebaseUID = getFirebaseUID();
        if (!firebaseUID) {
          console.error(
            "[WebSocketProvider] ❌ No Firebase UID found for execution request"
          );
          return undefined;
        }

        // Get current conversation ID
        const conversationsStr = localStorage.getItem("conversations");
        const conversations = conversationsStr
          ? JSON.parse(conversationsStr)
          : {};
        const currentBroker = getCurrentBroker();
        const messageType =
          activeTab === "orders" || activeTab === "monitoring"
            ? activeTab
            : "chat";
        const conversationId = conversations?.[currentBroker]?.[messageType];

        const executionRequest = {
          type: "execution_request" as const,
          firebase_uid: firebaseUID,
          conversation_id: conversationId || "unknown",
          status: "pending" as const,
          primitives, // Pass primitives as-is - no conversion needed
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Store in execution PouchDB
        const result = await executionPouchDBSyncService.storeExecutionRequest(
          executionRequest,
          preferredExecId
        );
        console.warn(
          `[WebSocketProvider] ✅ Execution request stored successfully: ${result}`
        );
        const createdId = typeof result === "string" ? result : undefined;

        // Wake up the service worker by sending a test message
        try {
          const chromeContext = globalThis as unknown as ChromeExtensionContext;
          if (chromeContext.chrome?.runtime?.sendMessage) {
            console.warn(
              "[WebSocketProvider] 🚀 [DEBUG] Sending wake-up message to executor service worker..."
            );
            chromeContext.chrome.runtime.sendMessage({
              type: "WAKE_UP_SERVICE_WORKER",
              timestamp: Date.now(),
            });
          }
        } catch (_error) {
          console.warn(
            "[WebSocketProvider] 🔧 [DEBUG] Chrome runtime not available (expected in web context)"
          );
        }
        return createdId;
      } catch (error) {
        console.error(
          "[WebSocketProvider] ❌ Error storing execution request:",
          error
        );
        return undefined;
      }
    },
    [activeTab]
  );

  const handleWebSocketMessage = useCallback(
    (payload: WebSocketResponse) => {
      console.log("[WebSocketProvider] Handling WebSocket message:", payload);
      console.log(
        "🚨 [EXECUTOR DEBUG] WebSocketProvider updated with executor check!"
      );

      if (payload.user_id) {
        const currentUserId = localStorage.getItem("user_id");

        // Always update if backend sends a different user_id (handles ID generation/mapping)
        if (currentUserId !== payload.user_id) {
          console.log(
            "[WebSocketProvider] 🆔 Updating user_id:",
            `${currentUserId} → ${payload.user_id}`
          );
          localStorage.setItem("user_id", payload.user_id);

          // 🔧 FIX: Immediately update WebSocket store to prevent race condition
          const { setUserId } = useWebSocketStore.getState();
          setUserId(payload.user_id);
          console.log(
            "[WebSocketProvider] ✅ Updated WebSocket store user_id immediately:",
            payload.user_id
          );
        } else {
          console.log(
            "[WebSocketProvider] 🆔 User ID unchanged:",
            payload.user_id
          );
        }
      }

      if (payload.conversation_id) {
        // Determine message type from the response or current active tab
        const messageType =
          payload.typeOfMessage ||
          (activeTab === "orders" || activeTab === "monitoring"
            ? activeTab
            : "chat");
        const conversationsStr = localStorage.getItem("conversations");
        const conversations = conversationsStr
          ? JSON.parse(conversationsStr)
          : {};
        const currentBroker = getCurrentBroker();

        const currentTabConversationId =
          conversations?.[currentBroker]?.[messageType];

        if (!currentTabConversationId) {
          console.log(
            "[WebSocketProvider] Storing new conversation_id for tab:",
            messageType,
            payload.conversation_id
          );
          const updatedConversations = {
            ...conversations,
            [currentBroker]: {
              ...conversations?.[currentBroker],
              [messageType]: payload.conversation_id,
            },
          };
          localStorage.setItem(
            "conversations",
            JSON.stringify(updatedConversations)
          );
          updateSessionForTab(messageType);

          // 🔧 NOTE: We don't update the WebSocket store conversation_id here because
          // the store only holds one conversation_id, but we need type-specific ones
          // getSessionIds() will read directly from localStorage for the specific type
        }
      }

      // Add message with the appropriate type (use typeOfMessage from response, fallback to active tab)
      const messageTab =
        payload.typeOfMessage ||
        (activeTab === "orders" || activeTab === "monitoring"
          ? activeTab
          : "chat");
      console.log(
        "[WebSocketProvider] Adding message to tab:",
        messageTab,
        "payload:",
        payload
      );
      addMessage(payload, messageTab);

      // Normalize primitives: ensure each has a stable id so executor uses our ids
      const primitivesAny: any[] = Array.isArray((payload as any).primitives)
        ? ((payload as any).primitives as any[])
        : [];
      // classification helpers imported from executionPlaceholders

      const normalizedPrimitives: any[] = primitivesAny;

      const isMonitoringAction = normalizedPrimitives.some(
        isMonitoringPrimitive
      );
      const isOrderExecution = payload.messageType === "order_execution";

      // ===== ORDERS (exclusive: only when order_execution and not monitoring) =====
      if (isOrderExecution && !isMonitoringAction) {
        const { setChatInputBlocked } = useWebSocketStore.getState();

        // Build pending placeholders from order primitives only
        const pendingFromPrimitives: Array<{
          id: string;
          symbol: string;
          status: string;
          quantity?: number;
          price?: string;
          orderType?: string;
          product?: string;
          broker?: string;
          action_id?: string;
          primitive?: string;
        }> = [];
        try {
          pendingFromPrimitives.push(
            ...buildOrderPlaceholders(normalizedPrimitives as any)
          );
        } catch (_) { }

        // Store execution request so background starts
        latestExecutionRequestIdRef.current = null;
        try {
          if (normalizedPrimitives.length > 0) {
            storeExecutionRequestInPouchDB(
              normalizedPrimitives as any,
              (payload as any)?.execution_request_id
            )
              .then((execId) => {
                if (typeof execId === "string") {
                  latestExecutionRequestIdRef.current = execId;
                  // Bind updater and register in global exec registry
                  updateFromOrders = buildOrdersUpdater(execId, hostMessageId);
                  execRegistryRef.current.set(execId, {
                    updateOrders: updateFromOrders,
                    messageIds: { ordersMessageId: hostMessageId },
                  });
                  // Seed immediately
                  executionPouchDBSyncService
                    .getOrders()
                    .then((orders) => {
                      try {
                        updateFromOrders(orders);
                      } catch (_) { }
                    })
                    .catch(() => { });
                  // Watch the execution request to unblock if needed
                  executionPouchDBSyncService.watchExecutionRequestStatus(
                    execId,
                    (status) => {
                      console.log(
                        "[EXEC-REQ] status update:",
                        status,
                        "execId:",
                        execId
                      );
                      if (status === "completed" || status === "failed") {
                        setChatInputBlocked(false);
                        // Force a final refresh to capture terminal statuses
                        executionPouchDBSyncService
                          .getOrders()
                          .then(updateFromOrders)
                          .catch(() => { });
                        // Small delayed refresh to account for replication lag
                        setTimeout(async () => {
                          try {
                            updateFromOrders(
                              await executionPouchDBSyncService.getOrders()
                            );
                          } catch (_) { }
                        }, 1500);
                      }
                    }
                  );
                }
              })
              .catch(() => { });
          }
        } catch (_) { }

        // Create bubble with placeholders
        const hostMessageId = addMessage(
          {
            id: Date.now().toString() + Math.random(),
            timestamp: Date.now(),
            data: {
              textMessage: "Executing your order(s)...",
              sender: "system",
              typeOfMessage: "chat",
              messageType: "chat_response",
              executionOrders: pendingFromPrimitives,
              executionCompleted: false,
            },
          } as any,
          "chat"
        );
        latestExecutionMessageIdRef.current = hostMessageId;

        // Block input immediately at start; subsequent updates will refine based on pending-only
        setChatInputBlocked(true);

        // Per-bubble updater; will be set once execId is known
        let updateFromOrders: (orders: any[]) => void = () => { };

        // Live updates + fallback poll
        executionPouchDBSyncService.watchOrderUpdates((orders) => {
          try {
            updateFromOrders(orders);
          } catch (e) {
            console.error("[WebSocketProvider] Update failed:", e);
          }
        });
        if (executionPollIdRef.current) {
          try {
            clearInterval(executionPollIdRef.current);
          } catch (_) { }
          executionPollIdRef.current = null;
        }
        const start = Date.now();
        const maxMs = 12000000000000;
        const intervalMs = 1500;
        executionPollIdRef.current = setInterval(async () => {
          if (Date.now() - start > maxMs) {
            try {
              clearInterval(executionPollIdRef.current!);
            } catch (_) { }
            executionPollIdRef.current = null;
            return;
          }
          try {
            updateFromOrders(await executionPouchDBSyncService.getOrders());
          } catch (_) { }
        }, intervalMs) as unknown as number;
      }

      // ===== MONITORING (exclusive: only when order_execution and monitoring primitives present) =====
      if (isOrderExecution && isMonitoringAction) {
        // no-op: updateMessageById not needed here

        // Build pending placeholders from monitoring primitives only
        const pendingFromPrimitives: Array<{
          id: string;
          symbol: string;
          status: string;
          quantity?: number;
          broker?: string;
          action_id?: string;
          primitive?: string;
          // Monitoring specific optional fields
          triggerPrice?: string | number;
          currentPrice?: string | number;
          // Marker for UI
          isMonitoring?: boolean;
          // Trigger label parts
          conditionOperator?: string;
          conditionValue?: string | number;
          // On trigger fields for order action row
          onTriggerAction?: string;
          onTriggerQuantity?: number;
          onTriggerSymbol?: string;
        }> = [];
        try {
          pendingFromPrimitives.push(
            ...buildMonitoringPlaceholders(normalizedPrimitives as any)
          );
        } catch (_) { }

        // Additionally, build order placeholders (if any) for mixed scenarios
        const pendingOrderPlaceholders: Array<{
          id: string;
          symbol: string;
          status: string;
          quantity?: number;
          price?: string;
          orderType?: string;
          product?: string;
          broker?: string;
          action_id?: string;
          primitive?: string;
        }> = [];
        try {
          normalizedPrimitives
            .filter(isOrderPrimitive)
            .forEach((p: any, idx: number) => {
              const args = p?.arguments || {};
              const rawSym = (
                args.symbol ||
                args.SYMBOL ||
                p?.symbol ||
                ""
              ).toString();
              const sym = rawSym ? rawSym.toLowerCase() : "";
              if (!sym) return;
              const qty =
                Number(args.quantity ?? args.QUANTITY ?? 0) || undefined;
              const product = (args.productType || args.PRODUCT_TYPE) as
                | string
                | undefined;
              const actionName = String(p?.action || "").toLowerCase();
              let orderType: string | undefined = "MARKET";
              if (actionName.includes("limit")) orderType = "LIMIT";
              if (
                actionName.includes("stoploss") &&
                actionName.includes("market")
              )
                orderType = "SL-M";
              if (
                actionName.includes("stoploss") &&
                actionName.includes("limit")
              )
                orderType = "SL-L";
              pendingOrderPlaceholders.push({
                id: `ord_pending_${Date.now()}_${idx}`,
                symbol: sym,
                status: "pending",
                quantity: qty,
                broker: "zerodha",
                action_id: p?.id,
                primitive: String(p?.action || "").toUpperCase(),
                product,
                orderType,
              } as any);
            });
        } catch (_) { }

        // Create monitoring bubble in chat with placeholders
        const monitoringMessageId = addMessage(
          {
            id: Date.now().toString() + Math.random(),
            timestamp: Date.now(),
            data: {
              textMessage: "Setting up a conditional order...",
              sender: "system",
              typeOfMessage: "chat",
              messageType: "chat_response",
              executionOrders: pendingFromPrimitives,
              executionCompleted: false,
              monitoringStarted: false,
            },
          } as any,
          "chat"
        );
        latestMonitoringMessageIdRef.current = monitoringMessageId;

        // Provisional bump: reflect new monitoring primitives in sidebar count immediately
        try {
          const monCount = pendingFromPrimitives.length;
          if (monCount > 0) {
            const { monitoringAlerts, setMonitoringAlertsCount } =
              useSidebarStore.getState();
            setMonitoringAlertsCount(monitoringAlerts + monCount);
          }
        } catch (_) { }

        // If there are order primitives, create an orders bubble too (no input blocking)
        const hasOrderPrims = pendingOrderPlaceholders.length > 0;
        const ordersMessageId = hasOrderPrims
          ? addMessage(
            {
              id: Date.now().toString() + Math.random(),
              timestamp: Date.now(),
              data: {
                textMessage: "Executing your order(s)...",
                sender: "system",
                typeOfMessage: "chat",
                messageType: "chat_response",
                executionOrders: pendingOrderPlaceholders,
                executionCompleted: false,
              },
            } as any,
            "chat"
          )
          : null;

        // Per-bubble monitoring alerts updater
        let updateFromAlertsFn: (alerts: any[]) => void = () => { };

        // Orders updater (no input blocking)
        let updateFromOrdersInMonitoring: (orders: any[]) => void = () => { };

        // Store execution request to start monitoring (and orders if present)
        latestMonitoringExecIdRef.current = null;
        latestExecutionRequestIdRef.current = null;
        try {
          if (normalizedPrimitives.length > 0) {
            storeExecutionRequestInPouchDB(
              normalizedPrimitives as any,
              (payload as any)?.execution_request_id
            )
              .then((execId) => {
                if (typeof execId === "string") {
                  latestMonitoringExecIdRef.current = execId;
                  latestExecutionRequestIdRef.current = execId;
                  // Bind per-execId updaters
                  updateFromAlertsFn = buildMonitoringUpdater(
                    execId,
                    monitoringMessageId
                  );
                  updateFromOrdersInMonitoring = buildOrdersUpdater(
                    execId,
                    ordersMessageId as string
                  );
                  // Register in global exec registry
                  execRegistryRef.current.set(execId, {
                    updateAlerts: updateFromAlertsFn,
                    updateOrders: ordersMessageId
                      ? updateFromOrdersInMonitoring
                      : undefined,
                    messageIds: {
                      monitoringMessageId,
                      ordersMessageId: ordersMessageId || undefined,
                    },
                  });
                  // Seed initial data via fan-out pattern
                  executionPouchDBSyncService
                    .getMonitoringAlerts()
                    .then((alerts) => {
                      try {
                        updateFromAlertsFn(alerts);
                      } catch (_) { }
                    })
                    .catch(() => { });
                  if (ordersMessageId) {
                    executionPouchDBSyncService
                      .getOrders()
                      .then((orders) => {
                        try {
                          updateFromOrdersInMonitoring(orders);
                        } catch (_) { }
                      })
                      .catch(() => { });
                  }
                }
              })
              .catch(() => { });
          }
        } catch (_) { }

        // Live monitoring updates handled by global watcher (fan-out)

        // Fallback polling handled by global poller (fan-out)
      }
    },
    [activeTab, addMessage, storeExecutionRequestInPouchDB, updateSessionForTab]
  );

  // Helper function to get Firebase UID (from auth or test config)
  const getFirebaseUID = (): string | null => {
    if (shouldSkipAuthValidation()) {
      const testUID = getTestFirebaseUID();
      console.warn("[WebSocketProvider] 🧪 Using test Firebase UID:", testUID);
      return testUID;
    }

    const firebaseUser =
      useAuthStore.getState().firebaseUser ?? useAuthStore.getState().user;
    console.warn(
      "[WebSocketProvider] AuthState contains:",
      useAuthStore.getState()
    );
    return firebaseUser?.uid || null;
  };

  useEffect(() => {
    if (!window.worker && !hasInitialized.current) {
      console.warn("[WebSocketProvider] Creating worker");
      const worker = new Worker(
        new URL("../workers/websocket.worker.ts", import.meta.url),
        { type: "module" }
      );
      window.worker = worker;
      hasInitialized.current = true;

      // Initialize execution PouchDB service
      const initializeExecutionPouchDB = async () => {
        try {
          const firebaseUID = getFirebaseUID();

          if (firebaseUID) {
            console.warn(
              "[WebSocketProvider] 🔧 Initializing execution PouchDB service with Firebase UID:",
              firebaseUID
            );
            // No auth token needed for local-only PouchDB
            try {
              console.warn(
                "[WebSocketProvider] 🚀 About to call executionPouchDBSyncService.initialize()"
              );

              let dbDetails = useAuthStore.getState().userDbDetails;
              if (!dbDetails) {
                console.warn(
                  "[WebSocketProvider] 🔍 Fetching user database details..."
                );
                const { authenticatedApiClient } = useAuthStore.getState();
                if (!authenticatedApiClient) {
                  throw new Error("Authenticated API client not available");
                }
                dbDetails = await authenticatedApiClient.auth.getUserDbDetails();

                console.warn(
                  "[WebSocketProvider] ✅ User database details fetched successfully:"
                );
                console.warn(
                  "[WebSocketProvider] CouchDB URL:",
                  dbDetails.couchdb_url
                );
                console.warn(
                  "[WebSocketProvider] CouchDB User:",
                  dbDetails.couchdb_user
                );
                console.warn(
                  "[WebSocketProvider] CouchDB Database:",
                  dbDetails.couchdb_database
                );
                console.warn("[WebSocketProvider] CouchDB Password: [HIDDEN]");
                useAuthStore.getState().setUserDbDetails(dbDetails);
              }
              await executionPouchDBSyncService.initialize(
                firebaseUID,
                dbDetails!
              );
              console.warn(
                "[WebSocketProvider] ✅ executionPouchDBSyncService.initialize() completed successfully"
              );
              // Seed monitoring badge count once based on current in-progress alerts
              try {
                const alerts =
                  await executionPouchDBSyncService.getMonitoringAlerts();
                const { setMonitoringAlertsCount } = useSidebarStore.getState();
                const count = (alerts || []).filter(
                  (a: any) =>
                    String(a?.status || "").toLowerCase() === "inprogress"
                ).length;
                setMonitoringAlertsCount(count);
              } catch (_) {
                // Ignore errors
              }

              // Bootstrap polling to hydrate count while replication catches up
              try {
                if (monCountBootstrapPollIdRef.current) {
                  try {
                    clearInterval(monCountBootstrapPollIdRef.current);
                  } catch (_) {
                    // Ignore errors
                  }
                  monCountBootstrapPollIdRef.current = null;
                }
                const start = Date.now();
                const maxMs = 20000; // up to 20s on first load
                const intervalMs = 1500;
                monCountBootstrapPollIdRef.current = setInterval(async () => {
                  if (Date.now() - start > maxMs) {
                    try {
                      clearInterval(monCountBootstrapPollIdRef.current!);
                    } catch (_) {
                      // Ignore errors
                    }
                    monCountBootstrapPollIdRef.current = null;
                    return;
                  }
                  try {
                    const alerts =
                      await executionPouchDBSyncService.getMonitoringAlerts();
                    const { setMonitoringAlertsCount } =
                      useSidebarStore.getState();
                    const count = (alerts || []).filter(
                      (a: any) =>
                        String(a?.status || "").toLowerCase() === "inprogress"
                    ).length;
                    setMonitoringAlertsCount(count);
                  } catch (_) {
                    // Ignore errors
                  }
                }, intervalMs) as unknown as number;
              } catch (_) {
                // Ignore errors
              }
              // Set up a global monitoring updates watcher (once) to update sidebar badge
              if (!hasGlobalMonitoringWatcherRef.current) {
                try {
                  executionPouchDBSyncService.watchMonitoringUpdates(
                    (alerts) => {
                      try {
                        const { setMonitoringAlertsCount } =
                          useSidebarStore.getState();
                        const newCount = (alerts || []).filter(
                          (a: any) =>
                            String(a?.status || "").toLowerCase() ===
                            "inprogress"
                        ).length;
                        setMonitoringAlertsCount(newCount);
                        // Stop bootstrap poll once live updates are flowing
                        if (monCountBootstrapPollIdRef.current) {
                          try {
                            clearInterval(monCountBootstrapPollIdRef.current);
                          } catch (_) {
                            // Ignore errors
                          }
                          monCountBootstrapPollIdRef.current = null;
                        }
                      } catch (_) {
                        // Ignore errors
                      }
                    }
                  );
                  hasGlobalMonitoringWatcherRef.current = true;
                } catch (e) {
                  console.warn(
                    "[WebSocketProvider] Failed to set global monitoring watcher:",
                    e
                  );
                }
              }

              // Ensure single watchers and pollers for fan-out updates
              ensureGlobalWatchersAndPollersStarted();
            } catch (error) {
              console.error(
                "[WebSocketProvider] ❌ Error during executionPouchDBSyncService.initialize():",
                error
              );
              throw error;
            }

            // Setup execution callback
            executionPouchDBSyncService.setExecutionCallback((request) => {
              console.warn(
                "🎯 [WebSocketProvider] Execution request received:",
                request._id
              );
              // Handle execution request changes here
            });

            console.warn(
              "[WebSocketProvider] ✅ Execution PouchDB service initialized (local only)"
            );
          } else {
            console.warn(
              "[WebSocketProvider] ⚠️ Skipping execution PouchDB initialization - no Firebase UID"
            );
            // Start a short retry loop to initialize once UID becomes available
            try {
              if (!execInitRetryPollIdRef.current) {
                const start = Date.now();
                const maxMs = 20000; // up to 20s
                const intervalMs = 1500;
                execInitRetryPollIdRef.current = setInterval(async () => {
                  if (Date.now() - start > maxMs) {
                    try {
                      clearInterval(execInitRetryPollIdRef.current!);
                    } catch (_) {
                      // Ignore errors
                    }
                    execInitRetryPollIdRef.current = null;
                    return;
                  }
                  try {
                    const uid = getFirebaseUID();
                    if (uid) {
                      try {
                        clearInterval(execInitRetryPollIdRef.current!);
                      } catch (_) {
                        // Ignore errors
                      }
                      execInitRetryPollIdRef.current = null;
                      // Attempt initialization again now that UID is present
                      await initializeExecutionPouchDB();
                    }
                  } catch (_) {
                    // Ignore errors
                  }
                }, intervalMs) as unknown as number;
              }
            } catch (_) {
              // Ignore errors
            }
          }
        } catch (error) {
          console.error(
            "[WebSocketProvider] ❌ Failed to initialize execution PouchDB:",
            error
          );
        }
      };

      initializeExecutionPouchDB();

      // Fallback: seed monitoring badge count on open even if init hasn't run yet
      try {
        if (!monCountBootstrapPollIdRef.current) {
          const start = Date.now();
          const maxMs = 20000; // up to 20s on first load
          const intervalMs = 1500;
          monCountBootstrapPollIdRef.current = setInterval(async () => {
            if (Date.now() - start > maxMs) {
              try {
                clearInterval(monCountBootstrapPollIdRef.current!);
              } catch (_) {
                // Ignore errors
              }
              monCountBootstrapPollIdRef.current = null;
              return;
            }
            try {
              const alerts =
                await executionPouchDBSyncService.getMonitoringAlerts();
              const { setMonitoringAlertsCount } = useSidebarStore.getState();
              const count = (alerts || []).filter(
                (a: any) =>
                  String(a?.status || "").toLowerCase() === "inprogress"
              ).length;
              setMonitoringAlertsCount(count);
            } catch (_) {
              // Ignore errors
            }
          }, intervalMs) as unknown as number;
        }
      } catch (_) {
        // Ignore errors
      }

      worker.onmessage = (event: MessageEvent<MainThreadMessage>) => {
        const { type, payload } = event.data;
        console.warn("[WebSocketProvider] Received message from worker:", {
          type,
          payload,
        });

        switch (type) {
          case "CONNECTION_STATUS": {
            const status = payload as ConnectionStatus;
            setConnectionStatus(status.connected);

            // If worker exhausted all reconnect attempts, force logout
            if (
              !status.connected &&
              (status as any).reason === "MAX_RECONNECTS" &&
              !hasForcedLogoutRef.current
            ) {
              hasForcedLogoutRef.current = true;
              try {
                // Avoid auto-redirect loops in web tabs (if any still open)
                setPreventAutoRedirect(false);
                console.warn(
                  "[WebSocketProvider] Max WS reconnect attempts reached - forcing logout"
                );
                signOut().catch((e) => console.warn("signOut error:", e));
              } catch (e) {
                console.warn("[WebSocketProvider] Forced logout failed:", e);
              }
            }
            break;
          }
          case "WEBSOCKET_MESSAGE":
            handleWebSocketMessage(payload as WebSocketResponse);
            break;
          case "ERROR": {
            const errorPayload = payload as ErrorPayload;
            console.warn(
              "[WebSocketProvider] Setting error:",
              errorPayload.error
            );
            const errorTab = "chat";
            const _errorMessage = {
              id: Date.now().toString() + Math.random(),
              timestamp: Date.now(),
              data: {
                textMessage: `${"Connection lost. Check your internet and try again."}`,
                sender: "system",
                typeOfMessage: errorTab,
                messageType: "chat_response",
              },
            };
            // addMessage(_errorMessage, errorTab); // optional system bubble
            break;
          }
        }
      };

      const userId =
        useWebSocketStore.getState().user_id ??
        localStorage.getItem("user_id") ??
        localStorage.getItem("sessionUserId") ??
        "";

      const connectWithCookieAuth = async () => {
        try {
          console.warn(
            "[WebSocketProvider] Sending CONNECT (cookie-auth) with userId:",
            { userId }
          );
          window.worker.postMessage({
            type: "CONNECT",
            payload: {
              userId,
            },
          });
        } catch (error) {
          console.error(
            "[WebSocketProvider] Failed to start WS connection:",
            error
          );
        }
      };

      if (isAuthenticated) {
        connectWithCookieAuth();
      }
    }

    // Don't terminate worker on cleanup - let it persist
    // The worker will be terminated when the page is actually closed
    return () => {
      console.warn(
        "[WebSocketProvider] Component cleanup - keeping worker alive"
      );
    };
  }, [
    handleWebSocketMessage,
    isAuthenticated,
    setConnectionStatus,
    ensureGlobalWatchersAndPollersStarted,
    setPreventAutoRedirect,
    signOut,
  ]);

  // 🚀 FIX: Reset WebSocket connection when authentication state changes
  useEffect(() => {
    console.warn("[WebSocketProvider] 🔄 Auth state changed:", {
      isAuthenticated,
    });

    if (window.worker && hasInitialized.current) {
      console.warn(
        "[WebSocketProvider] 🔄 Resetting WebSocket connection due to auth change"
      );

      window.worker.postMessage({ type: "DISCONNECT" });

      const userId = localStorage.getItem("user_id") || "";

      if (isAuthenticated) {
        console.warn("[WebSocketProvider] 🔑 Reconnecting (cookie-auth)");
        const connectWithCookieAuth = async () => {
          try {
            window.worker.postMessage({
              type: "CONNECT",
              payload: {
                userId,
              },
            });
          } catch (error) {
            console.error(
              "[WebSocketProvider] ❌ Failed to reconnect (cookie-auth):",
              error
            );
          }
        };
        connectWithCookieAuth();
      } else {
        console.warn(
          "[WebSocketProvider] 🔓 User logged out - not reconnecting"
        );
        setConnectionStatus(false);
      }
    }
  }, [isAuthenticated, setConnectionStatus]);

  // On mount: query broker status from background to handle fresh loads
  useEffect(() => {
    if (typeof chrome !== "undefined" && chrome.runtime?.sendMessage) {
      // Wake up SW first
      chrome.runtime.sendMessage({ type: "WAKE_UP_SERVICE_WORKER" }, () => {
        // Then do a one-shot broker status check (zerodha for now)
        chrome.runtime.sendMessage(
          { type: "BROKER_STATUS_CHECK", brokerName: "zerodha" },
          (resp: any) => {
            if (resp?.success) {
              const required = resp?.data?.required === true;
              setBrokerLoginRequired(required);
            }
          }
        );
      });
    }
  }, [addMessage, activeTab, setBrokerLoginRequired]);

  // If we previously showed login-required, re-check once on window focus to clear state after user logs in
  useEffect(() => {
    if (typeof chrome !== "undefined" && chrome.runtime?.sendMessage) {
      const onFocus = () => {
        chrome.runtime.sendMessage(
          { type: "BROKER_STATUS_CHECK", brokerName: "zerodha", force: true },
          (resp: any) => {
            if (resp?.success) {
              const required = resp?.data?.required === true;
              setBrokerLoginRequired(required);
            }
          }
        );
      };
      window.addEventListener("focus", onFocus);
      return () => window.removeEventListener("focus", onFocus);
    }
  }, [setBrokerLoginRequired]);

  // Listen for background service broadcasts about login status changes
  useEffect(() => {
    if (typeof chrome !== "undefined" && chrome.runtime?.onMessage) {
      const handleBackgroundMessage = (message: any) => {
        try {
          if (message?.type === "LOGIN_REQUIRED") {
            console.log(
              "[WebSocketProvider] Received LOGIN_REQUIRED broadcast:",
              message
            );
            setBrokerLoginRequired(true);
          } else if (message?.type === "LOGIN_RESOLVED") {
            console.log(
              "[WebSocketProvider] Received LOGIN_RESOLVED broadcast:",
              message
            );
            setBrokerLoginRequired(false);
          }
        } catch (error) {
          console.warn(
            "[WebSocketProvider] Error handling background message:",
            error
          );
        }
      };

      chrome.runtime.onMessage.addListener(handleBackgroundMessage);
      return () => {
        try {
          chrome.runtime.onMessage.removeListener(handleBackgroundMessage);
        } catch (_) { }
      };
    }
  }, [setBrokerLoginRequired]);

  // Nudge the worker to reconnect after system wake/focus/visibility online events
  useEffect(() => {
    const wake = (reason: string) => {
      try {
        if (window.worker) {
          console.warn("[WebSocketProvider] 🔔 WAKE event:", reason);
          window.worker.postMessage({ type: "WAKE", payload: { reason } });
        }
      } catch (_) { }
    };
    const onFocus = () => wake("focus");
    const onPageShow = () => wake("pageshow");
    const onVisibility = () => {
      if (document.visibilityState === "visible") wake("visibilitychange");
    };
    const onOnline = () => wake("online");

    window.addEventListener("focus", onFocus);
    window.addEventListener("pageshow", onPageShow);
    document.addEventListener("visibilitychange", onVisibility);
    window.addEventListener("online", onOnline);

    return () => {
      try {
        window.removeEventListener("focus", onFocus);
        window.removeEventListener("pageshow", onPageShow);
        document.removeEventListener("visibilitychange", onVisibility);
        window.removeEventListener("online", onOnline);
      } catch (_) { }
    };
  }, []);

  useEffect(() => {
    if (window.worker) {
      console.warn("[WebSocketProvider] Sending network status to worker:", {
        isOnline,
      });
      window.worker.postMessage({
        type: "NETWORK_STATUS_CHANGE",
        payload: { isOnline },
      });
    }
  }, [isOnline]);

  return <>{children}</>;
};

export default WebSocketProvider;
