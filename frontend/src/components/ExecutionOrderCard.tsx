import React, { useEffect, useRef, useState } from "react";
import { cn } from "../utils/cn";
import crossIcon from "../assets/x.svg";
import HourglassIcon from "../assets/hourglass-03.svg";
import CheckCircleIcon from "../assets/check-circle.svg";
import LoadingIcon from "../assets/loading-02.svg";
import TargetIcon from "../assets/target-04.svg";
import LineChartUpIcon from "../assets/line-chart-up-04.svg";
import ArrowRightIcon from "../assets/arrow-circle-broken-up-right.svg";
import OrderCardEnhanced from "./OrderCardEnhanced";

interface ExecutionOrderCardProps {
  symbol?: string;
  status?: string;
  quantity?: number | string;
  price?: string | number;
  orderType?: string;
  product?: string;
  broker?: string;
  exchange?: string;
  primitive?: string; // BUY/SELL/other
  timestamp?: string; // ISO string if available
  className?: string;
  // Monitoring specific optional fields (when card represents a monitoring alert)
  triggerPrice?: string | number;
  limitPrice?: string | number;
  currentPrice?: string | number;
  isMonitoring?: boolean;
  conditionOperator?: string;
  conditionValue?: string | number;
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  execution_details?: Record<string, string>; // NEW: detailed execution info
}

// Hardcoded CSS values derived from Figma variables (resolved offline)
const COLORS = {
  cardBg: "#ffffff", // Background-Surface-Neutral-Default
  cardBorder: "#dee4f0", // Border-Neutral-Primary
  textPrimary: "#181e29", // Text-Neutral-Primary
  textSecondary: "#43556e", // Text-Neutral-Secondary
  buyBg: "#efeeff", // Background-Surface-Brand-Primary-Tertiary
  buyText: "#5c54fd", // Text-Button-Tertiary-Default (brand)
  sellBg: "#feeaec",
  sellText: "#f03142",
  statusPendingBg: "#fff4e8",
  statusPendingText: "#c87012",
  statusInProgressBg: "#fff4e8",
  statusInProgressText: "#c87012",
  statusExecutedBg: "#e9f7ef",
  statusExecutedText: "#27ae60",
  statusCancelledBg: "#f4f6fa",
  statusCancelledText: "#6d82a6",
  statusStoppedBg: "#feeaec",
  statusStoppedText: "#f03142",
};

const STATUS_CONFIG: Record<
  string,
  { text: string; bg: string; color: string; icon: string }
> = {
  pending: {
    text: "PENDING",
    bg: COLORS.statusPendingBg,
    color: COLORS.statusPendingText,
    icon: HourglassIcon,
  },
  inprogress: {
    text: "IN PROGRESS",
    bg: COLORS.statusInProgressBg,
    color: COLORS.statusInProgressText,
    icon: LoadingIcon,
  },
  completed: {
    text: "COMPLETED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  executed: {
    text: "EXECUTED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  order_executed: {
    text: "ORDER EXECUTED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  cancelled: {
    text: "CANCELLED",
    bg: COLORS.statusStoppedBg,
    color: COLORS.statusStoppedText,
    icon: crossIcon,
  },
  rejected: {
    text: "REJECTED",
    bg: COLORS.statusStoppedBg,
    color: COLORS.statusStoppedText,
    icon: crossIcon,
  },
  triggered: {
    text: "TRIGGERED",
    bg: COLORS.statusExecutedBg,
    color: COLORS.statusExecutedText,
    icon: CheckCircleIcon,
  },
  stopped: {
    text: "STOPPED",
    bg: COLORS.statusStoppedBg,
    color: COLORS.statusStoppedText,
    icon: crossIcon,
  },
};
// A small helper to infer whether a primitive indicates monitoring
const MONITORING_ACTIONS = new Set([
  "monitorconditionthenact",
  "monitorprofit",
  "monitorsymbolfromwatchlist",
]);
function inferIsMonitoring(primitive?: string): boolean {
  const act = String(primitive || "").toLowerCase();
  if (!act) return false;
  return MONITORING_ACTIONS.has(act);
}

function formatTime(ts?: string): string | null {
  if (!ts) return null;
  try {
    const d = new Date(ts);
    const hh = `${d.getHours()}`.padStart(2, "0");
    const mm = `${d.getMinutes()}`.padStart(2, "0");
    const ss = `${d.getSeconds()}`.padStart(2, "0");
    return `${hh}:${mm}:${ss}`;
  } catch {
    return null;
  }
}

// function normalizeExchange(ex?: string): string | null {
//   if (!ex) return null;
//   const v = ex.toUpperCase();
//   if (v.includes("NSE")) return "NSE";
//   if (v.includes("BSE")) return "BSE";
//   return v; // fallback to provided uppercase value
// }

function formatNow(): string {
  const d = new Date();
  const hh = `${d.getHours()}`.padStart(2, "0");
  const mm = `${d.getMinutes()}`.padStart(2, "0");
  const ss = `${d.getSeconds()}`.padStart(2, "0");
  return `${hh}:${mm}:${ss}`;
}

const ExecutionOrderCard: React.FC<ExecutionOrderCardProps> = ({
  symbol,
  status,
  quantity,
  price,
  orderType,
  product,
  exchange,
  primitive,
  timestamp,
  className,
  triggerPrice,
  limitPrice,
  currentPrice,
  isMonitoring: isMonitoringProp,
  conditionOperator,
  conditionValue,
  onTriggerAction,
  onTriggerQuantity,
  onTriggerSymbol,
  execution_details
}) => {
  const isSell = (primitive || "").toString().toLowerCase().includes("sell");

  // Determine if this card should render the monitoring variant
  const isMonitoring =
    Boolean(isMonitoringProp) || inferIsMonitoring(primitive);

  const normalizedStatusKey = (status || "").toString().toLowerCase();
  const statusInfo = STATUS_CONFIG[normalizedStatusKey] || {
    text: (status || "").toString().toUpperCase(),
    bg: COLORS.statusCancelledBg,
    color: COLORS.statusCancelledText,
    icon: HourglassIcon,
  };

  // Per-card timestamp: only update when status changes
  const [displayTime, setDisplayTime] = useState<string>(() => {
    return formatTime(timestamp || undefined) || formatNow();
  });
  const prevStatusRef = useRef<string | undefined>(status);
  useEffect(() => {
    if (prevStatusRef.current !== status) {
      setDisplayTime(formatNow());
      prevStatusRef.current = status;
    }
  }, [status]);
  // const exchangeLabel = normalizeExchange(exchange || undefined);

  // Monitoring-specific computed labels
  const monitoringTriggerLabel = (() => {
    const s = String(symbol || "").toUpperCase();
    const isIndex = s.includes("NIFTY") || s.includes("SENSEX");
    const mapOp = (op: string) => {
      const o = String(op || "").toLowerCase();
      if (o === "gte") return ">=";
      if (o === "gt") return ">";
      if (o === "lte") return "<=";
      if (o === "lt") return "<";
      return o || ">=";
    };
    if (
      conditionOperator &&
      conditionValue !== undefined &&
      conditionValue !== null
    ) {
      const val = isIndex ? `${conditionValue}` : `₹${conditionValue}`;
      return `${s} ${mapOp(conditionOperator)} ${val}`;
    }
    if (typeof triggerPrice === "string" && triggerPrice.trim().length > 0) {
      return isIndex ? triggerPrice.replace(/₹\s*/g, "") : triggerPrice;
    }
    const trig =
      triggerPrice !== undefined &&
      triggerPrice !== null &&
      `${triggerPrice}` !== ""
        ? `${triggerPrice}`
        : undefined;
    if (trig) return `${s} > ${isIndex ? "" : "₹"}${trig}`;
    return undefined;
  })();
  const monitoringCurrentLabel = (() => {
    if (
      currentPrice === undefined ||
      currentPrice === null ||
      `${currentPrice}` === ""
    )
      return undefined;
    const s = String(symbol || "").toUpperCase();
    const isIndex = s.includes("NIFTY") || s.includes("SENSEX");
    return isIndex ? `${currentPrice}` : `₹${currentPrice}`;
  })();

  return (
    <div
      className={cn(
        "self-stretch  w-full rounded-2xl outline outline-1 inline-flex flex-col justify-start items-start gap-2",
        className,
        isMonitoring ? "p-3" : "p-1"
      )}
      style={{
        backgroundColor: COLORS.cardBg,
        outlineColor: COLORS.cardBorder,
        outlineOffset: -1,
      }}
    >
      {/* Header badges (variant by type) */}
      {isMonitoring ? (
        <div className="self-stretch flex flex-col justify-center items-start gap-1">
          <div className="self-stretch inline-flex justify-start items-center gap-4">
            <div
              className="flex-1 justify-start text-sm font-semibold font-['Inter'] leading-tight"
              style={{ color: COLORS.textPrimary }}
            >
              {String(symbol || "").toUpperCase()}
            </div>
            <div
              className="px-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
              style={{ backgroundColor: statusInfo.bg }}
            >
              <img src={statusInfo.icon} alt="" className="w-4 h-4" />
              <div className="flex justify-center items-center">
                <div
                  className="justify-start text-xs font-medium font-['Inter'] leading-none"
                  style={{ color: COLORS.textPrimary }}
                >
                  {statusInfo.text}
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <OrderCardEnhanced
          stockName={String(symbol || "")}
          value={price ?? ""}
          quantity={
            quantity !== undefined && quantity !== null ? String(quantity) : ""
          }
          time={displayTime || ""}
          exchange={String(exchange || "")}
          orderType={String(orderType || "")}
          product={String(product || "")}
          ltp={""}
          tradeType={isSell ? "SELL" : "BUY"}
          status={String(status || "")}
          className="border-none m-0 w-full p-1"
          triggerPrice={
            triggerPrice !== "0" && triggerPrice !== 0
              ? triggerPrice
              : undefined
          }
          limitPrice={
            limitPrice !== "0" && limitPrice !== 0 ? limitPrice : undefined
          }
          primitive={String(primitive || "")}
          execution_details={execution_details}
        />
      )}

      {isMonitoring ? (
        <div className="self-stretch flex flex-col justify-start items-start gap-1">
          {/* Row: Trigger condition */}
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={TargetIcon} alt="Trigger" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Trigger condition
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {monitoringTriggerLabel || "—"}
            </div>
          </div>

          {/* Row: Current value */}
          <div
            className={cn(
              "self-stretch inline-flex justify-start items-start gap-4",
              monitoringCurrentLabel === "₹0" && "hidden"
            )}
          >
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={LineChartUpIcon} alt="Current" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Current value
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {monitoringCurrentLabel || "—"}
            </div>
          </div>

          {/* Row: Order action (derived from primitive and quantity) */}
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 flex justify-start items-center gap-2">
              <img src={ArrowRightIcon} alt="Action" className="w-4 h-4" />
              <div
                className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none"
                style={{ color: COLORS.textSecondary }}
              >
                Order action
              </div>
            </div>
            <div
              className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none"
              style={{ color: COLORS.textPrimary }}
            >
              {(() => {
                const trigAct = (onTriggerAction || "")
                  .toString()
                  .toUpperCase();
                const trigQty = onTriggerQuantity ?? quantity;
                const trigSym = (onTriggerSymbol || symbol || "")
                  .toString()
                  .toUpperCase();
                if (trigAct || trigQty || trigSym) {
                  return [trigAct, trigQty, trigSym]
                    .filter(
                      (x) => x !== undefined && x !== null && `${x}` !== ""
                    )
                    .join(" ")
                    .trim();
                }
                const qtyStr =
                  quantity !== undefined && quantity !== null
                    ? String(quantity)
                    : "";
                const sym = String(symbol || "").toUpperCase();
                if (primitive) {
                  const prim = String(primitive).toUpperCase();
                  return [prim, qtyStr, sym].filter(Boolean).join(" ").trim();
                }
                return (
                  [qtyStr && "BUY", qtyStr, sym]
                    .filter(Boolean)
                    .join(" ")
                    .trim() || "—"
                );
              })()}
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
};

export default ExecutionOrderCard;
