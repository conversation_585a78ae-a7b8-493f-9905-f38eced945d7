import React from "react";
import editIcon from "../assets/edit.svg";

interface EditButtonProps {
  onClick?: () => void;
  className?: string;
}

const EditButton: React.FC<EditButtonProps> = ({ onClick, className = "" }) => {
  return (
    <button
      onClick={onClick}
      className={`inline-flex items-center gap-1 bg-transparent rounded-lg hover:bg-black/5 transition-colors ${className}`}
    >
      <img src={editIcon} alt="Edit" className="w-[18px] h-[18px]" />
      <span className="font-medium text-sm leading-5 text-[#5c54fd]">Edit</span>
    </button>
  );
};

export default EditButton;
