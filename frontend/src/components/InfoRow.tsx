import React from "react";

interface InfoRowProps {
  label: string;
  value: string;
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value }) => {
  return (
    <div className="flex flex-row gap-4 items-center justify-start w-full">
      <div className="font-normal text-[#43556e] text-left text-nowrap text-[16px] leading-[24px]">
        {label}
      </div>
      <div className="flex-1 font-medium text-[#181e29] text-right text-[16px] leading-[24px]">
        {value}
      </div>
    </div>
  );
};

export default InfoRow;
