import React from "react";
import * as RadixDialog from "@radix-ui/react-dialog";
import { cn } from "../utils/cn";

// Assuming you have an 'x' icon in your assets
// import CloseIcon from "../assets/x.svg"; // Commented out - used in DialogManager

const Dialog = RadixDialog.Root;
const DialogTrigger = RadixDialog.Trigger;

const DialogContent: React.FC<RadixDialog.DialogContentProps> = ({
  className,
  children,
  ...props
}) => {
  // Convert children to array to process them
  const childrenArray = React.Children.toArray(children);

  // Find DialogHeader and separate it from other content
  const headerChild = childrenArray.find(
    (child) => React.isValidElement(child) && child.type === DialogHeader
  );

  const otherChildren = childrenArray.filter(
    (child) => React.isValidElement(child) && child.type !== DialogHeader
  );

  return (
    <RadixDialog.Portal>
      <RadixDialog.Overlay className="fixed inset-0 bg-black/30 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
      <RadixDialog.Content
        {...props}
        className={cn(
          "fixed bottom-0 left-0 z-50 w-full bg-white shadow-lg rounded-t-lg",
          "data-[state=open]:animate-slide-in-from-bottom data-[state=closed]:animate-slide-out-to-bottom",
          "flex flex-col overflow-hidden", // Add flex layout and overflow handling
          "transform-gpu will-change-transform", // Hardware acceleration for smoother animations
          "origin-bottom", // Set transform origin for consistent animations
          className || "h-[45vh]" // Default height if no className provided
        )}
        style={{
          // Ensure consistent positioning during animation
          transform: "translateZ(0)", // Force hardware acceleration
        }}
      >
        {/* Fixed header area */}
        {headerChild}

        {/* Scrollable content area */}
        <div className="flex-1 min-h-0 overflow-y-auto">{otherChildren}</div>
      </RadixDialog.Content>
    </RadixDialog.Portal>
  );
};

const DialogHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  ...props
}) => (
  <div
    className={cn("flex items-center justify-between border-b p-4", className)}
    {...props}
  />
);

const DialogTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement>> = ({
  className,
  ...props
}) => (
  <RadixDialog.Title
    className={cn("text-lg font-semibold text-gray-900", className)}
    {...props}
  />
);

const DialogClose = RadixDialog.Close;

export {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
};

/*
 * Usage Example:
 *
 * import {
 *   Dialog,
 *   DialogTrigger,
 *   DialogContent,
 *   DialogHeader,
 *   DialogTitle,
 *   DialogClose
 * } from './Dialog';
 * import CloseIcon from '../assets/x.svg'; // Make sure you have this icon
 *
 * const MyComponent = () => {
 *   return (
 *     <Dialog>
 *       <DialogTrigger asChild>
 *         <button className="rounded-md bg-blue-500 px-4 py-2 text-white">Open Dialog</button>
 *       </DialogTrigger>
 *       <DialogContent>
 *         <DialogHeader>
 *           <DialogTitle>Profile Edit</DialogTitle>
 *           <DialogClose>
 *             <img src={CloseIcon} alt="Close" className="h-6 w-6" />
 *           </DialogClose>
 *         </DialogHeader>
 *         <div className="p-4">
 *           <p>This is the main content of the bottom sheet dialog.</p>
 *           <p>You can add any form elements or other components here.</p>
 *         </div>
 *       </DialogContent>
 *     </Dialog>
 *   );
 * };
 */
