import React from "react";
import * as RadioGroup from "@radix-ui/react-radio-group";
import { cn } from "../utils/cn";

interface RadioButtonOption {
  value: string;
  label: string;
}

interface RadioButtonProps {
  options: RadioButtonOption[];
  defaultValue?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  className?: string;
  name?: string;
}

/**
 * RadioButton component generated from Figma design tokens
 *
 * Colors derived from figma-variables-resolver.js context:
 * - Text/Brand/Secondary -> Violet/500: #a330e5
 * - Background/Surface/Brand - Secondary/Tertiary -> Violet/50: #f6eafc
 * - Border/Brand/Secondary - Inverse -> Violet/300: #daacf5
 * - Text/Neutral/Secondary -> Grey/700: #43556e
 * - Background/Surface/Neutral/Default -> White: #ffffff
 * - Border/Neutral/Primary -> Grey/300: #dee4f0
 * - Body/md/Semibold: Inter Semi Bold, 16px, 600 weight, 24px line-height
 * - Body/md/Medium: Inter Medium, 16px, 500 weight, 24px line-height
 */
const RadioButton: React.FC<RadioButtonProps> = ({
  options,
  defaultValue,
  value,
  onValueChange,
  className,
  name,
}) => {
  // Design tokens derived from Figma variables
  const brandSecondaryText = "#a330e5"; // Text/Brand/Secondary -> Violet/500
  const brandSecondaryBg = "#f6eafc"; // Background/Surface/Brand - Secondary/Tertiary -> Violet/50
  const brandSecondaryBorder = "#daacf5"; // Border/Brand/Secondary - Inverse -> Correct border color
  const neutralSecondaryText = "#43556e"; // Text/Neutral/Secondary -> Grey/700
  const neutralDefaultBg = "#ffffff"; // Background/Surface/Neutral/Default -> White
  const neutralPrimaryBorder = "#dee4f0"; // Border/Neutral/Primary -> Grey/300

  return (
    <RadioGroup.Root
      className={cn(
        // Container: mirrors Figma's "Horizontal tabs" layer
        "bg-white box-border content-stretch flex flex-row gap-0.5 items-center justify-center p-0 relative rounded-lg size-full",
        className
      )}
      style={{
        backgroundColor: neutralDefaultBg,
      }}
      defaultValue={defaultValue}
      value={value}
      onValueChange={onValueChange}
      name={name}
    >
      {/* Absolute border overlay - mirrors Figma's border layer */}
      <div
        className="absolute border border-solid inset-0 pointer-events-none rounded-lg"
        style={{ borderColor: neutralPrimaryBorder }}
      />

      {options.map((option) => (
        <RadioGroup.Item
          key={option.value}
          className={cn(
            // Base button: mirrors Figma's "_Tab button base" layer
            "basis-0 grow h-9 min-h-px min-w-px relative rounded-lg shrink-0",
            "transition-all duration-200 ease-in-out",
            "focus:outline-none focus:ring-2 focus:ring-offset-2",
            // Selected state styling
            "data-[state=checked]:shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
          )}
          style={{
            backgroundColor: "var(--radio-bg)",
            borderColor: "var(--radio-border)",
          }}
          value={option.value}
          data-testid={`radio-option-${option.value}`}
        >
          {/* Content container - mirrors Figma's inner flex container */}
          <div className="flex flex-row items-center justify-center overflow-clip relative size-full">
            <div className="box-border content-stretch flex flex-row gap-2 h-9 items-center justify-center px-3 py-2 relative w-full">
              {/* Text element - mirrors Figma's text layer with exact typography */}
              <div
                className={cn(
                  "font-['Inter'] leading-[0] not-italic relative shrink-0 text-[16px] text-left text-nowrap",
                  "transition-all duration-200 ease-in-out"
                )}
                style={{
                  color: "var(--radio-text)",
                  fontWeight: "var(--radio-weight)",
                }}
              >
                <p className="block leading-[24px] whitespace-pre">
                  {option.label}
                </p>
              </div>
            </div>
          </div>

          {/* Absolute border for selected state - mirrors Figma's selected border layer */}
          <div
            className="absolute border border-solid inset-0 pointer-events-none rounded-lg opacity-0 data-[state=checked]:opacity-100 transition-opacity duration-200"
            style={{ borderColor: brandSecondaryBorder }}
          />

          {/* CSS custom properties for state-based styling */}
          <style>{`
            [data-state="checked"] {
              --radio-bg: ${brandSecondaryBg};
              --radio-border: ${brandSecondaryBorder};
              --radio-text: ${brandSecondaryText};
              --radio-weight: 600;
            }
            [data-state="unchecked"] {
              --radio-bg: transparent;
              --radio-border: transparent;
              --radio-text: ${neutralSecondaryText};
              --radio-weight: 500;
            }
          `}</style>
        </RadioGroup.Item>
      ))}
    </RadioGroup.Root>
  );
};

export default RadioButton;

/*
 * Usage Example:
 *
 * import RadioButton from './components/RadioButton';
 *
 * const MyComponent = () => {
 *   const [selectedValue, setSelectedValue] = React.useState('open');
 *
 *   // Matches the Figma design example
 *   const options = [
 *     { value: 'open', label: 'Open' },
 *     { value: 'executed', label: 'Executed' },
 *   ];
 *
 *   return (
 *     <RadioButton
 *       options={options}
 *       value={selectedValue}
 *       onValueChange={setSelectedValue}
 *     />
 *   );
 * };
 *
 * // Custom width example
 * const CustomExample = () => {
 *   const options = [
 *     { value: 'pending', label: 'Pending' },
 *     { value: 'completed', label: 'Completed' },
 *     { value: 'cancelled', label: 'Cancelled' },
 *   ];
 *
 *   return (
 *     <RadioButton
 *       options={options}
 *       defaultValue="pending"
 *       className="w-80"
 *       onValueChange={(value) => console.warn('Selected:', value)}
 *     />
 *   );
 * };
 */
