import React, { useEffect, useRef } from "react";
import { cn } from "../utils/cn";
import UserBubble from "./UserBubble";
import ResponseBubble from "./ResponseBubble";
import LoadingBars from "./LoadingBars";
import ĀagmanIcon from "../assets/Āagman.svg";
import type {
  MessageHistory,
  WebSocketResponse,
} from "../stores/websocketStore";
import { transformPrimitivesForDisplay } from "../utils/messageUtils";
import Avatar from "./Avatar";

interface ChatMessagesViewProps {
  messages: MessageHistory[];
  isWaitingForResponse?: boolean;
  onActionClick?: (action: {
    description: string;
    type: "chat" | "orders" | "monitoring";
    message: string;
  }) => void;
  className?: string;
}

const ChatMessagesView: React.FC<ChatMessagesViewProps> = ({
  messages,
  isWaitingForResponse = false,
  onActionClick,
  className,
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll only on first render
  const hasAutoScrolledRef = useRef(false);
  useEffect(() => {
    if (hasAutoScrolledRef.current) return;
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    hasAutoScrolledRef.current = true;
  }, []);

  // Auto-scroll when a new message is appended (length increases)
  const previousLengthRef = useRef<number>(messages.length);
  useEffect(() => {
    const prev = previousLengthRef.current;
    const curr = messages.length;
    if (curr > prev) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
    previousLengthRef.current = curr;
  }, [messages.length]);

  // Helper to determine if message is from user
  const isUserMessage = (message: MessageHistory): boolean => {
    return message.data.sender === "user";
  };

  // Helper to get system message data
  const getSystemMessageData = (message: MessageHistory): WebSocketResponse => {
    return message.data as WebSocketResponse;
  };

  // Helper to transform primitives based on message type
  const transformPrimitives = (systemData: WebSocketResponse): string[] => {
    return transformPrimitivesForDisplay(
      systemData.primitives,
      systemData.messageType,
      {
        group_human_friendly_explanation: systemData.group_human_friendly_explanation,
        group_clarification_message: systemData.group_clarification_message,
      }
    );
  };

  return (
    <div className={cn("flex-1 overflow-y-auto flex gap-3", className)}>
      <div className="space-y-4 flex flex-col gap-3">
        {/* Render all messages */}
        {messages.filter((message) => getSystemMessageData(message)?.messageType !== "order_execution").map((message) => {
          if (isUserMessage(message)) {
            // Render UserBubble
            const userData = message.data as { sender: "user"; message?: string; textMessage?: string; };
            return (
              <UserBubble
                key={message.id}
                message={userData.message || userData.textMessage || ""}
              />
            );
          } else {
            // Render ResponseBubble
            const systemData = getSystemMessageData(message);
            // When messageType is order_execution, suppress primitives/actions and show override text styled as primitives
            const isOrderExecution =
              systemData.messageType === "order_execution";
            const transformedPrimitives = isOrderExecution
              ? []
              : transformPrimitives(systemData);
            const actionsToUse = isOrderExecution ? [] : systemData.actions;

            return (
              <ResponseBubble
                key={message.id}
                message={systemData.textMessage}
                primitives={transformedPrimitives}
                actions={actionsToUse}
                onActionClick={onActionClick}
                executionOrders={systemData.executionOrders}
                executionCompleted={systemData.executionCompleted}
                monitoringStarted={
                  (
                    systemData as WebSocketResponse & {
                      monitoringStarted?: boolean;
                    }
                  ).monitoringStarted
                }
                primitivesOverrideText={
                  isOrderExecution ? "Executing..." : undefined
                }
              />
            );
          }
        })}

        {/* Show LoadingBars when waiting for response */}
        {isWaitingForResponse && (
          <div className="flex justify-start w-full">
            <div className="flex flex-col items-start gap-1 w-full">
              <div className="flex items-center gap-2">
                <Avatar
                  title="AI"
                  icon={ĀagmanIcon}
                  size="md"
                  variant="aagman"
                />
                <span className="font-['Inter'] text-sm text-[#43556e]">
                  Āagman
                </span>
              </div>
              <p className="justify-start text-[#181E29] text-base font-medium font-['Inter'] leading-normal mb-2">
                Thinking...
              </p>
              <LoadingBars className="w-full" />
            </div>
          </div>
        )}

        {/* Invisible element for auto-scrolling */}
        <div ref={messagesEndRef} />
      </div>
    </div>
  );
};

export default ChatMessagesView;
