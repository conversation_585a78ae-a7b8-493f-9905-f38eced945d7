import React, { useState, useEffect } from "react";
import { useDialogStore, type Order } from "../../stores/dialogStore";
import { executionPouchDBSyncService } from "../../services/ExecutionPouchDBSyncService";
import LoadingBars from "../LoadingBars";
import RadioButton from "../RadioButton";
import Dropdown from "../Dropdown";
import OrderCardEnhanced from "../OrderCardEnhanced";
import FilterPopover from "../FilterPopover";
import EmptyState from "../EmptyState";
import CalendarIcon from "../../assets/calendar.svg";
import ChevronDownIcon from "../../assets/chevron-down.svg";
import ZerodhaIcon from "../../assets/zerodha.svg";
import { mapPrimitiveToLabel } from "../WebSocketProvider";
// import GrowwIcon from "../../assets/groww.svg";
// import UpstoxIcon from "../../assets/upstox.svg";

const AllOrdersPage: React.FC = () => {
  const { orders, isLoading, setOrders, setLoading } = useDialogStore();
  const [selectedTab, setSelectedTab] = useState<string>("open");
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedOrderTypes, setSelectedOrderTypes] = useState<string[]>([]);
  const [selectedDateRange, setSelectedDateRange] = useState<string>("today");
  const [selectedBroker, setSelectedBroker] = useState<string>("zerodha");
  const [isBrokerDropdownOpen, setIsBrokerDropdownOpen] = useState(false);

  // Load orders from PouchDB and set up real-time updates
  useEffect(() => {
    const loadOrders = async () => {
      // console.warn("🚀 [AllOrdersPage] ===== STARTING ORDER LOADING =====");
      try {
        setLoading(true);
        // console.warn("📊 [AllOrdersPage] Setting loading state to true");
        // console.warn("📊 [AllOrdersPage] Loading orders from PouchDB...");

        const realOrders = await executionPouchDBSyncService.getOrders();
        // console.warn(
        //   "📊 [AllOrdersPage] Loaded orders count:",
        //   realOrders.length
        // );
        // console.warn(
        //   "📊 [AllOrdersPage] Loaded orders details:",
        //   JSON.stringify(realOrders, null, 2)
        // );

        // console.warn("📊 [AllOrdersPage] Setting orders in dialogStore...");
        setOrders(realOrders);
        // console.warn("✅ [AllOrdersPage] Orders set in store successfully");

        // Set up real-time updates
        // console.warn(
        //   "👀 [AllOrdersPage] Setting up real-time order watcher..."
        // );
        executionPouchDBSyncService.watchOrderUpdates((updatedOrders) => {
          // console.warn("🔄 [AllOrdersPage] ===== REAL-TIME ORDER UPDATE =====");
          // console.warn(
          //   "🔄 [AllOrdersPage] Updated orders count:",
          //   updatedOrders.length
          // );
          // console.warn(
          //   "🔄 [AllOrdersPage] Updated orders:",
          //   JSON.stringify(updatedOrders, null, 2)
          // );
          // console.warn("🔄 [AllOrdersPage] Updating store...");
          setOrders(updatedOrders);
          // console.warn("✅ [AllOrdersPage] Store updated with new orders");
        });
        // console.warn("✅ [AllOrdersPage] Real-time watcher setup completed");
      } catch (_error) {
        // deliberately ignored
      } finally {
        // console.warn("📊 [AllOrdersPage] Setting loading state to false");
        setLoading(false);
        // console.warn("🎉 [AllOrdersPage] ===== ORDER LOADING COMPLETED =====");
      }
    };

    loadOrders();
  }, [setLoading, setOrders]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <LoadingBars />
      </div>
    );
  }

  // Filter orders based on selected criteria
  // console.warn(
  //   "📥 [AllOrdersPage] Orders array received:",
  //   JSON.parse(JSON.stringify(orders))
  // );

  const isWithinSelectedDateRange = (isoTs: string, range: string): boolean => {
    try {
      const date = new Date(isoTs);
      if (isNaN(date.getTime())) return false;

      const now = new Date();
      const startOfToday = new Date(now);
      startOfToday.setHours(0, 0, 0, 0);
      const endOfToday = new Date(now);
      endOfToday.setHours(23, 59, 59, 999);

      switch (range) {
        case "today": {
          return (
            date.getFullYear() === now.getFullYear() &&
            date.getMonth() === now.getMonth() &&
            date.getDate() === now.getDate()
          );
        }
        case "this_week": {
          // Monday as start of week
          const dow = (now.getDay() + 6) % 7; // 0..6 Mon..Sun
          const start = new Date(now);
          start.setDate(now.getDate() - dow);
          start.setHours(0, 0, 0, 0);
          const end = new Date(endOfToday);
          return date >= start && date <= end;
        }
        case "last_month": {
          // Interpret as last 1 month rolling window
          const start = new Date(now);
          start.setMonth(now.getMonth() - 1);
          start.setHours(0, 0, 0, 0);
          return date >= start && date <= endOfToday;
        }
        case "last_3_months": {
          const start = new Date(now);
          start.setMonth(now.getMonth() - 3);
          start.setHours(0, 0, 0, 0);
          return date >= start && date <= endOfToday;
        }
        case "last_6_months": {
          const start = new Date(now);
          start.setMonth(now.getMonth() - 6);
          start.setHours(0, 0, 0, 0);
          return date >= start && date <= endOfToday;
        }
        case "2025":
        case "2024": {
          const yr = Number(range);
          return date.getFullYear() === yr;
        }
        default:
          return true;
      }
    } catch (_) {
      return false;
    }
  };

  const getEffectiveStatus = (
    order: Order
  ):
    | "complete"
    | "rejected"
    | "cancelledAmo"
    | "open"
    | "triggerPending"
    | "amo"
    | "validationPending"
    | "cancelled"
    | (string & {}) => {
    const rawUnknown: unknown = (
      order as unknown as { broker_status?: unknown }
    )?.broker_status;
    const raw =
      typeof rawUnknown === "string" ? rawUnknown : String(rawUnknown || "");
    const s = raw.toString().trim().toUpperCase();
    if (s) {
      if (s === "COMPLETE") return "complete";
      if (s === "REJECTED") return "rejected";
      if (s === "CANCELLED AMO") return "cancelledAmo";
      if (s === "OPEN") return "open";
      if (s === "TRIGGER PENDING") return "triggerPending";
      if (s === "AMO") return "amo";
      if (s === "VALIDATION PENDING") return "validationPending";
      if (s === "CANCELLED" || s === "CANCELED") return "cancelled";
    }

    // Fallback to order.status mapping when broker_status is missing/unrecognized
    try {
      const rawStatusUnknown: unknown = (
        order as unknown as { status?: unknown }
      )?.status;
      const t = String(rawStatusUnknown || "")
        .trim()
        .toLowerCase();
      if (t) {
        if (t.includes("complete") || t.includes("execut")) return "complete";
        if (t.includes("reject")) return "rejected";
        if (t.includes("cancel")) return "cancelled"; // cancelled/canceled
        if (t === "amo") return "amo";
        if (t.includes("validation") && t.includes("pending"))
          return "validationPending";
        if (
          t === "open" ||
          t.includes("inprogress") ||
          t.includes("in progress")
        )
          return "open";
        if (t.includes("trigger") && t.includes("pending"))
          return "triggerPending";
        if (t === "pending") return "triggerPending";
      }
    } catch (_ignored) {
      // intentionally ignored: fallback to default below
    }

    return "open"; // Default safe fallback
  };

  const filteredOrders = orders.filter((order: Order) => {
    const effectiveStatus = getEffectiveStatus(order);

    const openStatuses = ["open", "triggerPending", "amo", "validationPending"];
    const isOpenOrder = openStatuses.includes(effectiveStatus);
    const isExecutedOrder = !isOpenOrder;

    const tabMatch = selectedTab === "open" ? isOpenOrder : isExecutedOrder;

    const brokerMatch = order.broker === selectedBroker;

    const statusMatch =
      selectedStatuses.length === 0 ||
      selectedStatuses.includes(effectiveStatus);

    const orderTypeMatch =
      selectedOrderTypes.length === 0 ||
      selectedOrderTypes.includes(order.type) ||
      selectedOrderTypes.includes(order.orderType);

    const dateMatch = isWithinSelectedDateRange(
      order.timestamp,
      selectedDateRange
    );

    if (order.id === orders[0]?.id) {
      // console.warn("🔍 [AllOrdersPage] Filtering debug for first order:", {
      //   selectedTab,
      //   totalOrders: orders.length,
      //   order: {
      //     id: order.id,
      //     status: order.status,
      //     broker_status: order.broker_status,
      //     effectiveStatus,
      //     type: order.type,
      //   },
      //   isOpenOrder,
      //   isExecutedOrder,
      //   tabMatch,
      //   statusMatch,
      //   orderTypeMatch,
      //   dateMatch,
      //   finalResult: tabMatch && brokerMatch && statusMatch && orderTypeMatch,
      // });
    }

    return (
      tabMatch && brokerMatch && statusMatch && orderTypeMatch && dateMatch
    );
  });

  // Debug: Log filtering results
  // console.warn("🔍 [AllOrdersPage] Filtering results:", {
  //   totalOrders: orders.length,
  //   filteredOrders: filteredOrders.length,
  //   selectedTab,
  //   selectedStatuses,
  //   selectedOrderTypes,
  //   orderStatuses: orders.map((o) => o.status),
  //   sampleOrder: orders[0],
  // });
  // console.warn(
  //   "📤 [AllOrdersPage] Filtered orders objects:",
  //   JSON.parse(JSON.stringify(filteredOrders))
  // );

  // Broker options with icons
  const brokerOptions = [
    { value: "zerodha", label: "Zerodha", icon: ZerodhaIcon },
    // { value: "groww", label: "Groww", icon: GrowwIcon },
    // { value: "upstox", label: "Upstox", icon: UpstoxIcon },
  ];

  // Radio button options
  const tabOptions = [
    { value: "open", label: "Open" },
    { value: "executed", label: "Executed" },
  ];

  // Dropdown options
  const dateRangeOptions = [
    { value: "today", label: "Today" },
    { value: "this_week", label: "This week" },
    { value: "last_month", label: "Last month" },
    { value: "last_3_months", label: "Last 3 Months" },
    { value: "last_6_months", label: "Last 6 Months" },
    { value: "2025", label: "2025" },
    { value: "2024", label: "2024" },
  ];

  const handleChatClick = (orderId: string) => {
    console.warn(`Chat clicked for order ${orderId}`);
    // Implement chat navigation logic here
  };

  const handleApplyFilters = (statuses: string[], orderTypes: string[]) => {
    console.warn("AllOrdersPage - Applying filters:", {
      statuses,
      orderTypes,
      totalOrders: orders.length,
    });
    setSelectedStatuses(statuses);
    setSelectedOrderTypes(orderTypes);
  };

  const handleBrokerSelect = (brokerValue: string) => {
    setSelectedBroker(brokerValue);
    setIsBrokerDropdownOpen(false);
  };

  const getCurrentBroker = () => {
    return brokerOptions.find((broker) => broker.value === selectedBroker);
  };

  

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Container for broker selection and main content */}
      <div className="flex flex-col h-full">
        {/* Empty State - only show when NO orders are loaded at all */}
        {orders.length === 0 && <EmptyState type="orders" className="h-full" />}

        {/* Broker Selection Component */}

        {orders.length > 0 && (
          <div className="bg-white relative border-b border-[#dee4f0]">
            <div className="flex items-center justify-between px-4 py-3">
              <div className="text-[#000000] text-base font-medium">
                Viewing orders for
              </div>
              <div className="relative">
                <button
                  className="bg-[#f4f6fa] border border-[#dee4f0] rounded-lg px-3 py-2 flex items-center gap-1 min-w-[139px]"
                  onClick={() => setIsBrokerDropdownOpen(!isBrokerDropdownOpen)}
                >
                  <img
                    src={getCurrentBroker()?.icon}
                    alt={getCurrentBroker()?.label}
                    className="w-5 h-5 rounded"
                  />
                  <div className="flex-1 px-0.5">
                    <span className="text-[#181e29] text-sm font-medium">
                      {getCurrentBroker()?.label}
                    </span>
                  </div>
                  <img
                    src={ChevronDownIcon}
                    alt="chevron down"
                    className={`w-5 h-5 transition-transform duration-200 ${
                      isBrokerDropdownOpen ? "rotate-180" : ""
                    }`}
                    style={{
                      filter:
                        "brightness(0) saturate(100%) invert(16%) sepia(73%) saturate(6341%) hue-rotate(357deg) brightness(97%) contrast(94%)",
                    }}
                  />
                </button>

                {/* Broker Dropdown */}
                {isBrokerDropdownOpen && (
                  <div className="absolute top-full left-0 mt-1 w-full bg-white border border-[#dee4f0] rounded-lg shadow-lg z-10">
                    {brokerOptions.map((broker) => (
                      <button
                        key={broker.value}
                        className={`w-full px-3 py-2 flex items-center gap-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg ${
                          selectedBroker === broker.value ? "bg-[#f4f6fa]" : ""
                        }`}
                        onClick={() => handleBrokerSelect(broker.value)}
                      >
                        <img
                          src={broker.icon}
                          alt={broker.label}
                          className="w-5 h-5 rounded"
                        />
                        <span className="text-[#181e29] text-sm font-medium">
                          {broker.label}
                        </span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        {orders.length > 0 && (
          <div className="flex flex-col gap-4 p-4">
            {/* Radio button tabs */}
            <div className="w-full">
              <RadioButton
                options={tabOptions}
                value={selectedTab}
                onValueChange={setSelectedTab}
                className="w-full"
              />
            </div>

            {/* Controls row */}
            <div className="flex items-stretch justify-between gap-2">
              <FilterPopover
                selectedStatuses={selectedStatuses}
                selectedOrderTypes={selectedOrderTypes}
                onApplyFilters={handleApplyFilters}
                className="min-w-[100px]"
              />
              <Dropdown
                options={dateRangeOptions}
                value={selectedDateRange}
                onSelect={setSelectedDateRange}
                placeholder="Date Range"
                icon={CalendarIcon}
                className="min-w-[120px]"
              />
            </div>
          </div>
        )}

        {/* Orders list */}
        {orders.length > 0 && (
          <div className="flex-1 overflow-auto">
            {filteredOrders.length === 0 ? (
              <div className="flex items-center justify-center h-64 p-4">
                <div className="text-center text-gray-500">
                  <p className="text-lg font-medium">
                    No orders match your current filters
                  </p>
                  <p className="text-sm mt-2">
                    Try adjusting your filter criteria above
                  </p>
                </div>
              </div>
            ) : (
              <div className="px-4 pb-4">
                <div className="flex flex-col gap-4">
                  {filteredOrders.map((order: Order) => (
                    <OrderCardEnhanced
                      key={order.id}
                      stockName={order.symbol}
                      value={order.price}
                      quantity={order.quantity.toString()}
                      time={new Date(order.timestamp).toLocaleTimeString()}
                      exchange="NSE"
                      primitive={mapPrimitiveToLabel(order.primitive ?? "")}
                      orderType={order.orderType}
                      product={order.product}
                      triggerPrice={order.triggerPrice !== "0" && order.triggerPrice !== 0 ? order.triggerPrice : undefined}
                      limitPrice={order.limitPrice !== "0" && order.limitPrice !== 0 ? order.limitPrice : undefined}
                      ltp={""}
                      tradeType={order.type === "buy" ? "BUY" : "SELL"}
                      execution_details={order.execution_details}
                      status={order.broker_status || getEffectiveStatus(order)}
                      onChatClick={() => handleChatClick(order.id)}
                      className="w-full"
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default AllOrdersPage;
