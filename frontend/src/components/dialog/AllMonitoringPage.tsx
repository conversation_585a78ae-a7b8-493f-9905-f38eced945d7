import React, { useEffect } from "react";
import { useDialogStore, type MonitoringAlert } from "../../stores/dialogStore";
import { executionPouchDBSyncService } from "../../services/ExecutionPouchDBSyncService";
import MonitoringAlertCard from "../MonitoringAlertCard";
import LoadingBars from "../LoadingBars";
import EmptyState from "../EmptyState";

const AllMonitoringPage: React.FC = () => {
  const { monitoringAlerts, isLoading, setMonitoringAlerts, setLoading } =
    useDialogStore();

  // Load monitoring alerts from PouchDB and set up real-time updates
  useEffect(() => {
    // Keep it simple: do not manipulate last seen or counts here

    const loadMonitoringAlerts = async () => {
      console.warn(
        "🚀 [AllMonitoringPage] ===== STARTING MONITORING LOADING ====="
      );
      try {
        setLoading(true);
        console.warn("📊 [AllMonitoringPage] Setting loading state to true");
        console.warn(
          "📊 [AllMonitoringPage] Loading monitoring alerts from PouchDB..."
        );

        const realAlerts =
          await executionPouchDBSyncService.getMonitoringAlerts();
        console.warn(
          "📊 [AllMonitoringPage] Loaded alerts count:",
          realAlerts.length
        );
        console.warn(
          "📊 [AllMonitoringPage] Loaded alerts details:",
          JSON.stringify(realAlerts, null, 2)
        );
        console.warn(
          "📥 [AllMonitoringPage] Monitoring alerts array received:",
          JSON.parse(JSON.stringify(realAlerts))
        );

        console.warn("📊 [AllMonitoringPage] Setting alerts in dialogStore...");
        setMonitoringAlerts(realAlerts);
        console.warn("✅ [AllMonitoringPage] Alerts set in store successfully");

        // Set up real-time updates
        console.warn(
          "👀 [AllMonitoringPage] Setting up real-time monitoring watcher..."
        );
        executionPouchDBSyncService.watchMonitoringUpdates((updatedAlerts) => {
          console.warn(
            "🔄 [AllMonitoringPage] ===== REAL-TIME MONITORING UPDATE ====="
          );
          console.warn(
            "🔄 [AllMonitoringPage] Updated alerts count:",
            updatedAlerts.length
          );
          console.warn(
            "🔄 [AllMonitoringPage] Updated alerts:",
            JSON.stringify(updatedAlerts, null, 2)
          );
          console.warn(
            "📤 [AllMonitoringPage] Updated monitoring alert objects:",
            JSON.parse(JSON.stringify(updatedAlerts))
          );
          console.warn("🔄 [AllMonitoringPage] Updating store...");
          setMonitoringAlerts(updatedAlerts);
          console.warn("✅ [AllMonitoringPage] Store updated with new alerts");
        });
        console.warn("✅ [AllMonitoringPage] Real-time watcher setup completed");
      } catch (error) {
        console.error(
          "❌ [AllMonitoringPage] ===== MONITORING LOADING FAILED ====="
        );
        console.error("❌ [AllMonitoringPage] Error details:", error);
        console.error(
          "❌ [AllMonitoringPage] Error stack:",
          (error as Error).stack
        );
      } finally {
        console.warn("📊 [AllMonitoringPage] Setting loading state to false");
        setLoading(false);
        console.warn(
          "🎉 [AllMonitoringPage] ===== MONITORING LOADING COMPLETED ====="
        );
      }
    };

    loadMonitoringAlerts();
  }, [setLoading, setMonitoringAlerts]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 p-4">
        <LoadingBars />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {monitoringAlerts.length === 0 ? (
        <EmptyState type="monitoring" className="h-full" />
      ) : (
        <div className="flex flex-col gap-4 p-4">
          {monitoringAlerts.map((alert: MonitoringAlert) => (
            <MonitoringAlertCard key={alert.id} alert={alert} />
          ))}
        </div>
      )}
    </div>
  );
};

export default AllMonitoringPage;
