import React from "react";
import { cn } from "../utils/cn";
import Card from "./Card";
import ArrowUpIcon from "../assets/arrow-narrow-up.svg";
import LoadingIcon from "../assets/loading-02.svg";
import HourglassIcon from "../assets/hourglass-03.svg";
import CheckCircleIcon from "../assets/check-circle.svg";
import crossIcon from "../assets/x.svg";
import infoIcon from "../assets/info-circle.svg";
import { Popover, PopoverTrigger, PopoverContent } from "@radix-ui/react-popover";

interface OrderCardProps {
  stockName: string;
  value: string | number; // Unused in new layout, kept for compatibility
  quantity: string;
  time: string;
  exchange: string;
  orderType: string;
  product: string; // NEW: product like CNC/MIS
  ltp: string | number; // Unused in new layout, kept for compatibility
  tradeType: "BUY" | "SELL";
  status: string; // Allow unknown statuses and handle gracefully
  onChatClick?: () => void; // Unused in new layout, kept for compatibility
  className?: string;
  // Newly added optional fields
  triggerPrice?: string | number;
  limitPrice?: string | number;
  // Optional primitive label (e.g., SL-L, BUY LIMIT) to override header text
  primitive?: string;
  execution_details?: {
    node_result?: {
      message?: string;
      [key: string]: unknown;
    } | string;
    [key: string]: unknown;
  };
}

const OrderCardEnhanced: React.FC<OrderCardProps> = ({
  stockName,
  value, // eslint-disable-line @typescript-eslint/no-unused-vars
  quantity,
  time,
  orderType,
  product,
  ltp, // eslint-disable-line @typescript-eslint/no-unused-vars
  tradeType,
  status,
  onChatClick, // eslint-disable-line @typescript-eslint/no-unused-vars
  className,
  triggerPrice,
  limitPrice,
  primitive,
  execution_details
}) => {
  // Prefer primitive to determine SELL/BUY orientation if provided
  
  const lowerPrim = String(primitive || "").toLowerCase();
  const derivedIsSell = lowerPrim
    ? lowerPrim.includes("sell")
    : tradeType === "SELL";
  const tradeText = tradeType === "BUY" ? "Buy" : "Sell";
  const tradePillBgStyle: React.CSSProperties = {
    backgroundColor: derivedIsSell ? "#FEEAEC" : "#EFEEFF",
  };
  const tradeTextStyle: React.CSSProperties = {
    color: derivedIsSell ? "#F03142" : "#5C54FD",
  };

  // Status pill mapping with explicit hex values
  const normalizedStatus = String(status || "").toLowerCase();
  const statusConfig = (() => {
    if (normalizedStatus === "pending" || normalizedStatus === "inprogress") {
      return {
        bg: "#fff4e8",
        text: "#c87012",
        icon: LoadingIcon,
        label: normalizedStatus === "inprogress" ? "IN PROGRESS" : "PENDING",
      };
    }
    if (normalizedStatus === "executed" || normalizedStatus === "complete") {
      return {
        bg: "#e9f7ef",
        text: "#27ae60",
        icon: CheckCircleIcon,
        label: "EXECUTED",
      };
    }
    if (
      normalizedStatus === "cancelled" ||
      normalizedStatus === "rejected" ||
      normalizedStatus === "stopped"
    ) {
      return {
        bg: "#f4f6fa",
        text: "#6d82a6",
        icon: crossIcon,
        label: (status || "").toString().toUpperCase(),
      };
    }
    return {
      bg: "#f4f6fa",
      text: "#6d82a6",
      icon: HourglassIcon,
      label: (status || "").toString().toUpperCase(),
    };
  })();

  // Combined order type display: PRODUCT    ORDER_TYPE (e.g., CNC    LIMIT)
  const productUpper = String(product || "").toUpperCase();
  const orderTypeUpper = String(orderType || "").toUpperCase();
  const orderTypeCombined =
    productUpper && orderTypeUpper
      ? `${productUpper}    ${orderTypeUpper}`
      : productUpper || orderTypeUpper || "—";

  // Compute header label text: prefer provided primitive (already mapped) else fallback
  const headerLabel = (() => {
    const prim = String(primitive || "").trim();
    if (prim) return prim.toUpperCase();
    return (
      tradeText +
      (orderTypeUpper && orderTypeUpper.toLowerCase().includes("market")
        ? ""
        : ` ${orderTypeUpper}`)
    );
  })();

  return (
    <Card
      className={cn(
        "w-96 p-3 bg-white rounded-2xl inline-flex flex-col justify-start items-start gap-2 border-[#dee4f0]",
        className
      )}
    >
      {/* Header: Trade and Status pills */}
      <div className="self-stretch inline-flex justify-between items-start">
        {/* Trade pill */}
        <div
          className={cn(
            "pl-0.5 pr-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
          )}
          style={tradePillBgStyle}
        >
          <img
            src={ArrowUpIcon}
            alt=""
            className={cn("w-4 h-4", derivedIsSell ? "rotate-180" : "")}
            style={
              derivedIsSell
                ? {
                    // red tint
                    filter:
                      "brightness(0) saturate(100%) invert(23%) sepia(79%) saturate(3483%) hue-rotate(350deg) brightness(100%) contrast(97%)",
                  }
                : {
                    // blue tint (#5C54FD approx.)
                    filter:
                      "brightness(0) saturate(100%) invert(25%) sepia(86%) saturate(1282%) hue-rotate(229deg) brightness(102%) contrast(101%)",
                  }
            }
          />
          <div className="flex justify-center items-center">
            <div
              className={cn(
                "justify-start text-xs font-medium font-['Inter'] leading-none"
              )}
              style={tradeTextStyle}
            >
              {headerLabel}
            </div>
          </div>
        </div>

        {/* Status pill */}
        <div className="gap-2 flex flex-row items-center justify-center">
          
          <Popover >
            <PopoverTrigger asChild className={typeof execution_details?.node_result === "object" && execution_details?.node_result !== null && (execution_details.node_result as { message?: string }).message
                ? ""
                : "hidden"}>
              <img
                src={infoIcon}
                alt="infoIcon"
                className="cursor-pointer"
                tabIndex={0}
                // onMouseEnter={e => e.currentTarget.click()}
                // onMouseLeave={e => {
                //   // Close popover on mouse leave
                //   const popover = e.currentTarget.closest('[data-radix-popper-content-wrapper]');
                //   if (popover) {
                //     const closeBtn = popover.querySelector('[data-radix-popover-close]');
                //     if (closeBtn) (closeBtn as HTMLElement).click();
                //   }
                // }}
                
              />
            </PopoverTrigger>
            <PopoverContent
              side="top"
              align="center"
              className="rounded-md bg-white border p-2 shadow text-xs text-[#181e29] max-w-xs"
              sideOffset={8}
            >
              
              {typeof execution_details?.node_result === "object" && execution_details?.node_result !== null
                ? (execution_details.node_result as { message?: string }).message ?? ""
                : ""}

            </PopoverContent>
          </Popover>
          
        <div
          className={cn(
            "pl-0.5 pr-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
          )}
          style={{ backgroundColor: statusConfig.bg }}
        >
          <img src={statusConfig.icon} alt="" className="w-4 h-4" />
          <div className="flex justify-center items-center">
            <div
              className={cn(
                "justify-start text-xs font-medium font-['Inter'] leading-none"
              )}
              style={{ color: statusConfig.text }}
            >
              {statusConfig.label}
            </div>
          </div>
        </div>
        </div>
      </div>

      {/* Details */}
      <div className="self-stretch flex flex-col justify-start items-start gap-1">
        <div className="self-stretch inline-flex justify-start items-start gap-4">
          <div className="flex-1 justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
            Stock
          </div>
          <div className="flex-1 text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
            {String(stockName || "").toUpperCase()}
          </div>
        </div>
        <div className="self-stretch inline-flex justify-start items-start gap-4">
          <div className="flex-1 justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            Quantity
          </div>
          <div className="flex-1 text-right justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            {quantity}
          </div>
        </div>
        <div className="self-stretch inline-flex justify-start items-start gap-4">
          <div className="flex-1 justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            Order Type
          </div>
          <div className="flex-1 text-right justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            {orderTypeCombined}
          </div>
        </div>
        <div className="self-stretch inline-flex justify-start items-start gap-4">
          <div className="flex-1 justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            Time
          </div>
          <div className="flex-1 text-right justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
            {time}
          </div>
        </div>
        {/* Trigger Price row */}
        {triggerPrice !== undefined &&
        triggerPrice !== null &&
        `${triggerPrice}` !== "" ? (
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
              Trigger Price
            </div>
            <div className="flex-1 text-right justify-start text-[#181e29] text-xs font-normal font-['Inter'] leading-none">
                {`₹${triggerPrice}`}
            </div>
          </div>
        ) : null}
        {/* Limit Price row */}
        {limitPrice !== undefined &&
        limitPrice !== null &&
        `${limitPrice}` !== "" ? (
          <div className="self-stretch inline-flex justify-start items-start gap-4">
            <div className="flex-1 justify-start text-[#43556e] text-xs font-normal font-['Inter'] leading-none">
              Limit Price
            </div>
            <div className="flex-1 text-right justify-start text-[#181e29] text-xs font-normal font-['Inter'] leading-none">
              {`₹${limitPrice}`}
            </div>
          </div>
        ) : null}
      </div>

      {/* Divider intentionally removed per latest design */}
    </Card>
  );
};

export default OrderCardEnhanced;
