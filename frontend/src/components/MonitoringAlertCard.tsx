import React, { useEffect, useRef, useState } from "react";
import * as Accordion from "@radix-ui/react-accordion";
import { cn } from "../utils/cn";
import type { MonitoringAlert } from "../stores/dialogStore";
import xIcon from "../assets/x.svg";

// Extended MonitoringAlert with additional runtime fields
type ExtendedMonitoringAlert = MonitoringAlert & {
  updatedAt?: string;
  description?: string;
  orderType?: string;
  onTriggerAction?: string | number;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  conditionOperator?: string;
};
// import { useDialogStore } from "../stores/dialogStore"; // Commented out with stop functionality

// Icons
import TargetIcon from "../assets/target-04.svg";
import LineChartUpIcon from "../assets/line-chart-up-04.svg";
import ChevronDownIcon from "../assets/chevron-down.svg";
import ArrowRightIcon from "../assets/arrow-circle-broken-up-right.svg";
import LoadingIcon from "../assets/loading-02.svg";
import HourglassIcon from "../assets/hourglass-03.svg";
import CheckIcon from "../assets/check-circle.svg";
import StopMonitoringDialog from "./StopMonitoringDialog";
import { executionPouchDBSyncService } from "../services/ExecutionPouchDBSyncService";
// import CloseIcon from "../assets/x.svg"; // Commented out with stop functionality

const MonitoringAlertCard: React.FC<{
  alert: ExtendedMonitoringAlert;
  className?: string;
}> = ({ alert, className }) => {
  // const { stopAlert } = useDialogStore(); // Commented out with stop functionality

  // const handleStopAlert = () => {
  //   stopAlert(alert.id);
  // };

  // Track status change time (local-only, independent of PouchDB)

  const [stopMonitoringDialogOpen, setStopMonitoringDialogOpen] =
    useState(false);

  const formatStatusTime = (d: Date) => {
    const day = d.getDate().toString().padStart(2, "0");
    const months = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];
    const mon = months[d.getMonth()];
    const year = d.getFullYear();
    const hh = d.getHours().toString().padStart(2, "0");
    const mm = d.getMinutes().toString().padStart(2, "0");
    return `${day} ${mon} ${year}, ${hh}:${mm}`;
  };
  const [statusChangedAt, setStatusChangedAt] = useState<string>(() =>
    formatStatusTime(new Date())
  );
  const prevStatusRef = useRef<string | undefined>(alert.status);
  useEffect(() => {
    if (prevStatusRef.current !== alert.status) {
      setStatusChangedAt(formatStatusTime(new Date()));
      prevStatusRef.current = alert.status;
    }
  }, [alert.status]);

  const persistedUpdatedAt = (() => {
    const iso = alert.updatedAt;
    if (!iso) return undefined;
    const d = new Date(iso);
    if (isNaN(d.getTime())) return undefined;
    return formatStatusTime(d);
  })();

  // Header pill (symbol + status)
  const getHeaderStatusPill = (statusRaw: string) => {
    const s = String(statusRaw || "").toLowerCase();
    if (s === "pending")
      return {
        bg: "#fff4e8",
        color: "#c87012",
        icon: HourglassIcon,
        text: "PENDING",
      };
    if (s === "inprogress")
      return {
        bg: "#fff4e8",
        color: "#c87012",
        icon: LoadingIcon,
        text: "IN PROGRESS",
      };
    if (s === "triggered")
      return {
        bg: "#e9f7ef",
        color: "#27ae60",
        icon: CheckIcon,
        text: "TRIGGERED",
      };
    if (s === "stopped")
      return {
        bg: "#feeaec",
        color: "#f03142",
        icon: xIcon,
        text: "STOPPED",
      };
    if (s === "order_executed")
      return {
        bg: "#e9f7ef",
        color: "#27ae60",
        icon: CheckIcon,
        text: "ORDER EXECUTED",
      };
    if (s === "cancelled")
      return {
        bg: "#f4f6fa",
        color: "#6d82a6",
        icon: xIcon,
        text: "CANCELLED",
      };
    if (s === "rejected")
      return {
        bg: "#f4f6fa",
        color: "#6d82a6",
        icon: xIcon,
        text: "REJECTED",
      };
    return {
      bg: "#f4f6fa",
      color: "#6d82a6",
      icon: HourglassIcon,
      text: (statusRaw || "").toString().toUpperCase(),
    };
  };

  const formatTriggerLabel = (): string => {
    const sym = String(alert.symbol || "").toUpperCase();
    const trigRaw = String(alert.triggerPrice || "");
    if (!trigRaw) return "—";
    const isIndex = sym.includes("NIFTY") || sym.includes("SENSEX");
    // Determine operator from conditionOperator; default to ">"
    const op = (() => {
      const raw = String(alert.conditionOperator || "").toLowerCase();
      if (raw === "gte") return ">=";
      if (raw === "gt") return ">";
      if (raw === "lte") return "<=";
      if (raw === "lt") return "<";
      return ">";
    })();
    // If already formatted string, normalize currency for indices
    if (/[<>]=?/.test(trigRaw) || trigRaw.includes("₹")) {
      const normalized = isIndex ? trigRaw.replace(/₹\s*/g, "") : trigRaw;
      return normalized;
    }
    // Build default label
    return `${sym} ${op} ${isIndex ? "" : "₹"}${trigRaw}`;
  };

  const formatCurrentLabel = (): string | undefined => {
    const cur = String(alert.currentPrice || "");
    if (!cur || cur === "₹0" || cur === "0") return undefined;
    return cur.startsWith("₹") ? cur : `₹${cur}`;
  };

  const extractOrderAction = (): string => {
    const act = String(alert.onTriggerAction || "").toUpperCase();
    const qty = alert.onTriggerQuantity;
    const sym = String(
      alert.onTriggerSymbol || alert.symbol || ""
    ).toUpperCase();
    const parts = [act, qty, sym].filter(
      (x) => x !== undefined && x !== null && `${x}` !== ""
    );
    if (parts.length > 1) return parts.join(" ");
    // Fallback to description if structured fields absent
    const desc = String(alert.description || "");
    const m1 = desc.match(/Will\s+(buy|sell)\s+(\d+)\s+([A-Za-z0-9.&-]+)/i);
    if (m1) return `${m1[1].toUpperCase()} ${m1[2]} ${m1[3].toUpperCase()}`;
    const m2 = desc.match(
      /Action:\s*(Buy|Sell).*?Quantity:\s*(\d+).*?Stock:\s*([A-Za-z0-9.&-]+)/is
    );
    if (m2) return `${m2[1].toUpperCase()} ${m2[2]} ${m2[3].toUpperCase()}`;
    const orderType = String(alert.orderType || "").toUpperCase();
    return orderType || "—";
  };

  const handleStopAlert = async (alert: ExtendedMonitoringAlert) => {
    try {
      await executionPouchDBSyncService.stopMonitoringAlert(alert.id);
    } catch (e) {
      console.error("Failed to stop monitoring alert", e);
    }
  };

  return (
    <Accordion.Root
      type="single"
      collapsible
      className={cn("w-full", className)}
    >
      <Accordion.Item value={alert.id} className="border-none">
        <div className="bg-white rounded-2xl border border-[#dee4f0] overflow-hidden">
          {/* Header Section */}
          {/* <div className="bg-[#f4f6fa] px-3 py-3">
            <p className="text-sm text-[#181e29] leading-5">
              {alert.description}
            </p>
          </div> */}

          {/* Main Content (Monitoring layout) */}
          <div className="p-3">
            {/* Header badges */}
            <div className="self-stretch flex flex-col justify-center items-start gap-1 pb-2">
              <div className="self-stretch inline-flex justify-start items-center gap-4">
                <div className="flex-1 justify-start text-sm font-semibold font-['Inter'] leading-tight text-[#181e29]">
                  {String(alert.symbol || "").toUpperCase()}
                </div>
                {(() => {
                  const pill = getHeaderStatusPill(String(alert.status || ""));
                  return (
                    <div
                      className="px-1 py-0.5 rounded-lg flex justify-center items-center gap-1 overflow-hidden"
                      style={{ backgroundColor: pill.bg }}
                    >
                      <img src={pill.icon} alt="" className="w-4 h-4" />
                      <div className="flex justify-center items-center">
                        <div
                          className="justify-start text-xs font-medium font-['Inter'] leading-none"
                          style={{ color: pill.color }}
                        >
                          {pill.text}
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </div>
              <div className="w-full place-items-end ">
                <div className="text-xs text-[#43556e]">
                  {persistedUpdatedAt || statusChangedAt}
                </div>
              </div>
            </div>

            {/* Rows */}
            <div className="self-stretch flex flex-col justify-start items-start gap-2 mt-1">
              {/* Trigger condition */}
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="flex-1 flex justify-start items-center gap-2">
                  <img src={TargetIcon} alt="Trigger" className="w-4 h-4" />
                  <div className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none text-[#43556e]">
                    Trigger condition
                  </div>
                </div>
                <div className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none text-[#181e29]">
                  {formatTriggerLabel()}
                </div>
              </div>

              {/* Current value */}
              {(() => {
                const cur = formatCurrentLabel();
                if (!cur) return null;
                return (
                  <div className="self-stretch inline-flex justify-start items-start gap-4">
                    <div className="flex-1 flex justify-start items-center gap-2">
                      <img
                        src={LineChartUpIcon}
                        alt="Current"
                        className="w-4 h-4"
                      />
                      <div className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none text-[#43556e]">
                        Current value
                      </div>
                    </div>
                    <div className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none text-[#181e29]">
                      {cur}
                    </div>
                  </div>
                );
              })()}

              {/* Order action */}
              <div className="self-stretch inline-flex justify-start items-start gap-4">
                <div className="flex-1 flex justify-start items-center gap-2">
                  <img src={ArrowRightIcon} alt="Action" className="w-4 h-4" />
                  <div className="flex-1 justify-start text-xs font-normal font-['Inter'] leading-none text-[#43556e]">
                    Order action
                  </div>
                </div>
                <div className="flex-1 text-right justify-start text-xs font-medium font-['Inter'] leading-none text-[#181e29]">
                  {extractOrderAction()}
                </div>
              </div>
            </div>
          </div>

          {/* Status Bar */}
          <div className="border-t border-[#dee4f0] px-3 py-3">
            <div className="flex items-center justify-between">
              {/* <div className="text-xs text-[#43556e] invisible">
                {persistedUpdatedAt || statusChangedAt}
              </div> */}
              <div
                className={cn(
                  "w-fit flex flex-row gap-1 text-[14px] text-[#F03142] font-medium text-center justify-center items-center",
                  alert.status === "stopped" ||
                    alert.status === "cancelled" ||
                    alert.status === "rejected" ||
                    alert.status === "order_executed" ||
                    alert.status === "triggered"
                    ? "invisible"
                    : ""
                )}
              >
                <img
                  src={xIcon}
                  style={{
                    filter:
                      "invert(23%) sepia(99%) saturate(7472%) hue-rotate(346deg) brightness(97%) contrast(108%)",
                  }}
                  alt="Stop"
                />
                <StopMonitoringDialog
                  open={stopMonitoringDialogOpen}
                  onOpenChange={() =>
                    setStopMonitoringDialogOpen((val) => !val)
                  }
                  targetName={String(alert.symbol || "").toUpperCase()}
                  handleSubmit={() => {
                    handleStopAlert(alert);
                  }}
                >
                  <button>Stop</button>
                </StopMonitoringDialog>
              </div>

              <div className="flex items-center gap-2">
                {/* TODO: need to discuss this functionality */}
                {/* {alert.status === "pending" && (
                  <button
                    onClick={handleStopAlert}
                    className="bg-white rounded-lg border border-[#f03142] px-3 py-1 flex items-center gap-1 hover:bg-red-50 transition-colors"
                  >
                    <img src={CloseIcon} alt="Stop" className="w-4 h-4" />
                    <span className="text-sm font-medium text-[#f03142]">Stop</span>
                  </button>
                )} */}

                <Accordion.Trigger className="bg-white rounded-lg px-3 py-1 flex items-center gap-1 hover:bg-gray-50 transition-colors group">
                  <span className="text-sm font-medium text-[#5c54fd] group-data-[state=open]:text-[#5c54fd]">
                    <span className="group-data-[state=open]:hidden">
                      View more
                    </span>
                    <span className="group-data-[state=closed]:hidden">
                      Hide Detail
                    </span>
                  </span>
                  <img
                    src={ChevronDownIcon}
                    alt="Expand"
                    className="w-6 h-6 transition-transform group-data-[state=open]:rotate-180"
                  />
                </Accordion.Trigger>
              </div>
            </div>
          </div>

          {/* Expandable Content */}
          <Accordion.Content className="overflow-hidden data-[state=open]:animate-slideDown data-[state=closed]:animate-slideUp">
            <div className="px-3 py-2">
              <div className="p-3 bg-white rounded-2xl border border-[#dee4f0] flex flex-col gap-2">
                <div className="self-stretch flex flex-col justify-center items-start gap-1">
                  <div className="self-stretch inline-flex justify-start items-center gap-4">
                    <div className="text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
                      Others Info
                    </div>
                  </div>
                </div>

                {/* Product */}
                {alert.product && (
                  <div className="self-stretch flex flex-col justify-start items-start gap-1">
                    <div className="self-stretch inline-flex justify-start items-start gap-4">
                      <div className="flex-1 justify-start text-[#43556e] text-sm font-normal font-['Inter'] leading-tight">
                        Product
                      </div>
                      <div className="flex-1 text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
                        {String(alert.product).toUpperCase()}
                      </div>
                    </div>
                  </div>
                )}

                {/* Order type */}
                {alert.orderType && (
                  <div className="self-stretch flex flex-col justify-start items-start gap-1">
                    <div className="self-stretch inline-flex justify-start items-start gap-4">
                      <div className="flex-1 justify-start text-[#43556e] text-sm font-normal font-['Inter'] leading-tight">
                        Order type
                      </div>
                      <div className="flex-1 text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
                        {String(alert.orderType).toUpperCase()}
                      </div>
                    </div>
                  </div>
                )}

                {/* Trigger price */}
                {alert.triggerPrice && (
                  <div className="self-stretch flex flex-col justify-start items-start gap-1">
                    <div className="self-stretch inline-flex justify-start items-start gap-4">
                      <div className="flex-1 justify-start text-[#43556e] text-sm font-normal font-['Inter'] leading-tight">
                        Trigger price
                      </div>
                      <div className="flex-1 text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
                        {String(alert.triggerPrice).startsWith("₹")
                          ? alert.triggerPrice
                          : `₹${alert.triggerPrice}`}
                      </div>
                    </div>
                  </div>
                )}

                {/* Limit price */}
                {alert.limitPrice && (
                  <div className="self-stretch flex flex-col justify-start items-start gap-1">
                    <div className="self-stretch inline-flex justify-start items-start gap-4">
                      <div className="flex-1 justify-start text-[#43556e] text-sm font-normal font-['Inter'] leading-tight">
                        Limit price
                      </div>
                      <div className="flex-1 text-right justify-start text-[#181e29] text-sm font-medium font-['Inter'] leading-tight">
                        {String(alert.limitPrice).startsWith("₹")
                          ? alert.limitPrice
                          : `₹${alert.limitPrice}`}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </Accordion.Content>
        </div>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default MonitoringAlertCard;
