import { useCallback } from "react";
import { useWebSocketStore } from "../stores/websocketStore";
import { getCurrentBroker, getSessionIds } from "../utils/sessionManager";

// Helper function to create a user message
const createUserMessage = (message: string) => {
  return {
    id: Date.now().toString() + Math.random(),
    timestamp: Date.now(),
    data: {
      message,
      sender: "user" as const,
    },
  };
};

export const useWebSocket = (tabType: "chat" | "orders" | "monitoring") => {
  const {
    isConnected,
    connectionStatus,
    error,
    chatMessages,
    orderMessages,
    monitoringMessages,
    chatQueue,
    orderQueue,
    monitoringQueue,
    selectedModelId,
    setSelectedModel,
    addMessage,
    setWaitingForResponse,
    isWaitingForResponse,
  } = useWebSocketStore();

  // Get messages and queue for current tab
  const messages =
    tabType === "chat"
      ? chatMessages
      : tabType === "orders"
        ? orderMessages
        : monitoringMessages;

  const queueLength =
    tabType === "chat"
      ? chatQueue.length
      : tabType === "orders"
        ? orderQueue.length
        : monitoringQueue.length;

  // Send message through worker
  const sendMessage = useCallback(
    (message: string, modelId?: string) => {
      // Guard: prevent sending when broker requires login
      if (useWebSocketStore.getState().brokerLoginRequired) {
        return;
      }
      if (window.worker) {
        // Add user message to UI immediately
        const userMessage = createUserMessage(message);
        addMessage(userMessage, tabType);

        // Set waiting state for current tab
        setWaitingForResponse(tabType);

        // Get current broker and session IDs
        const brokerName = getCurrentBroker();
        const sessionIds = getSessionIds(brokerName, tabType);

        console.warn(`[useWebSocket] 🔍 getSessionIds returned:`, sessionIds);
        console.warn(
          `[useWebSocket] 🔍 About to send WebSocket message with:`,
          {
            user_id: sessionIds?.user_id || "",
            conversation_id: sessionIds?.conversation_id || "",
            brokerName,
            tabType,
          }
        );

        window.worker.postMessage({
          type: "SEND_MESSAGE",
          payload: {
            message,
            typeOfMessage: tabType,
            modelId: modelId || selectedModelId, // Use passed modelId or fallback to selectedModelId
            user_id: sessionIds?.user_id || "",
            conversation_id: sessionIds?.conversation_id || "",
            brokerName,
            sender: "user",
          },
        });
      }
    },
    [tabType, addMessage, selectedModelId, setWaitingForResponse]
  );

  // Trigger action through worker
  const triggerAction = useCallback(
    (action: {
      description: string;
      type: "chat" | "orders" | "monitoring";
      message: string;
    }) => {
      if (window.worker) {
        // Add user action message to UI immediately
        const userMessage = createUserMessage(action.message);
        addMessage(userMessage, action.type);

        // Set waiting state for action tab
        setWaitingForResponse(action.type);

        // Get current broker and session IDs
        const brokerName = getCurrentBroker();
        const sessionIds = getSessionIds(brokerName, action.type);

        window.worker.postMessage({
          type: "SEND_MESSAGE",
          payload: {
            message: action.message,
            typeOfMessage: action.type,
            modelId: selectedModelId, // Add modelId for action messages
            isAction: true,
            description: action.description,
            user_id: sessionIds?.user_id || "",
            conversation_id: sessionIds?.conversation_id || "",
            brokerName,
            sender: "user",
          },
        });
      }
    },
    [addMessage, selectedModelId, setWaitingForResponse]
  );

  return {
    messages,
    queueLength,
    isConnected,
    connectionStatus,
    error,
    sendMessage,
    triggerAction,
    selectedModelId,
    setSelectedModel,
    isWaitingForResponse: isWaitingForResponse(tabType),
  };
};
