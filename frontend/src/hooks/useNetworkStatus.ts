import { useEffect } from "react";
import { useNetworkStore } from "../stores/networkStore";

export const useNetworkStatus = () => {
  const { isOnline, setOnlineStatus, checkNetworkStatus } = useNetworkStore();

  useEffect(() => {
    // Check current network status immediately on mount
    checkNetworkStatus();

    // Set up event listeners for network changes
    const handleOnline = () => {
      setOnlineStatus(true);
    };

    const handleOffline = () => {
      setOnlineStatus(false);
    };

    // Add event listeners
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    // Cleanup function
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [checkNetworkStatus, setOnlineStatus]); // Empty dependency array - only run once on mount

  return {
    isOnline,
    checkNetworkStatus,
  };
};
