// Simple Execution PouchDB Service
// Minimal service for storing execution requests in PouchDB

import PouchDB from "pouchdb";
import PouchDBFind from "pouchdb-find";
import {
  shouldSkipAuthValidation,
  TEST_FIREBASE_UID,
} from "../config/testConfig";
import { isRunningInExtension } from "../utils/extensionDetector";
import type {
  ExecutionRequest as BaseExecutionRequest,
  OrderDocument,
  MonitoringDocument,
  PouchDocument,
} from "../types";

// Register the find plugin
PouchDB.plugin(PouchDBFind);

export interface ExecutionRequest extends BaseExecutionRequest {
  _id?: string;
}

export interface Order {
  id: string;
  symbol: string;
  type: "buy" | "sell";
  quantity: number;
  price: string;
  status: "pending" | "inProgress" | "executed" | "cancelled" | (string & {});
  broker_status?: string;
  timestamp: string;
  orderType: string;
  product: string;
  broker: string;
  exchange?: string;
  execution_request_id?: string;
  primitive?: string; // NEW: display action/primitive label (e.g., BUY, SELL, MONITOR)
  action_id?: string;
  triggerPrice?: string;
  limitPrice?: string;
  execution_details?: Record<string, string>; // NEW: detailed execution info
}

export interface MonitoringAlert {
  id: string;
  description: string;
  symbol: string;
  triggerPrice: string;
  currentPrice: string;
  progress: string;
  progressPercent: number;
  status: "pending" | "inProgress" | "triggered" | "stopped" | (string & {});
  orderType: string;
  stopLoss: string;
  product: string;
  execution_request_id?: string;
  action_id?: string;
  // Structured fields for UI
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
  conditionOperator?: string;
  conditionValue?: string | number;
  limitPrice?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Simple execution PouchDB service
 */
class ExecutionPouchDBSyncService {
  private localDB: PouchDB.Database | null = null;
  private remoteDB: PouchDB.Database | null = null;
  private firebaseUID: string = "";
  private executionCallback: ((request: ExecutionRequest) => void) | null =
    null;

  constructor() {
    console.warn("🔧 ExecutionPouchDBSyncService constructor called");
  }

  /**
   * Initialize PouchDB with Firebase UID
   */
  async initialize(
    firebaseUID: string,
    couchdbDetails: {
      couchdb_url: string;
      couchdb_user: string;
      couchdb_password: string;
      couchdb_database: string;
    }
  ): Promise<void> {
    console.warn(
      "🚀 [INIT] BasePouchDBSyncService.initialize() called with Firebase UID:",
      firebaseUID
    );

    // Use test Firebase UID if provided
    const actualFirebaseUID = shouldSkipAuthValidation()
      ? TEST_FIREBASE_UID
      : firebaseUID;

    if (shouldSkipAuthValidation()) {
      console.warn(
        "🧪 [TEST_CONFIG] Using test Firebase UID:",
        actualFirebaseUID
      );
    }

    this.firebaseUID = actualFirebaseUID;

    // Generate database names
    const localDBName = `execution_${actualFirebaseUID}_local`;
    console.warn("🔧 Creating local PouchDB instance:", localDBName);
    this.localDB = new PouchDB(localDBName);
    console.warn("✅ Local PouchDB instance created successfully");

    console.warn(
      "🔧 Creating remote PouchDB instance:",
      `${couchdbDetails.couchdb_url}/${couchdbDetails.couchdb_database} (Username: ${couchdbDetails.couchdb_user})`
    );
    this.remoteDB = new PouchDB(
      `${couchdbDetails.couchdb_url}/${couchdbDetails.couchdb_database}`,
      {
        skip_setup: true,
        auth: {
          username: couchdbDetails.couchdb_user,
          password: couchdbDetails.couchdb_password,
        },
      }
    );
    if (isRunningInExtension()) {
      if (typeof chrome !== "undefined" && chrome.runtime?.sendMessage) {
        chrome.runtime.sendMessage({
          type: "CONNECT_TO_COUCHDB",
          firebaseUid: actualFirebaseUID,
          couchdb_details: couchdbDetails,
          success: true,
        });
      }
    }
    console.warn("✅ Remote PouchDB instance created successfully");

    // Test connection
    console.warn("🔧 [INIT] Testing connection to CouchDB...");
    try {
      await this.remoteDB!.info();
      console.warn("✅ [INIT] Connection test successful");
    } catch (_error) {
      console.warn(
        "⚠️ [INIT] Connection test failed, continuing with local-only mode"
      );
    }

    // Setup sync
    console.warn("🔄 [SYNC] Setting up sync with mode: live");
    console.warn("🔄 [SYNC] Initializing live sync...");

    try {
      const syncOptions = {
        live: true,
        retry: true,
        filter: (doc: PouchDocument) => {
          console.warn(
            "🔄 [SYNC] Firebase UID for filtering:",
            actualFirebaseUID
          );
          return doc.firebase_uid === actualFirebaseUID;
        },
      };

      console.warn("🔄 [SYNC] Starting live sync with options:", syncOptions);
      const sync = this.localDB!.sync(this.remoteDB!, syncOptions);

      sync
        .on("change", (info) => {
          console.warn("🔄 [SYNC] Change detected:", info);
        })
        .on("paused", () => {
          console.warn("⏸️ [SYNC] Sync paused");
        })
        .on("active", () => {
          console.warn("▶️ [SYNC] Sync active");
        })
        .on("error", (err) => {
          console.warn("❌ [SYNC] Sync error:", err);
        });

      console.warn("✅ [SYNC] Live sync initialized");
    } catch (_error) {
      console.warn(
        "⚠️ [SYNC] Sync setup failed, continuing with local-only mode"
      );
    }

    console.warn(
      "🎉 [INIT] BasePouchDBSyncService initialization completed successfully!"
    );
  }

  /**
   * Store execution request in PouchDB
   */
  async storeExecutionRequest(
    request: Omit<ExecutionRequest, "_id" | "_rev">,
    preferredId?: string
  ): Promise<string> {
    if (!this.localDB) {
      throw new Error("PouchDB not initialized");
    }

    // Clear previous requests for this Firebase UID (like the original)
    console.warn(
      `[ExecutionPouchDB] 🧹 Clearing previous execution requests for user: ${request.firebase_uid}`
    );
    const clearedCount = await this.clearPreviousRequests(request.firebase_uid);
    console.warn(
      `[ExecutionPouchDB] ✅ Cleared ${clearedCount} previous execution requests`
    );

    // Create document with _id (like the original ExecutionDocumentHandler)
    const doc: ExecutionRequest = {
      _id:
        preferredId ||
        `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      ...request,
    };

    // Add Firebase UID context if not present
    if (!doc.firebase_uid && this.firebaseUID) {
      doc.firebase_uid = this.firebaseUID;
    }

    // Add timestamps (like the original storeDocument)
    const now = new Date().toISOString();
    if (!doc.created_at) {
      doc.created_at = now;
    }
    doc.updated_at = now;

    const result = await this.localDB.put(doc);
    return typeof result === "string" ? result : result.id || doc._id!;
  }

  /**
   * Clear previous execution requests for a user (like the original)
   */
  private async clearPreviousRequests(firebaseUID: string): Promise<number> {
    if (!this.localDB) {
      console.warn(
        "[ExecutionPouchDB] LocalDB not initialized, skipping clear"
      );
      return 0;
    }

    // Check if find method is available (requires pouchdb-find plugin)
    if (typeof this.localDB.find !== "function") {
      console.error(
        "[ExecutionPouchDB] CRITICAL: PouchDB find method not available! This will cause execution request accumulation!"
      );
      // Try alternative cleanup method using allDocs
      return await this.clearPreviousRequestsAlternative(firebaseUID);
    }

    try {
      // Find all execution requests for this user
      const result = await this.localDB.find({
        selector: {
          type: "execution_request",
          firebase_uid: firebaseUID,
        },
      });

      // Delete them
      let deleteCount = 0;
      for (const doc of result.docs) {
        try {
          await this.localDB.remove(doc._id, doc._rev);
          deleteCount++;
        } catch (_error) {
          // Ignore delete errors (document might already be deleted)
        }
      }

      return deleteCount;
    } catch (error) {
      console.warn("Error clearing previous requests:", error);
      return 0;
    }
  }

  /**
   * Alternative method to clear previous requests using allDocs (fallback when find() not available)
   */
  private async clearPreviousRequestsAlternative(
    firebaseUID: string
  ): Promise<number> {
    if (!this.localDB) {
      return 0;
    }

    try {
      console.warn(
        "[ExecutionPouchDB] Using alternative cleanup method with allDocs"
      );

      // Get all documents
      const result = await this.localDB.allDocs({ include_docs: true });

      // Filter execution requests for this user
      const toDelete = result.rows.filter((row) => {
        const doc = row.doc as ExecutionRequest;
        return (
          doc &&
          doc.type === "execution_request" &&
          doc.firebase_uid === firebaseUID
        );
      });

      // Delete them
      let deleteCount = 0;
      for (const row of toDelete) {
        try {
          if (row.doc) {
            // Type guard
            await this.localDB.remove(row.doc._id, row.doc._rev);
            deleteCount++;
            console.warn(
              `[ExecutionPouchDB] Deleted execution request: ${row.doc._id}`
            );
          }
        } catch (error) {
          console.warn(
            `[ExecutionPouchDB] Failed to delete ${row.doc?._id}:`,
            error
          );
        }
      }

      console.warn(
        `[ExecutionPouchDB] Alternative cleanup completed: ${deleteCount} requests deleted`
      );
      return deleteCount;
    } catch (error) {
      console.error("[ExecutionPouchDB] Alternative cleanup failed:", error);
      return 0;
    }
  }

  /**
   * Set callback for execution changes
   */
  setExecutionCallback(callback: (request: ExecutionRequest) => void): void {
    this.executionCallback = callback;
    // Note: Change monitoring would be implemented here if needed
  }

  /**
   * Get orders from PouchDB
   */
  async getOrders(): Promise<Order[]> {
    // console.warn("🚀 [ORDERS] ===== STARTING ORDER RETRIEVAL =====");

    if (!this.localDB) {
      console.warn(
        "⚠️ [ORDERS] PouchDB not initialized, returning empty orders array"
      );
      return [];
    }

    // console.warn("📋 [ORDERS] Firebase UID:", this.firebaseUID);
    // console.warn("📋 [ORDERS] Querying for order_result documents...");

    try {
      const result = await this.localDB.find({
        selector: {
          type: "order_result",
          firebase_uid: this.firebaseUID,
        },
      });

      // console.warn(`📊 [ORDERS] Found ${result.docs.length} order documents`);

      const mappedOrders = result.docs.map((pouchDoc) => {
        const doc = pouchDoc as OrderDocument;
        const mappedOrder: Order = {
          id: doc.id,
          symbol: doc.symbol,
          type:
            doc.tradeType?.toLowerCase() === "buy"
              ? ("buy" as const)
              : ("sell" as const),
          quantity: parseInt(String(doc.quantity)) || 0,
          price: doc.price,
          status: doc.status,
          broker_status: doc.broker_status,
          primitive: doc.primitive || doc.action,
          execution_details: doc.execution_details,
          triggerPrice: doc.triggerPrice,
          limitPrice: doc.limitPrice,
          timestamp: doc.timestamp,
          orderType: doc.orderType,
          product: doc.product,
          broker: doc.broker || "zerodha", // Default to zerodha if not specified
          exchange: doc.exchange || doc.market || undefined,
          execution_request_id: doc.execution_request_id,
          action_id: doc.action_id,
        };
        return mappedOrder;
      });

      // Sort by timestamp (newest first)
      mappedOrders.sort((a, b) => {
        const timeA = new Date(a.timestamp).getTime();
        const timeB = new Date(b.timestamp).getTime();
        return timeB - timeA; // Descending order (newest first)
      });

      // Add EXEC-SYNC debug
      try {
        Array.from(
          new Set(mappedOrders.map((o) => o.execution_request_id || ""))
        ).filter(Boolean);
      } catch (_) {
        // ignore debug aggregation errors
      }

      return mappedOrders;
    } catch (error) {
      console.error("❌ [ORDERS] ===== ORDER RETRIEVAL FAILED =====");
      console.error("❌ [ORDERS] Error details:", error);
      return [];
    }
  }

  /**
   * Get monitoring alerts from PouchDB
   */
  async getMonitoringAlerts(): Promise<MonitoringAlert[]> {
    // console.warn("🚀 [MONITORING] ===== STARTING MONITORING RETRIEVAL =====");

    if (!this.localDB) {
      return [];
    }

    try {
      const result = await this.localDB.find({
        selector: {
          type: "monitoring_alert",
          firebase_uid: this.firebaseUID,
        },
      });

      const mappedAlerts = result.docs.map((pouchDoc) => {
        const doc = pouchDoc as MonitoringDocument;
        const condition = doc.condition;
        const onTrigger = doc.onTrigger;
        const onTrigArgs = onTrigger?.arguments;
        const condOperator = (condition?.operator || "").toString();
        const condValue = condition?.value;
        const otcActionRaw = (onTrigger?.action || "").toString();
        const actionName = otcActionRaw.toLowerCase();
        const normalizedVerb = actionName.includes("sell")
          ? "SELL"
          : actionName.includes("buy")
            ? "BUY"
            : otcActionRaw.toUpperCase();
        const normalizedOrderType = (() => {
          if (actionName.includes("stoploss") && actionName.includes("limit"))
            return "SL-L";
          if (actionName.includes("stoploss") && actionName.includes("market"))
            return "SL-M";
          if (actionName.includes("limit")) return "LIMIT";
          return "MARKET";
        })();
        const otcQty =
          onTrigger?.quantity ??
          onTrigger?.QUANTITY ??
          onTrigArgs?.quantity ??
          onTrigArgs?.QUANTITY;
        const otcSym =
          onTrigger?.symbol || onTrigArgs?.symbol || doc.symbol || "";
        const productRaw =
          doc.product ||
          onTrigArgs?.productType ||
          onTrigArgs?.PRODUCT_TYPE ||
          "";
        const mappedAlert: MonitoringAlert = {
          id: doc.id,
          description: doc.description,
          symbol: doc.symbol,
          triggerPrice: doc.triggerPrice,
          currentPrice: doc.currentPrice,
          progress: doc.progress,
          progressPercent: doc.progressPercent,
          status: doc.status,
          orderType: doc.orderType || normalizedOrderType,
          stopLoss: doc.stopLoss,
          product: productRaw ? String(productRaw).toUpperCase() : "",
          execution_request_id: doc.execution_request_id,
          action_id: doc.action_id,
          onTriggerAction: normalizedVerb || undefined,
          onTriggerQuantity: Number.isFinite(Number(otcQty))
            ? Number(otcQty)
            : undefined,
          onTriggerSymbol: otcSym ? String(otcSym).toUpperCase() : undefined,
          conditionOperator: condOperator || undefined,
          conditionValue: condValue !== undefined ? condValue : undefined,
          limitPrice: (() => {
            const lp =
              onTrigArgs?.limitPrice ||
              onTrigArgs?.LIMIT_PRICE ||
              onTrigArgs?.price ||
              onTrigArgs?.PRICE;
            return lp !== undefined && lp !== null ? String(lp) : undefined;
          })(),
          createdAt: doc.created_at,
          updatedAt: doc.updated_at,
        };
        return mappedAlert;
      });

      // Sort by timestamp (newest first)
      mappedAlerts.sort((a, b) => {
        const docA = result.docs.find(
          (doc) => (doc as MonitoringDocument).id === a.id
        );
        const docB = result.docs.find(
          (doc) => (doc as MonitoringDocument).id === b.id
        );
        const timeA = (docA as MonitoringDocument)?.created_at
          ? new Date((docA as MonitoringDocument).created_at).getTime()
          : 0;
        const timeB = (docB as MonitoringDocument)?.created_at
          ? new Date((docB as MonitoringDocument).created_at).getTime()
          : 0;
        return timeB - timeA; // Descending order (newest first)
      });

      // Add EXEC-SYNC debug
      try {
        Array.from(
          new Set(mappedAlerts.map((a) => a.execution_request_id || ""))
        ).filter(Boolean);
      } catch (_) {
        // ignore debug aggregation errors
      }

      return mappedAlerts;
    } catch (error) {
      console.error("❌ [MONITORING] Error details:", error);
      return [];
    }
  }

  /**
   * Stop a monitoring alert by setting its status to "stopped" in local PouchDB.
   * Accepts an alert object (preferred) or a document id string.
   */
  async stopMonitoringAlert(
    alertOrId: string | Partial<MonitoringAlert>
  ): Promise<{ success: boolean; id: string; rev?: string; error?: string }> {
    if (!this.localDB) {
      return {
        success: false,
        id: typeof alertOrId === "string" ? alertOrId : alertOrId.id || "",
        error: "PouchDB not initialized",
      };
    }

    const id = typeof alertOrId === "string" ? alertOrId : alertOrId.id || "";
    if (!id) {
      return { success: false, id: "", error: "Missing monitoring alert id" };
    }

    let doc: Record<string, unknown>;
    try {
      doc = await this.localDB.get(id);
    } catch (err: unknown) {
      if (
        typeof err === "object" &&
        err !== null &&
        "status" in err &&
        (err as { status?: number }).status === 404
      ) {
        // Do not create fallback documents; surface the error
        throw new Error("Monitoring document not found");
      }
      throw err;
    }

    const result = await this.localDB.put({
      ...doc,
      status: "stopped",
      updated_at: new Date().toISOString(),
      cancelled_at: new Date().toISOString(),
    });
    return { success: true, id, rev: result.rev };
  }

  /**
   * Watch for real-time order updates
   */
  watchOrderUpdates(callback: (orders: Order[]) => void): void {
    // console.warn("🚀 [ORDERS-WATCH] ===== SETTING UP ORDER WATCHER =====");

    if (!this.localDB) {
      return;
    }

    this.localDB
      .changes({
        since: "now",
        live: true,
        include_docs: true,
        filter: (doc) =>
          (doc as OrderDocument).type === "order_result" &&
          (doc as OrderDocument).firebase_uid === this.firebaseUID,
      })
      .on("change", (_change) => {
        this.getOrders().then((orders) => {
          callback(orders);
        });
      })
      .on("error", (err: Error) => {
        console.error("❌ [ORDERS-WATCH] ===== ORDER WATCH ERROR =====", err);
      });
  }

  /**
   * Watch for real-time monitoring updates
   */
  watchMonitoringUpdates(callback: (alerts: MonitoringAlert[]) => void): void {
    console.warn(
      "🚀 [MONITORING-WATCH] ===== SETTING UP MONITORING WATCHER ====="
    );

    if (!this.localDB) {
      console.warn(
        "⚠️ [MONITORING-WATCH] PouchDB not initialized, cannot watch monitoring updates"
      );
      return;
    }

    this.localDB
      .changes({ since: "now", live: true, include_docs: true })
      .on("change", (change) => {
        const doc = change?.doc as MonitoringDocument;
        if (!doc) return;
        if (
          doc.type === "monitoring_alert" &&
          doc.firebase_uid === this.firebaseUID
        ) {
          this.getMonitoringAlerts().then((alerts) => {
            callback(alerts);
          });
        }
      })
      .on("error", (err: Error) => {
        console.error(
          "❌ [MONITORING-WATCH] ===== MONITORING WATCH ERROR ====="
        );
        console.error("❌ [MONITORING-WATCH] Error details:", err);
      });

    console.warn("✅ [MONITORING-WATCH] Monitoring watcher setup completed");
  }

  /**
   * Watch a specific execution_request document for status updates
   */
  watchExecutionRequestStatus(
    executionRequestId: string,
    callback: (status: string, doc?: ExecutionRequest) => void
  ): void {
    if (!this.localDB) {
      console.warn(
        "⚠️ [EXEC-REQ-WATCH] PouchDB not initialized, cannot watch execution request"
      );
      return;
    }
    try {
      // Immediately fetch once
      this.localDB
        .get(executionRequestId)
        .then((doc) => {
          if ((doc as ExecutionRequest)?.status)
            callback((doc as ExecutionRequest).status, doc as ExecutionRequest);
        })
        .catch(() => {});

      this.localDB
        .changes({ since: "now", live: true, include_docs: true })
        .on("change", (change) => {
          const doc = change?.doc as ExecutionRequest;
          if (
            doc &&
            doc._id === executionRequestId &&
            doc.type === "execution_request"
          ) {
            callback(doc.status, doc);
          }
        })
        .on("error", (err: Error) => {
          console.error("❌ [EXEC-REQ-WATCH] Watch error:", err);
        });
    } catch (err) {
      console.error("❌ [EXEC-REQ-WATCH] Failed to set watch:", err);
    }
  }
}

// Export singleton instance
export const executionPouchDBSyncService = new ExecutionPouchDBSyncService();
