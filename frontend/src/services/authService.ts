// Auth Service - Handles session management to break circular dependency
// This service manages server-side session cookies without depending on the auth store

const API_CONFIG = {
  baseUrl: import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",
  endpoints: {
    auth: {
      sessionLogin: "/api/v1/auth/sessionLogin",
      sessionLogout: "/api/v1/auth/sessionLogout",
    },
  },
};

// Helper function to build endpoint URL
function buildEndpointUrl(endpointPath: string): string {
  return `${API_CONFIG.baseUrl}${endpointPath}`;
}

// Generic POST function for auth service
async function postData<T = Record<string, unknown>>(
  endpoint: string,
  data: Record<string, unknown>
): Promise<T> {
  const fullUrl = buildEndpointUrl(endpoint);
  console.warn(`[AuthService] Posting data to: ${fullUrl}`);

  try {
    const headers: HeadersInit = {
      "Content-Type": "application/json",
    };

    const res = await fetch(fullUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
      credentials: "include", // Include cookies for session management
    });

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    return res.json();
  } catch (error) {
    console.error(`[AuthService] Failed to post to ${fullUrl}:`, error);
    throw error;
  }
}

export class AuthService {
  /**
   * Establish a server-side session by sending Firebase ID token
   * This creates/updates the session cookie on the server
   */
  static async sessionLogin(
    idToken: string,
    expiresInDays: number = 14
  ): Promise<{ success: boolean; message: string }> {
    try {
      console.warn("[AuthService] Establishing server session...");
      const result = await postData<{
        success: boolean;
        message: string;
      }>(API_CONFIG.endpoints.auth.sessionLogin, { idToken, expiresInDays });
      console.warn("[AuthService] Server session established successfully");
      return result;
    } catch (error) {
      console.error("[AuthService] Failed to establish server session:", error);
      throw error;
    }
  }

  /**
   * Clear the server-side session cookie
   */
  static async sessionLogout(): Promise<{ success: boolean; message: string }> {
    try {
      console.warn("[AuthService] Clearing server session...");
      const result = await postData<{
        success: boolean;
        message: string;
      }>(API_CONFIG.endpoints.auth.sessionLogout, {});
      console.warn("[AuthService] Server session cleared successfully");
      return result;
    } catch (error) {
      console.error("[AuthService] Failed to clear server session:", error);
      throw error;
    }
  }
}
