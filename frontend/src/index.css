@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  @apply font-sans;
}

/* Accordion animations */
@keyframes slideDown {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes slideUp {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-slideDown {
  animation: slideDown 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

.animate-slideUp {
  animation: slideUp 300ms cubic-bezier(0.87, 0, 0.13, 1);
}

/* Figma Design Tokens */
.bg-Background-Surface-Neutral-Primary {
  @apply bg-white;
}

.outline-Border-Neutral-Primary {
  @apply outline-gray-200;
}

.text-Text-Neutral-Primary {
  @apply text-gray-900;
}
