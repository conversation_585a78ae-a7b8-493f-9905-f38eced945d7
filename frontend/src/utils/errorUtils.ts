import { FirebaseError } from "firebase/app";

/**
 * Converts Firebase error codes to user-friendly messages
 * @param error - The error object from Firebase
 * @returns A user-friendly error message
 */
export const getFirebaseErrorMessage = (error: FirebaseError): string => {
  // If it's already a user-friendly message, return it
  if (typeof error === "string") {
    return error;
  }

  // If it's an Error object with a message that's already user-friendly, return it
  if (error instanceof Error && !error.message.includes("auth/")) {
    return error.message;
  }

  // Extract error code from Firebase error
  const errorCode = error.code ?? error.message ?? "";

  // Map Firebase error codes to user-friendly messages
  const errorMessages: Record<string, string> = {
    // Phone number errors
    "auth/invalid-phone-number":
      "Please enter a valid phone number in international format (e.g., +1234567890)",
    "auth/too-many-requests":
      "Too many attempts. Please wait a few minutes before trying again",
    "auth/quota-exceeded": "SMS quota exceeded. Please try again later",
    "auth/captcha-check-failed":
      "Verification failed. Please refresh the page and try again",
    "auth/web-context-unsupported":
      "This browser is not supported. Please try a different browser",

    // OTP verification errors
    "auth/invalid-verification-code":
      "Invalid verification code. Please check and try again",
    "auth/invalid-verification-id":
      "Verification session expired. Please request a new code",
    "auth/code-expired":
      "Verification code has expired. Please request a new code",
    "auth/missing-verification-code": "Please enter the verification code",
    "auth/missing-verification-id":
      "Verification session not found. Please request a new code",

    // General authentication errors
    "auth/user-disabled":
      "This account has been disabled. Please contact support",
    "auth/user-not-found":
      "No account found with this phone number. Please sign up",
    "auth/operation-not-allowed":
      "Phone authentication is not enabled. Please contact support",
    "auth/network-request-failed":
      "Network error. Please check your connection and try again",
    "auth/internal-error": "An internal error occurred. Please try again",
    "auth/invalid-api-key": "Configuration error. Please contact support",
    "auth/app-not-authorized":
      "This app is not authorized. Please contact support",

    // Session errors
    "auth/session-expired": "Your session has expired. Please sign in again",
    "auth/requires-recent-login": "For security, please sign in again",

    // Rate limiting
    "auth/rate-limit-exceeded":
      "Rate limit exceeded. Please wait before trying again",

    // Generic fallbacks
    "auth/unknown": "An unknown error occurred. Please try again",
    "auth/error": "An error occurred. Please try again",
  };

  // Check if we have a specific message for this error code
  if (errorCode in errorMessages) {
    return errorMessages[errorCode];
  }

  // If the error code contains 'auth/', try to extract the specific code
  if (errorCode.includes("auth/")) {
    const specificCode = errorCode.split("auth/")[1];
    if (specificCode in errorMessages) {
      return errorMessages[specificCode];
    }
  }

  // If we have a message but it's a Firebase technical message, provide a generic one
  if (error?.message && error.message.includes("auth/")) {
    return "Authentication failed. Please check your details and try again";
  }

  // Fallback for any other error
  return "Something went wrong. Please try again";
};

/**
 * Checks if an error is a Firebase authentication error
 * @param error - The error object to check
 * @returns True if it's a Firebase auth error
 */
export const isFirebaseAuthError = (error: FirebaseError): boolean => {
  const errorCode = error?.code || error?.message || "";
  return errorCode.includes("auth/");
};

/**
 * Gets a user-friendly error message for any type of error
 * @param error - The error object
 * @returns A user-friendly error message
 */
export const getUserFriendlyErrorMessage = (error: FirebaseError): string => {
  // If it's already a user-friendly string, return it
  if (typeof error === "string") {
    return error;
  }

  // If it's an Error object, check if it contains Firebase error codes first
  if (error instanceof Error) {
    const message = error.message;

    // Check if the message contains Firebase error codes (e.g., "Firebase: TOO_LONG (auth/invalid-phone-number)")
    if (message.includes("auth/")) {
      // Extract the auth code from the message - handle both formats
      let authCode = null;

      // Try to match auth/code format
      const authMatch = message.match(/auth\/[^)\s]+/);
      if (authMatch) {
        authCode = authMatch[0];
      }

      // If we found an auth code, use it
      if (authCode) {
        return getFirebaseErrorMessage(
          new FirebaseError(authCode, "Something went wrong")
        );
      }
    }

    // Check if the message is already user-friendly (doesn't contain technical details)
    if (
      !message.includes("auth/") &&
      !message.includes("Firebase:") &&
      !message.includes("TOO_LONG")
    ) {
      return message;
    }
  }

  // If it's a Firebase auth error object, use the Firebase error handler
  if (isFirebaseAuthError(error)) {
    return getFirebaseErrorMessage(error);
  }

  // Fallback for any other error
  return "Something went wrong. Please try again";
};
