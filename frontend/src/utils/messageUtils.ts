/**
 * Message utility functions for transforming message primitives for display
 */

export interface PrimitiveType {
  action?: string;
  arguments?: Record<string, unknown>;
  human_friendly_explanation?: string;
  need_more_info?: string[];
  clarification?: string;
}

export function transformPrimitivesForDisplay(
  primitives: PrimitiveType[] | string[] | undefined,
  messageType?: string,
  group?: {
    group_human_friendly_explanation?: string;
    group_clarification_message?: string;
  }
): string[] {
  const groupClar = group?.group_clarification_message?.trim();
  if (groupClar) {
    // console.warn(
    //   `[MessageUtils] Using group_clarification_message for ${messageType}:`,
    //   groupClar
    // );
    return [groupClar];
  }
  const groupHuman = group?.group_human_friendly_explanation?.trim();
  if (groupHuman) {
    // console.warn(
    //   `[MessageUtils] Using group_human_friendly_explanation for ${messageType}:`,
    //   groupHuman
    // );
    return [groupHuman];
  }

  if (!primitives || primitives.length === 0) {
    return [];
  }

  // If primitives is already an array of strings, return it directly
  if (typeof primitives[0] === "string") {
    return primitives as string[];
  }

  // Process as PrimitiveType objects
  const result = (primitives as PrimitiveType[])
    .map((primitive: PrimitiveType) => {
      // Use messageType to determine which field to display
      if (
        messageType === "order_confirmation" ||
        messageType === "order_execution"
      ) {
        // For order confirmations AND executions, prefer human_friendly_explanation
        const explanation =
          primitive.human_friendly_explanation || primitive.clarification || "";
        if (explanation) {
          // console.warn(
          //   `[MessageUtils] Using human_friendly_explanation for ${messageType}:`,
          //   explanation
          // );
        }
        return explanation;
      } else {
        // For other message types (order_planning, chat_response, monitor_order, etc.), prefer clarification
        const clarification =
          primitive.clarification || primitive.human_friendly_explanation || "";
        if (clarification) {
          // console.warn(
          //   `[MessageUtils] Using clarification for ${messageType}:`,
          //   clarification
          // );
        }
        return clarification;
      }
    })
    .filter((text) => text.length > 0);

  // console.warn(
  //   `[MessageUtils] Transformed primitives for ${messageType}:`,
  //   result
  // );
  return result;
}
