// API Configuration
const API_CONFIG = {
  baseUrl: window.__USE_MOCK__
    ? "/assets/mock-data"
    : import.meta.env.VITE_API_BASE_URL || "http://localhost:8000",

  endpoints: {
    notifications:
      import.meta.env.VITE_NOTIFICATIONS_ENDPOINT || "/api/v1/notifications",
    profile: import.meta.env.VITE_PROFILE_ENDPOINT || "/api/v1/profile",
    monitoring:
      import.meta.env.VITE_MONITORING_ENDPOINT ||
      "/api/v1/monitoring/instances",
    orders: import.meta.env.VITE_ORDERS_ENDPOINT || "/api/v1/orders",
    chat: import.meta.env.VITE_CHAT_ENDPOINT || "/api/v1/chatHistory",
    health: import.meta.env.VITE_HEALTH_ENDPOINT || "/api/v1/health",
    // Auth endpoints
    auth: {
      signup: "/api/v1/auth/signup",
      checkUser: "/api/v1/auth/check-user",
      profile: "/api/v1/auth/profile",
      getUserDbDetails: "/api/v1/auth/get-user-db-details",
      sessionLogin: "/api/v1/auth/sessionLogin",
      sessionLogout: "/api/v1/auth/sessionLogout",
    },
  },
};

// Helper function to build endpoint URL
function buildEndpointUrl(endpointPath: string): string {
  const baseUrl = API_CONFIG.baseUrl;

  if (window.__USE_MOCK__) {
    // For mock mode, append .json extension
    return `${baseUrl}${endpointPath}.json`;
  } else {
    // For real API, use the endpoint as-is
    return `${baseUrl}${endpointPath}`;
  }
}

// Cookie-only: no auth headers
async function getAuthHeaders(): Promise<HeadersInit> {
  return {
    "Content-Type": "application/json",
  };
}

// Generic fetch function
export async function fetchData(endpointPath: string) {
  const fullUrl = buildEndpointUrl(endpointPath);
  console.warn(`Fetching data from: ${fullUrl} (Mock: ${window.__USE_MOCK__})`);

  try {
    const res = await fetch(fullUrl, { credentials: "include" });
    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }
    return res.json();
  } catch (error) {
    console.error(`Failed to fetch from ${fullUrl}:`, error);
    throw error;
  }
}

// Generic GET function with authentication
export async function getData<T = Record<string, unknown>>(
  endpoint: string,
  requireAuth: boolean = true,
  signOutCallback?: () => Promise<void>
): Promise<T> {
  const fullUrl = buildEndpointUrl(endpoint);
  console.warn(`Getting data from: ${fullUrl} (Mock: ${window.__USE_MOCK__})`);

  if (window.__USE_MOCK__) {
    console.warn("Mock: GET request", { endpoint });
    // For mock mode, return a mock response
    return Promise.resolve({
      success: true,
      couchdb_url: "http://localhost:5984",
      couchdb_user: "mock_user",
      couchdb_password: "mock_password",
      couchdb_database: "mock_db",
    } as T);
  }

  try {
    const headers = requireAuth
      ? await getAuthHeaders()
      : { "Content-Type": "application/json" };

    const res = await fetch(fullUrl, {
      method: "GET",
      headers,
      credentials: window.__USE_MOCK__ ? undefined : "include",
    });

    // Handle 403 Forbidden - redirect to login
    if (res.status === 403) {
      console.warn("Authentication failed, redirecting to login");
      sessionStorage.setItem("session_expired", "1");
      if (signOutCallback) {
        try {
          console.warn("[Extension] 🚪 Starting logout process...");
          await signOutCallback();
          localStorage.removeItem("firebase_uid");
          localStorage.removeItem("sessionUserId");
          localStorage.removeItem("extension_login_completed");
        } catch (error) {
          console.error("[Extension] ❌ Logout failed:", error);
        }
      }
      throw new Error("Authentication failed");
    }

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    return res.json();
  } catch (error) {
    console.error(`Failed to get data from ${fullUrl}:`, error);
    throw error;
  }
}

// Generic POST function
export async function postData<T = Record<string, unknown>>(
  endpoint: string,
  data: Record<string, unknown>,
  requireAuth: boolean = true,
  signOutCallback?: () => Promise<void>
): Promise<T> {
  const fullUrl = buildEndpointUrl(endpoint);
  console.warn(`Posting data to: ${fullUrl} (Mock: ${window.__USE_MOCK__})`);
  if (window.__USE_MOCK__) {
    console.warn("Mock: POST request", { endpoint, data });
    // For mock mode, return a mock response
    return Promise.resolve({
      success: true,
      user_id: "mock-user-id",
      conversation_id: "mock-conversation-id",
    } as T);
  }

  try {
    const headers = requireAuth
      ? await getAuthHeaders()
      : { "Content-Type": "application/json" };

    const res = await fetch(fullUrl, {
      method: "POST",
      headers,
      body: JSON.stringify(data),
      credentials: window.__USE_MOCK__ ? undefined : "include",
    });

    // Handle 403 Forbidden - redirect to login
    if (res.status === 403) {
      console.warn("Authentication failed, redirecting to login");
      sessionStorage.setItem("session_expired", "1");
      if (signOutCallback) {
        try {
          console.warn("[Extension] 🚪 Starting logout process...");
          await signOutCallback();
          localStorage.removeItem("firebase_uid");
          localStorage.removeItem("sessionUserId");
          localStorage.removeItem("extension_login_completed");
        } catch (error) {
          console.error("[Extension] ❌ Logout failed:", error);
        }
      }
      throw new Error("Authentication failed");
    }

    if (!res.ok) {
      throw new Error(`HTTP error! status: ${res.status}`);
    }

    return res.json();
  } catch (error) {
    console.error(`Failed to post to ${fullUrl}:`, error);
    throw error;
  }
}

// Specific API functions
export const apiClient = {
  // Generic POST method
  post: postData,

  // Generic GET method
  get: getData,

  // Authentication
  auth: {
    signup: (data: { firebase_token: string; name: string; phone?: string }) =>
      postData(API_CONFIG.endpoints.auth.signup, data, false),

    checkUser: (data: { firebase_token: string }) =>
      postData(API_CONFIG.endpoints.auth.checkUser, data, false),

    getProfile: () => fetchData(API_CONFIG.endpoints.auth.profile),

    updateProfile: (data: { name?: string; phone?: string }) =>
      postData(API_CONFIG.endpoints.auth.profile, data, true),

    getUserDbDetails: () =>
      getData<{
        couchdb_url: string;
        couchdb_user: string;
        couchdb_password: string;
        couchdb_database: string;
      }>(API_CONFIG.endpoints.auth.getUserDbDetails, true),
  },

  // Notifications
  getNotifications: () => fetchData(API_CONFIG.endpoints.notifications),

  // Profile
  getProfile: () => fetchData(API_CONFIG.endpoints.profile),

  // Monitoring
  getMonitoring: () => fetchData(API_CONFIG.endpoints.monitoring),

  // Orders
  getOrders: () => fetchData(API_CONFIG.endpoints.orders),

  // Chat
  sendChatMessage: (message: string) => {
    if (window.__USE_MOCK__) {
      return Promise.resolve({ success: true });
    }
    return postData(API_CONFIG.endpoints.chat, { message }, true);
  },
};

// Create authenticated API client with signOut callback
export function createAuthenticatedApiClient(
  signOutCallback: () => Promise<void>
) {
  return {
    // Generic methods with authentication
    post: <T = Record<string, unknown>>(
      endpoint: string,
      data: Record<string, unknown>
    ) => postData<T>(endpoint, data, true, signOutCallback),

    // Authentication methods with signOut callback
    auth: {
      signup: (data: {
        firebase_token: string;
        name: string;
        phone?: string;
      }) =>
        postData(
          API_CONFIG.endpoints.auth.signup,
          data,
          false,
          signOutCallback
        ),

      checkUser: (data: { firebase_token: string }) =>
        postData(
          API_CONFIG.endpoints.auth.checkUser,
          data,
          false,
          signOutCallback
        ),

      getProfile: () => fetchData(API_CONFIG.endpoints.auth.profile),

      updateProfile: (data: { name?: string; phone?: string }) =>
        postData(
          API_CONFIG.endpoints.auth.profile,
          data,
          true,
          signOutCallback
        ),

      getUserDbDetails: () =>
        getData<{
          couchdb_url: string;
          couchdb_user: string;
          couchdb_password: string;
          couchdb_database: string;
        }>(API_CONFIG.endpoints.auth.getUserDbDetails, true, signOutCallback),
    },

    // Other authenticated methods
    getNotifications: () => fetchData(API_CONFIG.endpoints.notifications),
    getProfile: () => fetchData(API_CONFIG.endpoints.profile),
    getMonitoring: () => fetchData(API_CONFIG.endpoints.monitoring),
    getOrders: () => fetchData(API_CONFIG.endpoints.orders),

    sendChatMessage: (message: string) => {
      if (window.__USE_MOCK__) {
        return Promise.resolve({ success: true });
      }
      return postData(
        API_CONFIG.endpoints.chat,
        { message },
        true,
        signOutCallback
      );
    },
  };
}

// Export individual functions for backward compatibility
export { fetchData as default };
