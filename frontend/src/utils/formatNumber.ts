/**
 * Formats a number for display.
 * - Adds commas to the integer part.
 * - Shows up to 2 decimal places for floats.
 * @param value The number or string to format.
 * @returns A formatted string.
 */
export function formatNumber(value: string | number): string {
  const num =
    typeof value === "string" ? parseFloat(value.replace(/,/g, "")) : value;

  if (isNaN(num)) {
    return String(value); // Return original string if not a valid number
  }

  const parts = num.toString().split(".");
  const integerPart = parseInt(parts[0], 10).toLocaleString("en-US");

  if (parts.length > 1) {
    const decimalPart = parts[1].slice(0, 2);
    return `${integerPart}.${decimalPart}`;
  }

  return integerPart;
}
