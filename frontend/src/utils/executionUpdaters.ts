/* eslint-disable @typescript-eslint/no-explicit-any */
import { useWebSocketStore } from "../stores/websocketStore";
import { mapPrimitiveToLabel } from "../components/WebSocketProvider";

// Merge helper
const makeKey = (o: any) =>
  (o?.action_id || o?.id || String(o?.symbol || "").toLowerCase()).toString();

const pick = (r: any, p: any, key: string) =>
  r[key] !== undefined && r[key] !== null ? r[key] : p[key];

export function buildOrdersUpdater(execId: string, messageId: string) {
  const { updateMessageById, setChatInputBlocked } =
    useWebSocketStore.getState();
  return (orders: any[]) => {
    const scoped = orders.filter(
      (o: any) => (o.execution_request_id || "") === execId
    );
    updateMessageById("chat", messageId, (old: any) => {
      const previous = Array.isArray(old.executionOrders)
        ? old.executionOrders
        : [];

      const pendingOnly = scoped.filter(
        (o: any) => String(o?.status || "").toLowerCase() === "pending"
      );
      const allDone = pendingOnly.length === 0 && scoped.length > 0;

      const prevByKey = new Map<string, any>(
        previous.map((e: any) => [makeKey(e), e])
      );
      const realByKey = new Map<string, any>(
        scoped.map((o: any) => [makeKey(o), o])
      );
      const targetKeys = new Set<string>([
        ...prevByKey.keys(),
        ...realByKey.keys(),
      ]);
      const display = Array.from(targetKeys).map((k) => {
        const real = realByKey.get(k) || {};
        const prev = prevByKey.get(k) || {};
        return {
          id: pick(real, prev, "id"),
          symbol: String(pick(real, prev, "symbol") || ""),
          status: String(
            pick(real, prev, "broker_status") ||
              pick(real, prev, "status") ||
              "pending"
          ),
          quantity: pick(real, prev, "quantity"),
          price: pick(real, prev, "price"),
          orderType: pick(real, prev, "orderType"),
          product: pick(real, prev, "product"),
          broker: pick(real, prev, "broker"),
          exchange: pick(real, prev, "exchange"),
          timestamp: pick(real, prev, "timestamp"),
          action_id: pick(real, prev, "action_id"),
          triggerPrice: pick(real, prev, "triggerPrice"),
          limitPrice: pick(real, prev, "limitPrice"),
          execution_details: pick(real, prev, "execution_details"),
          primitive: (() => {
            const fromReal = (real?.primitive || real?.type || "").toString();
            const fromPrev = (prev?.primitive || "").toString();
            const val = fromReal || fromPrev;
            return mapPrimitiveToLabel(val);
          })(),
        };
      });
      setChatInputBlocked(
        display.some(
          (o: any) => String(o?.status || "").toLowerCase() === "pending"
        )
      );
      return { ...old, executionOrders: display, executionCompleted: allDone };
    });
  };
}

export function buildMonitoringUpdater(execId: string, messageId: string) {
  const { updateMessageById } = useWebSocketStore.getState();
  return (alerts: any[]) => {
    const scoped = alerts.filter(
      (a: any) => (a.execution_request_id || "") === execId
    );
    updateMessageById("chat", messageId, (old: any) => {
      const previous = Array.isArray(old.executionOrders)
        ? old.executionOrders
        : [];

      const prevByKey = new Map<string, any>(
        previous.map((e: any) => [makeKey(e), e])
      );
      const realByKey = new Map<string, any>(
        scoped.map((a: any) => [makeKey(a), a])
      );
      const targetKeys = new Set<string>([
        ...prevByKey.keys(),
        ...realByKey.keys(),
      ]);
      const display = Array.from(targetKeys).map((k) => {
        const real = realByKey.get(k) || {};
        const prev = prevByKey.get(k) || {};
        return {
          id: pick(real, prev, "id"),
          symbol: String(pick(real, prev, "symbol") || ""),
          status: String(
            pick(real, prev, "broker_status") ||
              pick(real, prev, "status") ||
              "pending"
          ),
          quantity: pick(real, prev, "quantity"),
          price: pick(real, prev, "price"),
          orderType: pick(real, prev, "orderType"),
          product: pick(real, prev, "product"),
          broker: pick(real, prev, "broker"),
          action_id: pick(real, prev, "action_id"),
          primitive: (() => {
            const fromReal = (real?.primitive || real?.type || "").toString();
            const fromPrev = (prev?.primitive || "").toString();
            const val = fromReal || fromPrev;
            return mapPrimitiveToLabel(val);
          })(),
          triggerPrice: pick(real, prev, "triggerPrice"),
          currentPrice: pick(real, prev, "currentPrice"),
          isMonitoring: true,
          conditionOperator: pick(real, prev, "conditionOperator"),
          conditionValue: pick(real, prev, "conditionValue"),
          onTriggerAction: pick(real, prev, "onTriggerAction"),
          onTriggerQuantity: pick(real, prev, "onTriggerQuantity"),
          onTriggerSymbol: pick(real, prev, "onTriggerSymbol"),
        };
      });
      const statuses = scoped.map((a: any) =>
        String(a?.status || "").toLowerCase()
      );
      const hasPending = statuses.some((s: string) => s === "pending");
      const hasStarted = scoped.length > 0 && !hasPending;
      return {
        ...old,
        textMessage: hasStarted
          ? "Set up done! Monitoring started."
          : "Setting up a conditional order...",
        executionOrders: display,
        executionCompleted: false,
        monitoringStarted: hasStarted,
      };
    });
  };
}
