# Error Utilities

This directory contains utility functions for handling errors in the application.

## errorUtils.ts

Provides user-friendly error message conversion for Firebase authentication errors.

### Functions

#### `getFirebaseErrorMessage(error: any): string`

Converts Firebase error codes to user-friendly messages.

**Examples:**

```typescript
// Firebase error object
const error = { code: "auth/invalid-phone-number" };
getFirebaseErrorMessage(error);
// Returns: "Please enter a valid phone number in international format (e.g., +**********)"

// Error with Firebase message
const error = new Error("Firebase: TOO_LONG (auth/invalid-phone-number)");
getFirebaseErrorMessage(error);
// Returns: "Please enter a valid phone number in international format (e.g., +**********)"
```

#### `isFirebaseAuthError(error: any): boolean`

Checks if an error is a Firebase authentication error.

#### `getUserFriendlyErrorMessage(error: any): string`

Gets a user-friendly error message for any type of error. This is the main function to use in components.

**Usage in components:**

```typescript
import { getUserFriendlyErrorMessage } from "../utils/errorUtils";

try {
  await sendOTP(phoneNumber);
} catch (error) {
  const errorMessage = getUserFriendlyErrorMessage(error);
  setErrorMessage(errorMessage);
}
```

### Supported Firebase Error Codes

- `auth/invalid-phone-number` - Invalid phone number format
- `auth/too-many-requests` - Too many attempts
- `auth/quota-exceeded` - SMS quota exceeded
- `auth/invalid-verification-code` - Invalid OTP code
- `auth/code-expired` - OTP code expired
- `auth/user-not-found` - No account found
- `auth/network-request-failed` - Network error
- And many more...

### Error Message Examples

**Before (technical):**

- "Firebase: TOO_LONG (auth/invalid-phone-number)"
- "auth/invalid-verification-code"
- "Firebase: Error (auth/quota-exceeded)"

**After (user-friendly):**

- "Please enter a valid phone number in international format (e.g., +**********)"
- "Invalid verification code. Please check and try again"
- "SMS quota exceeded. Please try again later"
