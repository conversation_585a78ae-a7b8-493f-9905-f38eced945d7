/* eslint-disable @typescript-eslint/no-explicit-any */
// Navigation manager that handles session initialization for tabs
import {
  hasValidSession,
  loadChatHistory,
  getCurrentBroker,
} from "./sessionManager";
import { useWebSocketStore } from "../stores/websocketStore";
import { useSidebarStore } from "../stores/sidebarStore";
// Note: keep types minimal to avoid unused errors
import {
  buildMonitoringPlaceholders,
  buildOrderPlaceholders,
  isMonitoringPrimitive,
  isOrderPrimitive,
} from "./executionPlaceholders";

export interface NavigationResult {
  success: boolean;
  error?: string;
  sessionInitialized?: boolean;
}

// Navigate to a tab and ensure session is initialized
export async function navigateToTab(
  tab: "chat" | "orders" | "monitoring"
): Promise<NavigationResult> {
  // Set active tab first
  const sidebarStore = useSidebarStore.getState();
  sidebarStore.setActiveTab(tab);

  // Return success without initializing session (handled by ChatHomePage)
  return { success: true, sessionInitialized: false };
}

// Check if all required sessions are initialized
export function checkSessionStatus(): {
  chat: boolean;
  orders: boolean;
  monitoring: boolean;
} {
  const currentBroker = getCurrentBroker();

  return {
    chat: hasValidSession(currentBroker, "chat"),
    orders: hasValidSession(currentBroker, "orders"),
    monitoring: hasValidSession(currentBroker, "monitoring"),
  };
}

// Preload sessions for all tabs (optional optimization)
export async function preloadAllSessions(): Promise<{
  chat: NavigationResult;
  orders: NavigationResult;
  monitoring: NavigationResult;
}> {
  const [chatResult, ordersResult, monitoringResult] = await Promise.all([
    initializeTabSession("chat"),
    initializeTabSession("orders"),
    initializeTabSession("monitoring"),
  ]);

  return {
    chat: chatResult,
    orders: ordersResult,
    monitoring: monitoringResult,
  };
}

// Handle new chat functionality - clears current tab messages and conversation_id
export async function handleNewChat(): Promise<NavigationResult> {
  const { activeTab } = useSidebarStore.getState();

  // Determine the current tab type
  const tabType =
    activeTab === "orders"
      ? "orders"
      : activeTab === "monitoring"
        ? "monitoring"
        : "chat";

  try {
    console.warn(`Starting new chat for ${tabType} tab`);

    const websocketStore = useWebSocketStore.getState();

    // Clear messages for current tab and conversation_id from localStorage
    websocketStore.clearCurrentTabMessages(tabType);

    console.warn(`New chat initialized for ${tabType}:`, {
      messagesCleared: true,
      conversationIdCleared: true,
      userIdPreserved: true,
      readyForFirstMessage: true,
    });

    return {
      success: true,
      sessionInitialized: false, // Will be created on next WebSocket message
    };
  } catch (error) {
    console.error(`Failed to start new chat for ${tabType}:`, error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to start new chat",
      sessionInitialized: false,
    };
  }
}

// Load chat history for a tab if session exists (no session creation)
export async function initializeTabSession(
  tab: "chat" | "orders" | "monitoring"
): Promise<NavigationResult> {
  const currentBroker = getCurrentBroker();

  try {
    // Check if session exists and load history
    if (hasValidSession(currentBroker, tab)) {
      console.warn(
        `[NavigationManager] Loading chat history for existing session: ${currentBroker} ${tab}`
      );

      // Update WebSocket store with existing session
      const websocketStore = useWebSocketStore.getState();
      websocketStore.updateSessionForTab(tab);

      // Clear existing messages before loading history
      websocketStore.clearMessages(tab);

      // Load chat history if both IDs exist
      try {
        const historyResult = await loadChatHistory(currentBroker, tab);

        if (historyResult) {
          // Use shared helpers from executionPlaceholders for classification and building placeholders

          // Insert history
          for (const item of historyResult.history) {
            const isSystem = (item.data as any)?.sender === "system";
            const msgType = ((item.data as any)?.messageType || "") as string;
            const execId = (item.data as any)?.execution_request_id as
              | string
              | undefined;

            // For order_execution, synthesize placeholders using primitives
            if (isSystem && msgType === "order_execution") {
              // 1) Add the original order_execution item first → ChatMessagesView will render "Executing..."
              websocketStore.addMessage(item, tab);
              const primitivesRaw: any[] = Array.isArray(
                (item.data as any)?.primitives
              )
                ? ((item.data as any).primitives as any[])
                : [];
              const monitoring = primitivesRaw.filter(isMonitoringPrimitive);
              const orders = primitivesRaw.filter(isOrderPrimitive);
              const monitoringPlaceholders =
                buildMonitoringPlaceholders(monitoring);
              const orderPlaceholders = buildOrderPlaceholders(orders);
              // When both present, create two bubbles: monitoring first, then orders
              const baseData = item.data as any;
              // Intentionally no console logs in production path
              const createdIds: string[] = [];
              if (monitoringPlaceholders.length > 0) {
                const monitoringMsg = {
                  id: `${Date.now()}_${Math.random()}`,
                  timestamp: item.timestamp,
                  data: {
                    ...baseData,
                    primitives: [],
                    textMessage: "Setting up a conditional order...",
                    sender: "system",
                    typeOfMessage: "chat",
                    messageType: "chat_response",
                    executionOrders: monitoringPlaceholders,
                    executionCompleted: false,
                    monitoringStarted: false,
                  },
                } as any;
                const mid = websocketStore.addMessage(monitoringMsg, tab);
                createdIds.push(mid);
                if (execId) {
                  websocketStore.registerExecutionMessage(execId, {
                    monitoringMessageId: mid,
                  });
                }
              }
              if (orderPlaceholders.length > 0) {
                const ordersMsg = {
                  id: `${Date.now()}_${Math.random()}`,
                  timestamp: item.timestamp,
                  data: {
                    ...baseData,
                    primitives: [],
                    textMessage: "Executing your order(s)...",
                    sender: "system",
                    typeOfMessage: "chat",
                    messageType: "chat_response",
                    executionOrders: orderPlaceholders,
                    executionCompleted: false,
                  },
                } as any;
                const oid = websocketStore.addMessage(ordersMsg, tab);
                createdIds.push(oid);
                if (execId) {
                  websocketStore.registerExecutionMessage(execId, {
                    ordersMessageId: oid,
                  });
                }
              }
              // We already added the original item (exec bubble) and any derived bubbles
              continue;
            }

            // Default path: add original item
            const msgId = websocketStore.addMessage(item, tab);

            // Register exec mapping for syncing if any placeholders exist (orders-only case)
            if (isSystem && msgType === "order_execution" && execId) {
              const placeholders = (item.data as any).executionOrders || [];
              const hasMonitoring = placeholders.some(
                (p: any) => p.isMonitoring
              );
              if (hasMonitoring) {
                websocketStore.registerExecutionMessage(execId, {
                  monitoringMessageId: msgId,
                });
              } else {
                websocketStore.registerExecutionMessage(execId, {
                  ordersMessageId: msgId,
                });
              }
            }
          }

          console.warn(
            `[NavigationManager] Chat history loaded for ${currentBroker} ${tab}:`,
            {
              historyCount: historyResult.history.length,
            }
          );
        }
      } catch (error) {
        console.warn(
          `[NavigationManager] Failed to load chat history for ${currentBroker} ${tab}:`,
          error
        );
        // Continue anyway - user can start fresh conversation
      }

      return { success: true, sessionInitialized: false };
    }

    // No session exists - user will get session IDs from WebSocket when they send first message
    console.warn(
      `[NavigationManager] No session found for ${currentBroker} ${tab} - will be created on first message`
    );
    return { success: true, sessionInitialized: false };
  } catch (e) {
    console.warn("initializeTabSession failed:", e);
    return { success: false, sessionInitialized: false };
  }
}
