/* eslint-disable @typescript-eslint/no-explicit-any */
export type AnyPrimitive = {
  id?: string;
  action?: string;
  arguments?: Record<string, any>;
  symbol?: string;
  quantity?: number;
  orderType?: string;
  condition?: {
    symbol?: string;
    operator?: string;
    value?: number | string;
  };
  on_trigger?: {
    action?: string;
    quantity?: number;
    symbol?: string;
    arguments?: Record<string, any>;
  };
};

export const monitoringActions = new Set([
  "monitorconditionthenact",
  "monitorprofit",
  "monitorsymbolfromwatchlist",
  "monitor",
]);

export const orderActions = new Set([
  "buy",
  "sell",
  "placebuylimitorder",
  "placeselllimitorder",
  "placebuystoplossmarketorder",
  "placesellstoplossmarketorder",
  "placebuystoplosslimitorder",
  "placesellstoplosslimitorder",
  "sellall",
  "exitall",
  "exitallpositions"
]);

export const isMonitoringPrimitive = (p: AnyPrimitive) =>
  monitoringActions.has(String(p?.action || "").toLowerCase());

export const isOrderPrimitive = (p: AnyPrimitive) =>
  orderActions.has(String(p?.action || "").toLowerCase());

export function buildOrderPlaceholders(primitives: AnyPrimitive[]): any[] {
  const list: any[] = [];
  const arr = Array.isArray(primitives) ? primitives : [];
  arr.filter(isOrderPrimitive).forEach((p: any, idx: number) => {
    const args = p?.arguments || {};
    const rawSym = (args.symbol || args.SYMBOL || p?.symbol || "").toString();
    const sym = rawSym ? rawSym.toLowerCase() : "";
    if (!sym) return;
    const qty = Number(args.quantity ?? args.QUANTITY ?? 0) || undefined;
    const product = (args.productType || args.PRODUCT_TYPE) as
      | string
      | undefined;
    const actionName = String(p?.action || "").toLowerCase();
    let orderType: string | undefined = "MARKET";
    if (actionName.includes("limit")) orderType = "LIMIT";
    if (actionName.includes("stoploss") && actionName.includes("market"))
      orderType = "SL-M";
    if (actionName.includes("stoploss") && actionName.includes("limit"))
      orderType = "SL-L";
    list.push({
      id: `pending_${Date.now()}_${idx}`,
      symbol: sym,
      status: "pending",
      quantity: qty,
      broker: "zerodha",
      action_id: p?.id,
      primitive: String(p?.action || "").toUpperCase(),
      product,
      orderType,
    } as any);
  });
  return list;
}

export function buildMonitoringPlaceholders(primitives: AnyPrimitive[]): any[] {
  const list: any[] = [];
  const arr = Array.isArray(primitives) ? primitives : [];
  arr.filter(isMonitoringPrimitive).forEach((p: any, idx: number) => {
    const args = p?.arguments || {};
    const cond = args?.condition || p?.condition || {};
    const onTrig = args?.on_trigger || p?.on_trigger || {};
    const trySym = (
      args.symbol ||
      args.SYMBOL ||
      cond.symbol ||
      onTrig.symbol ||
      onTrig.arguments?.symbol ||
      p?.symbol ||
      ""
    ).toString();
    const sym = trySym ? trySym.toLowerCase() : "";
    if (!sym) return;
    const qty = Number(
      args.quantity ??
        args.QUANTITY ??
        onTrig.quantity ??
        onTrig.QUANTITY ??
        onTrig.arguments?.quantity ??
        onTrig.arguments?.QUANTITY
    );
    const onTrigAction = String(onTrig?.action || "").toUpperCase();
    const onTrigQty = Number(
      onTrig?.quantity ??
        onTrig?.QUANTITY ??
        onTrig?.arguments?.quantity ??
        onTrig?.arguments?.QUANTITY
    );
    const onTrigSym = (
      onTrig?.symbol ||
      onTrig?.arguments?.symbol ||
      ""
    ).toString();
    list.push({
      id: `mon_pending_${Date.now()}_${idx}`,
      symbol: sym,
      status: "pending",
      quantity: Number.isFinite(qty) && qty > 0 ? qty : undefined,
      broker: "zerodha",
      action_id: p?.id,
      primitive: onTrigAction || String(p?.action || "").toUpperCase(),
      triggerPrice: undefined,
      currentPrice: undefined,
      isMonitoring: true,
      conditionOperator: (cond?.operator || "").toString(),
      conditionValue: cond?.value,
      onTriggerAction: onTrigAction || undefined,
      onTriggerQuantity: Number.isFinite(onTrigQty) ? onTrigQty : undefined,
      onTriggerSymbol: onTrigSym ? onTrigSym.toLowerCase() : undefined,
    } as any);
  });
  return list;
}

export function extractActionIds(primitives: AnyPrimitive[]) {
  const arr = Array.isArray(primitives) ? primitives : [];
  const orderActionIds = arr
    .filter(isOrderPrimitive)
    .map((p) => String(p?.id || "")) as string[];
  const monitoringActionIds = arr
    .filter(isMonitoringPrimitive)
    .map((p) => String(p?.id || "")) as string[];
  return { orderActionIds, monitoringActionIds };
}
