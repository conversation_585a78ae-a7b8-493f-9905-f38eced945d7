/**
 * LoginPage Component
 *
 * This component implements the login interface for <PERSON><PERSON><PERSON> based on the actual design.
 * Features:
 * - Purple starburst logo with <PERSON>agman branding
 * - Dashed purple border container
 * - Feature cards with bar chart icons
 * - Purple login button with diagonal stripes
 * - Authentication state management
 * - Navigation to ChatHomePage after authentication
 */

import React, { useEffect } from "react";
import { useNavStore } from "../stores/navStore";
import { useAuthStore } from "../stores/authStore";
import { navigate } from "../navigation/pageNavigator";
import { useLoginMode } from "../hooks/useLoginMode";
import loginFrontImage from "../assets/login_front_image.svg";
import WebLoginPage from "./WebLoginPage";
import Card from "../components/Card";
import Pill from "../components/Pill";
import CTAButton from "../components/CTAButton";
import { getUserFriendlyErrorMessage } from "../utils/errorUtils";
import type {
  ExtensionMessage,
  ExtensionMessageSender,
  OTPPromiseResolvers,
} from "../types";

// Chrome Extension API usage with proper type safety

declare global {
  interface Window {
    otpPromiseResolvers?: OTPPromiseResolvers;
  }
}

// Utility function to safely access Chrome APIs
const getChromeAPI = () => {
  if (typeof chrome !== "undefined" && chrome?.tabs && chrome?.runtime) {
    return chrome;
  }
  return null;
};
import ĀagmanIcon from "../assets/Āagman.svg";
import PinIcon from "../assets/pin.svg";
// import LoginIcon from "../assets/loginIcon.svg";
import type { User } from "firebase/auth";
import type { FirebaseError } from "firebase/app";
import Avatar from "../components/Avatar";
import NotificationCard from "../components/NotificationCard";

// Figma Design Tokens (from Āagman-variables-full.json)
const designTokens = {
  // Brand Colors from Figma
  brand900: "rgb(18, 18, 51)", // Darkest purple
  brand800: "rgb(38, 33, 102)", // Very dark purple
  brand700: "rgb(56, 51, 153)", // Dark purple
  brand600: "rgb(74, 66, 201)", // Medium dark purple
  brand500: "rgb(92, 84, 252)", // Primary brand purple
  brand400: "rgb(125, 117, 252)", // Light purple
  brand300: "rgb(158, 153, 255)", // Lighter purple
  brand200: "rgb(191, 186, 255)", // Very light purple
  brand100: "rgb(222, 222, 255)", // Lightest purple
  brand50: "rgb(240, 237, 255)", // Background purple

  // Semantic Colors
  success: "rgb(31, 139, 77)", // Green for Zerodha pill
  successBg: "rgb(233, 247, 239)", // Light green background
  successBorder: "rgb(169, 223, 191)", // Light green border

  // Neutral Colors
  white: "rgb(255, 255, 255)",
  gray50: "rgb(248, 250, 252)",
  gray100: "rgb(241, 245, 249)",
  gray200: "rgb(226, 232, 240)",
  gray300: "rgb(203, 213, 225)",
  gray400: "rgb(148, 163, 184)",
  gray500: "rgb(100, 116, 139)",
  gray600: "rgb(71, 85, 105)",
  gray700: "rgb(51, 65, 85)",
  gray800: "rgb(30, 41, 59)",
  gray900: "rgb(15, 23, 42)",

  // Text Colors
  textPrimary: "rgb(15, 23, 42)", // gray900
  textSecondary: "rgb(100, 116, 139)", // gray500
  textMuted: "rgb(148, 163, 184)", // gray400
};

// Feature data
const _features = [
  {
    id: 1,
    text: "Get daily stock suggestions tailored to your goals, behavior, and market trends.",
  },
  {
    id: 2,
    text: "Know what's working, what's risky, and how to optimize your investments.",
  },
  {
    id: 3,
    text: "Stay ahead of the curve with real-time market movement forecasts.",
  },
  {
    id: 4,
    text: "Get notified the moment key events impact your trades.",
  },
];

// Sub-component: Header
const _Header: React.FC = () => {
  return (
    <Card className="border-b-2 border-white bg-[#f4f6fa]">
      <div className="flex flex-row items-center p-4 gap-4">
        {/* Logo Section */}
        <div className="flex-1 flex flex-row items-start gap-2">
          {/* Āagman Logo with Gradient Background */}
          <div className="relative w-8 h-8 rounded-lg bg-gradient-to-r from-[#5c54fd] to-[#a330e5] flex items-center justify-center shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)] border-2 border-[rgba(255,255,255,0.12)]">
            <div className="absolute inset-0 rounded-lg shadow-[0px_0px_0px_1px_inset_rgba(16,24,40,0.18),0px_-2px_0px_0px_inset_rgba(16,24,40,0.05)]" />
            <img
              src={ĀagmanIcon}
              alt="Āagman"
              className="w-[21.333px] h-[21.333px] relative z-10"
            />
          </div>

          {/* Text and Pill Section */}
          <div className="flex flex-col gap-1 justify-center">
            <h1 className="font-bold text-[18px] leading-[26px] text-[#181e29] whitespace-nowrap">
              Āagman
            </h1>
            <Pill className="text-[12px] leading-[16px]">Zerodha</Pill>
          </div>
        </div>

        {/* Pin Icon Section */}
        <div className="flex flex-row gap-4">
          {/* Pin Icon */}
          <button className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded transition-colors relative">
            <img src={PinIcon} alt="Pin" className="w-6 h-6" />
          </button>
        </div>
      </div>
    </Card>
  );
};

// ===================================================================
// PHASE 2 & 3: SECURE EXTENSION LOGIN WITH POSTMESSAGE COMMUNICATION
// ===================================================================

/**
 * Firebase Authentication Functions for Extension Context
 * These handle secure authentication operations within the extension,
 * keeping sensitive Firebase tokens isolated from the web tab.
 */

// Send OTP via Firebase (Extension context only)
// This is now just a placeholder - actual OTP sending is handled by the web tab
const _sendOTP = async (_phone: string): Promise<void> => {
  // This function is no longer used in the new flow
  // OTP sending is handled directly by the web tab via handleOTPRequestFromExtension
  console.warn(
    `[Extension] sendOTP called - this should not happen in the new flow`
  );
  throw new Error("sendOTP should not be called in the new flow");
};

// Verify OTP and check user existence (Extension context only)
// This is now just a placeholder - actual OTP verification is handled by the web tab
const _verifyOTP = async (
  phone: string,
  otp: string
): Promise<{ user: User; userExists: boolean; sessionUserId?: string }> => {
  console.warn(
    `[Extension] 🔍 VERIFICATION ATTEMPT: phone=${phone}, otp=${otp}`
  );
  console.warn(
    `[Extension] verifyOTP called - this should not happen in the new flow`
  );
  console.warn(
    `[Extension] OTP verification is now handled by the web tab via handleOTPVerificationForExtension`
  );
  throw new Error(
    "verifyOTP should not be called in the new flow - verification handled by web tab"
  );
};

// Submit registration for new users (Extension context only)
const submitRegistration = async (
  phone: string,
  name: string
): Promise<{ success: boolean; sessionUserId?: string }> => {
  const authStore = useAuthStore.getState();

  try {
    let firebaseUser = authStore.firebaseUser;

    // 🆕 FALLBACK: If no Firebase user in memory, try to restore from localStorage
    if (!firebaseUser) {
      console.warn(
        `[Extension] 🔄 No Firebase user in memory, attempting localStorage restore`
      );
      const storedToken = localStorage.getItem("firebase_id_token");
      const storedUid = localStorage.getItem("firebase_uid");

      if (storedToken && storedUid) {
        console.warn(
          `[Extension] 📱 Restoring Firebase context from localStorage`
        );
        firebaseUser = {
          uid: storedUid,
          getIdToken: () => Promise.resolve(storedToken),
        } as User;

        // Update authStore with restored context
        authStore.firebaseUser = firebaseUser;
        console.warn(
          `[Extension] ✅ Firebase context restored from localStorage`
        );
      }
    }

    if (!firebaseUser) {
      throw new Error("No authenticated user found");
    }

    // Get Firebase ID token
    const idToken = await firebaseUser.getIdToken();

    // Register user with our backend
    const { authenticatedApiClient } = useAuthStore.getState();
    if (!authenticatedApiClient) {
      throw new Error("Authenticated API client not available");
    }
    const registrationResponse = await authenticatedApiClient.auth.signup({
      firebase_token: idToken,
      name,
      phone,
    });

    return {
      success: true,
      sessionUserId: registrationResponse.user_id as string | undefined,
    };
  } catch (error) {
    console.error("Registration error:", error);
    throw new Error("Registration failed. Please try again.");
  }
};

/**
 * PostMessage Communication Functions
 * These handle secure communication between extension and web login tab
 */

// Send messages to web login tab
const sendToWebTab = (
  loginWindow: Window,
  type: string,
  data: Record<string, unknown> = {}
) => {
  if (loginWindow && !loginWindow.closed) {
    loginWindow.postMessage({ type, ...data }, window.location.origin);
    console.warn(`[Extension] Sent message to web tab:`, { type, ...data });
  } else {
    console.warn(`[Extension] Cannot send message - web tab is closed`);
  }
};

/**
 * User Database Details Handler
 * Fetches CouchDB database details for the authenticated user
 */
const fetchUserDbDetails = async () => {
  console.warn("🔍 Fetching user database details...");
  const { authenticatedApiClient } = useAuthStore.getState();
  if (!authenticatedApiClient) {
    throw new Error("Authenticated API client not available");
  }
  const dbDetails = await authenticatedApiClient.auth.getUserDbDetails();

  console.warn("✅ User database details fetched successfully:");
  console.warn("CouchDB URL:", dbDetails.couchdb_url);
  console.warn("CouchDB User:", dbDetails.couchdb_user);
  console.warn("CouchDB Database:", dbDetails.couchdb_database);
  console.warn("CouchDB Password: [HIDDEN]");

  return dbDetails;
};

/**
 * Firebase Operation Handlers
 * These execute secure Firebase operations and communicate results to web tab
 */

// Handle OTP sending (Secure - Extension context only)
const _handleSendOTP = async (phone: string, loginWindow: Window) => {
  console.warn(`[Extension] Handling OTP send for phone: ${phone}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    await _sendOTP(phone);
    sendToWebTab(loginWindow, "OTP_SENT", { success: true });
    console.warn(`[Extension] OTP sent successfully`);
  } catch (error: unknown) {
    const errorMessage = getUserFriendlyErrorMessage(error as FirebaseError);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] OTP send failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

// Handle OTP verification (Secure - Extension context only)
const _handleVerifyOTP = async (
  phone: string,
  otp: string,
  loginWindow: Window
) => {
  console.warn(`[Extension] Handling OTP verification for phone: ${phone}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    const result = await _verifyOTP(phone, otp);
    sendToWebTab(loginWindow, "OTP_VERIFIED", {
      success: true,
      userExists: result.userExists,
      sessionUserId: result.sessionUserId,
    });
    console.warn(`[Extension] OTP verified successfully:`, {
      userExists: result.userExists,
    });
  } catch (error: unknown) {
    const errorMessage = getUserFriendlyErrorMessage(error as FirebaseError);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] OTP verification failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

// Handle registration completion (Secure - Extension context only)
const _handleCompleteRegistration = async (
  phone: string,
  name: string,
  loginWindow: Window
) => {
  console.warn(`[Extension] Handling registration completion for: ${name}`);
  sendToWebTab(loginWindow, "SET_LOADING", { isLoading: true });

  try {
    const result = await submitRegistration(phone, name);

    // Store session data securely in extension context
    if (result.sessionUserId) {
      localStorage.setItem("sessionUserId", result.sessionUserId);
      console.warn(`[Extension] Session ID stored: ${result.sessionUserId}`);
    }

    // Complete authentication in extension context (SECURE)
    const authStore = useAuthStore.getState();
    const { setAuthenticated } = useNavStore.getState();

    authStore.setAuthenticated(true);
    setAuthenticated(true);
    authStore.setLoading(false);

    console.warn(`[Extension] Authentication completed successfully`);

    // Fetch user database details after successful authentication
    authStore.setUserDbDetails(await fetchUserDbDetails());

    // Notify web tab to close
    sendToWebTab(loginWindow, "LOGIN_COMPLETE", { success: true });

    // Navigate to ChatHomePage in extension
    navigate();
    console.warn(`[Extension] Navigated to ChatHomePage`);
  } catch (error: unknown) {
    const errorMessage = getUserFriendlyErrorMessage(error as FirebaseError);
    sendToWebTab(loginWindow, "SET_ERROR", { error: errorMessage });
    console.error(`[Extension] Registration completion failed:`, error);
  } finally {
    sendToWebTab(loginWindow, "SET_LOADING", { isLoading: false });
  }
};

/**
 * ===============================================================================
 * CHROME RUNTIME MESSAGING HANDLERS (New secure approach)
 * ===============================================================================
 */

// Send messages to web login tab via Content Script Bridge
const sendToWebTabRuntime = (
  tabId: number | undefined,
  type: string,
  data: Record<string, unknown> = {}
) => {
  if (!tabId) {
    console.warn(`[Extension] Cannot send message - no tab ID provided`);
    return;
  }

  const message = { type, ...data };

  // Use chrome.tabs.sendMessage to send to the content script in the specific tab
  const chromeAPI = getChromeAPI();
  if (!chromeAPI) {
    console.warn(`[Extension] Chrome API not available`);
    return;
  }

  chromeAPI.tabs.sendMessage(tabId, message, (response: unknown) => {
    if (chromeAPI.runtime.lastError) {
      console.warn(
        `[Extension] Failed to send message to tab ${tabId}:`,
        chromeAPI.runtime.lastError
      );
    } else {
      console.warn(`[Extension] ✅ Sent message to tab ${tabId}:`, message);
      console.warn(`[Extension] Response:`, response);
    }
  });
};

// Handle OTP sending via Chrome Runtime (Secure - Extension context only)
const handleSendOTPRuntime = async (
  phone: string,
  tabId: number | undefined,
  sendResponse: (response?: unknown) => void
) => {
  console.warn(`[Extension] Runtime: Handling OTP send for phone: ${phone}`);
  console.warn(`[Extension] Telling web tab to handle OTP sending directly`);

  // Send loading state to web tab
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  // Tell the web tab to handle the OTP sending (reCAPTCHA setup + Firebase OTP)
  // The web tab will call handleOTPRequestFromExtension which will:
  // 1. Setup reCAPTCHA in web tab context
  // 2. Send OTP via Firebase
  // 3. Send back EXTENSION_OTP_SENT or EXTENSION_OTP_ERROR
  sendToWebTabRuntime(tabId, "HANDLE_OTP_REQUEST", { phone });

  console.warn(
    `[Extension] Sent HANDLE_OTP_REQUEST to web tab for phone: ${phone}`
  );
  sendResponse({ success: true });
};

// Handle OTP verification via Chrome Runtime (Secure - Extension context only)
const handleVerifyOTPRuntime = async (
  phone: string,
  otp: string,
  tabId: number | undefined,
  sendResponse: (response?: unknown) => void
) => {
  console.warn(
    `[Extension] Runtime: Handling OTP verification for phone: ${phone}, OTP: ${otp}`
  );
  console.warn(
    `[Extension] 🔍 VERIFICATION DETAILS: phone="${phone}", otp="${otp}", tabId=${tabId}`
  );
  console.warn(
    `[Extension] Telling web tab to handle OTP verification directly`
  );

  // Send loading state to web tab
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  // Tell the web tab to handle the OTP verification (Firebase verification + user check)
  // The web tab will call handleOTPVerificationForExtension which will:
  // 1. Verify OTP with Firebase using the confirmationResult from the web tab's context
  // 2. Check if user exists in our database
  // 3. Send back EXTENSION_OTP_VERIFIED or EXTENSION_OTP_ERROR
  sendToWebTabRuntime(tabId, "HANDLE_OTP_VERIFICATION", {
    phone,
    otp,
  });

  console.warn(
    `[Extension] Sent HANDLE_OTP_VERIFICATION to web tab for phone: ${phone}, OTP: ${otp}`
  );
  sendResponse({ success: true });
};

// Handle registration completion via Chrome Runtime (Secure - Extension context only)
const handleCompleteRegistrationRuntime = async (
  phone: string,
  name: string,
  tabId: number | undefined,
  sendResponse: (response?: unknown) => void
) => {
  console.warn(
    `[Extension] Runtime: Handling registration completion for: ${name}`
  );
  sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: true });

  try {
    const result = await submitRegistration(phone, name);

    // Store session data securely in extension context
    if (result.sessionUserId) {
      localStorage.setItem("sessionUserId", result.sessionUserId);
      console.warn(
        `[Extension] Runtime: Session ID stored: ${result.sessionUserId}`
      );
    }

    console.warn(
      `[Extension] Runtime: Database registration completed successfully`
    );

    // Fetch user database details after successful authentication
    useAuthStore.getState().setUserDbDetails(await fetchUserDbDetails());

    // Notify web tab to close (authentication and navigation handled by message handler)
    sendToWebTabRuntime(tabId, "LOGIN_COMPLETE", { success: true });

    sendResponse({ success: true });
  } catch (error: unknown) {
    const errorMessage = getUserFriendlyErrorMessage(error as FirebaseError);
    sendToWebTabRuntime(tabId, "SET_ERROR", { error: errorMessage });
    console.error(
      `[Extension] Runtime: Registration completion failed:`,
      error
    );
    sendResponse({ error: errorMessage });
  } finally {
    sendToWebTabRuntime(tabId, "SET_LOADING", { isLoading: false });
  }
};

/**
 * Setup secure Chrome Runtime Messaging with web login tab
 * This creates a message handler that processes authentication requests from the web tab
 */
const setupChromeRuntimeCommunication = (tabId: number) => {
  console.warn(`[Extension] 🔗 Setting up Chrome Runtime communication`);
  console.warn(`[Extension] 🆔 Target tab ID:`, tabId);
  console.warn(`[Extension] 📡 Ready to receive Chrome runtime messages`);

  const handleRuntimeMessage = async (
    message: ExtensionMessage,
    sender: ExtensionMessageSender,
    sendResponse: (response?: unknown) => void
  ) => {
    console.warn(`[Extension] 📨 Runtime message received:`, {
      message,
      sender,
      tabId: sender.tab?.id,
      expectedTabId: tabId,
    });

    // SECURITY: Verify message comes from our login tab
    if (sender.tab?.id !== tabId) {
      console.warn(
        `[Extension] ❌ Rejected message from wrong tab. Expected: ${tabId}, Got: ${sender.tab?.id}`
      );
      return;
    }

    console.warn(`[Extension] ✅ Tab ID validated`);
    console.warn(`[Extension] ✅ Processing message:`, message);

    // Route messages to appropriate secure handlers
    switch (message.type) {
      case "EXTENSION_BRIDGE_READY": {
        console.warn(`[Extension] Bridge ready for tab:`, sender.tab?.id);
        sendResponse({ success: true });
        break;
      }

      case "EXTENSION_SEND_OTP": {
        handleSendOTPRuntime(
          String(message.phone),
          sender.tab?.id,
          sendResponse
        );
        break;
      }

      case "EXTENSION_OTP_SENT": {
        console.warn(`[Extension] Web tab confirmed OTP sent successfully`);
        if (window.otpPromiseResolvers) {
          window.otpPromiseResolvers.resolve();
          window.otpPromiseResolvers = undefined;
        }
        sendResponse({ success: true });
        break;
      }

      case "EXTENSION_OTP_ERROR": {
        console.warn(`[Extension] Web tab reported OTP error:`, message.error);
        if (window.otpPromiseResolvers) {
          window.otpPromiseResolvers.reject(new Error(String(message.error)));
          window.otpPromiseResolvers = undefined;
        }
        sendResponse({ success: false });
        break;
      }

      case "EXTENSION_OTP_VERIFIED": {
        console.warn(
          `[Extension] Web tab confirmed OTP verification successful:`,
          {
            userExists: message.userExists,
            sessionUserId: message.sessionUserId,
            firebaseUid: message.firebaseUid,
          }
        );

        // 🆕 SYNC FIREBASE CONTEXT TO EXTENSION AUTHSTORE
        console.warn(`[Extension] 🔄 Syncing Firebase context from web tab...`);
        const authStore = useAuthStore.getState();

        // Store Firebase user data in extension's authStore
        if (message.firebaseUser && message.firebaseIdToken) {
          const firebaseUser = message.firebaseUser as User;
          console.warn(
            `[Extension] 🔑 Storing Firebase user context for database operations`
          );

          // 🆕 PERSIST ID TOKEN IN LOCALSTORAGE FOR CROSS-CONTEXT RELIABILITY
          localStorage.setItem(
            "firebase_id_token",
            String(message.firebaseIdToken)
          );
          localStorage.setItem("firebase_uid", firebaseUser.uid);
          console.warn(
            `[Extension] 💾 Firebase ID token persisted to localStorage`
          );

          // 🔧 CREATE COMPLETE FIREBASE USER OBJECT WITH ALL REQUIRED METHODS
          const syncedFirebaseUser = {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            phoneNumber: firebaseUser.phoneNumber,
            emailVerified: firebaseUser.emailVerified,
            displayName: firebaseUser.displayName,
            // Required Firebase User methods
            getIdToken: (_forceRefresh = false) => {
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve(message.firebaseIdToken || storedToken);
            },
            getIdTokenResult: (_forceRefresh = false) => {
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve({
                token: message.firebaseIdToken || storedToken,
                claims: {},
                authTime: new Date().toISOString(),
                issuedAtTime: new Date().toISOString(),
                expirationTime: new Date(Date.now() + 3600000).toISOString(), // 1 hour
              });
            },
            refreshToken: "mock_refresh_token",
            providerId: "firebase",
            isAnonymous: false,
            metadata: {
              creationTime: new Date().toISOString(),
              lastSignInTime: new Date().toISOString(),
            },
            providerData: [],
            reload: () => Promise.resolve(),
            toJSON: () => ({}),
            delete: () => Promise.resolve(),
          };

          // Store in authStore for database operations (but don't call setUser yet)
          authStore.firebaseUser = syncedFirebaseUser as unknown as User;
          console.warn(`[Extension] ✅ Firebase context synced successfully`);
        }

        // 🔧 FIXED NAVIGATION LOGIC: Only navigate for EXISTING users, not new users
        if (message.userExists) {
          console.warn(
            `[Extension] ✅ Existing user verified successfully! User will be logged in.`
          );

          // Set authentication state in extension for EXISTING users only
          console.warn(
            `[Extension] 🔑 Setting authentication state to true...`
          );
          const navStore = useNavStore.getState();

          // Set user in authStore (this will work now with complete Firebase user object)
          if (message.firebaseUser) {
            authStore.setUser(message.firebaseUser as unknown as User);
          }

          // Update both stores to ensure UI updates correctly
          authStore.setAuthenticated(true);
          navStore.setAuthenticated(true);
          console.warn(
            `[Extension] ✅ Authentication state updated in both stores`
          );

          // Navigate to ChatHomePage in extension for EXISTING users
          console.warn(`[Extension] 🔄 Navigating to ChatHomePage...`);
          navigate();
          console.warn(`[Extension] ✅ Navigation completed`);

          // Close the login tab after a short delay to ensure message response is sent
          setTimeout(() => {
            if (tabId) {
              console.warn(`[Extension] 🗑️ Closing login tab: ${tabId}`);
              const chromeAPI = getChromeAPI();
              if (chromeAPI) {
                chromeAPI.tabs.remove(tabId, () => {
                  console.warn(`[Extension] ✅ Login tab closed successfully`);
                });
              }
            }
          }, 500);
        } else {
          console.warn(
            `[Extension] 👤 New user verified - waiting for Step 3 (name entry) completion`
          );
          console.warn(
            `[Extension] 🔒 Extension will NOT navigate until registration completes`
          );
          // Web tab will continue with name collection (Step 3)
          // Extension will wait for COMPLETE_REGISTRATION message before navigating
        }
        sendResponse({ success: true });
        break;
      }

      case "EXTENSION_VERIFY_OTP": {
        handleVerifyOTPRuntime(
          String(message.phone),
          String(message.otp),
          sender.tab?.id,
          sendResponse
        );
        break;
      }

      case "EXTENSION_COMPLETE_REGISTRATION": {
        handleCompleteRegistrationRuntime(
          String(message.phone),
          String(message.name),
          sender.tab?.id,
          sendResponse
        );

        // 🆕 COMPLETE AUTHENTICATION AND NAVIGATION FOR NEW USERS (after Step 3)
        console.warn(
          `[Extension] 🎉 Step 3 completed - new user registration successful`
        );

        // Now that registration is complete, set authentication state and navigate
        const authStoreReg = useAuthStore.getState();
        const navStoreReg = useNavStore.getState();

        console.warn(
          `[Extension] 🔑 Setting authentication state for new user after registration...`
        );

        // Set user in authStore if Firebase context is available
        const storedUid = localStorage.getItem("firebase_uid");
        if (storedUid) {
          authStoreReg.setUser({
            uid: storedUid,
            email: null,
            phoneNumber: message.phone,
            emailVerified: true,
            displayName: message.name,
          } as User);
        }

        // Complete authentication
        authStoreReg.setAuthenticated(true);
        navStoreReg.setAuthenticated(true);
        console.warn(`[Extension] ✅ Authentication completed for new user`);

        // Navigate to ChatHomePage for new user
        console.warn(
          `[Extension] 🔄 Navigating new user to ChatHomePage after registration...`
        );
        navigate();
        console.warn(`[Extension] ✅ New user navigation completed`);

        break;
      }

      case "EXTENSION_LOGIN_COMPLETE": {
        console.warn(`[Extension] 🎉 Web tab confirmed login complete:`, {
          userExists: message.userExists,
          firebaseUid: message.firebaseUid,
          email: message.email,
          phoneNumber: message.phoneNumber,
        });

        // 🆕 SYNC FIREBASE CONTEXT FOR EXISTING USERS TOO
        console.warn(
          `[Extension] 🔄 Syncing Firebase context for existing user...`
        );
        const authStoreComplete = useAuthStore.getState();

        // Sync Firebase context if provided
        if (message.firebaseUser && message.firebaseIdToken) {
          const firebaseUser = message.firebaseUser as User;
          console.warn(
            `[Extension] 🔑 Storing Firebase context for existing user`
          );

          // 🆕 PERSIST ID TOKEN IN LOCALSTORAGE FOR CROSS-CONTEXT RELIABILITY
          localStorage.setItem(
            "firebase_id_token",
            String(message.firebaseIdToken)
          );
          localStorage.setItem("firebase_uid", firebaseUser.uid);
          console.warn(
            `[Extension] 💾 Firebase ID token persisted to localStorage for existing user`
          );

          // Create synced Firebase user object for database operations
          const syncedFirebaseUser = {
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            phoneNumber: firebaseUser.phoneNumber,
            emailVerified: firebaseUser.emailVerified,
            displayName: firebaseUser.displayName,
            getIdToken: () => {
              // First try memory, then fallback to localStorage
              const storedToken = localStorage.getItem("firebase_id_token");
              return Promise.resolve(message.firebaseIdToken || storedToken);
            },
          };

          // Store in authStore
          authStoreComplete.firebaseUser =
            syncedFirebaseUser as unknown as User;
          console.warn(
            `[Extension] ✅ Firebase context synced for existing user`
          );
        }

        // Set authentication state in both stores
        console.warn(
          `[Extension] 🔐 Setting authentication state for login completion...`
        );
        const navStoreComplete = useNavStore.getState();

        // Set user information in auth store
        if (message.firebaseUid) {
          authStoreComplete.setUser({
            uid: message.firebaseUid,
            email: message.email,
            phoneNumber: message.phoneNumber,
            emailVerified: true, // Assume verified since they completed phone auth
            displayName: null,
          } as User); // Using proper type to satisfy Firebase User interface requirements
        }

        // Set authentication states
        authStoreComplete.setAuthenticated(true);
        navStoreComplete.setAuthenticated(true);
        console.warn(
          `[Extension] ✅ Authentication state updated for login completion`
        );

        // Clear login progress now that flow is complete
        authStoreComplete.clearLoginProgress();

        // Fetch user database details after successful authentication
        authStoreComplete.setUserDbDetails(await fetchUserDbDetails());

        // Navigate to ChatHomePage
        console.warn(
          `[Extension] 🔄 Navigating to ChatHomePage after login completion...`
        );
        navigate();
        console.warn(`[Extension] ✅ Navigation completed after login`);

        // Close the login tab after a short delay
        setTimeout(() => {
          if (tabId) {
            console.warn(
              `[Extension] 🗑️ Closing login tab after login completion: ${tabId}`
            );
            const chromeAPI = getChromeAPI();
            if (chromeAPI) {
              chromeAPI.tabs.remove(tabId, () => {
                console.warn(
                  `[Extension] ✅ Login tab closed successfully after login completion`
                );
              });
            }
          }
        }, 500);

        sendResponse({ success: true });
        break;
      }

      case "EXTENSION_ALREADY_AUTHENTICATED": {
        console.warn(
          `[Extension] 📨 ALREADY_AUTHENTICATED received from web tab:`,
          message
        );
        console.warn(
          `[Extension] ✅ Web tab detected user is already authenticated!`
        );

        // Set authentication state in both stores
        console.warn(
          `[Extension] 🔐 Setting authentication state for already authenticated user...`
        );
        const authStoreAlready = useAuthStore.getState();
        const navStoreAlready = useNavStore.getState();

        // Set user information in auth store from web tab response
        if (message.user) {
          const firebaseUser = message.user as User;
          authStoreAlready.setUser({
            ...firebaseUser,
            emailVerified: true,
            displayName: null,
          } as User);
          console.warn(
            `[Extension] 🔐 User data set: ${firebaseUser.uid} (${firebaseUser.phoneNumber})`
          );
        }

        // Set authentication states
        authStoreAlready.setAuthenticated(true);
        navStoreAlready.setAuthenticated(true);
        console.warn(
          `[Extension] ✅ Authentication state updated for already authenticated user`
        );

        // Clear login progress as we won't keep the tab open
        authStoreAlready.clearLoginProgress();

        // Navigate to ChatHomePage
        console.warn(
          `[Extension] 🔄 Navigating to ChatHomePage for already authenticated user...`
        );
        navigate();
        console.warn(
          `[Extension] ✅ Navigation completed for already authenticated user`
        );

        // Close the login tab immediately (no delay needed since no OTP flow)
        if (tabId) {
          console.warn(
            `[Extension] 🗑️ Closing login tab for already authenticated user: ${tabId}`
          );
          const chromeAPI = getChromeAPI();
          if (chromeAPI) {
            chromeAPI.tabs.remove(tabId, () => {
              console.warn(
                `[Extension] ✅ Login tab closed successfully for already authenticated user`
              );
            });
          }
        }

        sendResponse({ success: true });
        break;
      }

      case "EXTENSION_CLOSE_LOGIN": {
        console.warn(`[Extension] Login tab requested closure - cleaning up`);
        const chromeAPI = getChromeAPI();
        if (chromeAPI) {
          chromeAPI.runtime.onMessage.removeListener(handleRuntimeMessage);
        }
        // Clear login progress when login tab is closed via message
        useAuthStore.getState().clearLoginProgress();
        sendResponse({ success: true });
        break;
      }

      default: {
        console.warn(`[Extension] Unknown message type: ${message.type}`);
        sendResponse({ error: "Unknown message type" });
        break;
      }
    }

    // Return true to indicate we will send a response asynchronously
    return true;
  };

  // Add Chrome Runtime message listener
  const chromeAPI = getChromeAPI();
  if (chromeAPI) {
    chromeAPI.runtime.onMessage.addListener(handleRuntimeMessage);
    console.warn(`[Extension] ✅ Chrome Runtime message listener attached`);
    console.warn(`[Extension] Communication setup complete`);
  }
};

// Sub-component: Login Button
const LoginButton: React.FC<{ onBeforeLogin?: () => void }> = ({
  onBeforeLogin,
}) => {
  const { isLoginInProgress } = useAuthStore();
  // NEW: Extension Web Login Flow - Opens web tab for secure authentication
  const handleExtensionLogin = async () => {
    if (isLoginInProgress) return;
    onBeforeLogin?.();
    // 🆕 Check if user is already authenticated from persistence
    const authStore = useAuthStore.getState();
    const navStore = useNavStore.getState();

    console.warn(`[Extension] 🔍 Checking authentication state before login:`, {
      isAuthenticated: authStore.isAuthenticated,
      hasUser: !!authStore.user,
      userUid: authStore.user?.uid,
    });

    if (authStore.isAuthenticated && authStore.user) {
      console.warn(
        `[Extension] ✅ User already authenticated, navigating directly to ChatHomePage`
      );
      console.warn(
        `[Extension] 🔐 User: ${authStore.user.uid} (${authStore.user.phoneNumber || authStore.user.email})`
      );

      // Set nav store authentication state to match
      navStore.setAuthenticated(true);

      authStore.setUserDbDetails(await fetchUserDbDetails());

      // Navigate directly to ChatHomePage
      navigate();
      console.warn(`[Extension] ✅ Navigation completed - skipped web login`);
      return; // Exit early, don't open web tab
    }

    console.warn(
      `[Extension] 🔓 User not authenticated, proceeding with web login flow`
    );

    // Block repeated clicks while we open and manage the login tab
    authStore.setLoginInProgress(true);

    // Clear any existing completion flags to start fresh
    localStorage.removeItem("extension_login_completed");

    // Open web login in new TAB (not popup window) with extension mode parameter
    // This ensures the web flow knows it's being used by the extension
    const frontendUrl = import.meta.env.VITE_FRONTEND_URL;
    if (
      !frontendUrl ||
      typeof frontendUrl !== "string" ||
      frontendUrl.trim() === ""
    ) {
      console.error(
        "[Extension] ❌ VITE_FRONTEND_URL environment variable is not set or is empty. Cannot proceed with web login."
      );
      authStore.clearLoginProgress();
      return;
    }
    const webLoginUrl = `${frontendUrl}/?mode=extension&action=fresh_login`;
    console.warn(`[Extension] Opening web login at: ${webLoginUrl}`);
    console.warn(
      `[Extension] 🔄 Using fresh_login flag to ensure clean auth state in web tab`
    );

    // Use Chrome Extension APIs for proper tab management and communication
    console.warn(`[Extension] 🚀 Using Chrome Extension API to create new tab`);

    // Create new tab using Chrome Extension API (instead of window.open)
    const chromeAPI = getChromeAPI();
    if (chromeAPI) {
      chromeAPI.tabs.create({ url: webLoginUrl }, (tab) => {
        console.warn(`[Extension] ✅ New tab created:`, tab);
        console.warn(`[Extension] 🆔 Tab ID:`, tab.id);

        // Setup Chrome Runtime Messaging (instead of postMessage)
        if (tab.id) {
          authStore.setLoginTabId(tab.id);
          setupChromeRuntimeCommunication(tab.id);

          // Attach one-time close listener to clear progress if user closes tab
          const onRemoved = (closedTabId: number) => {
            if (closedTabId === tab.id) {
              console.warn(
                `[Extension] 🗑️ Login tab closed by user: ${closedTabId}`
              );
              authStore.clearLoginProgress();
              chromeAPI.tabs.onRemoved.removeListener(onRemoved);
            }
          };
          chromeAPI.tabs.onRemoved.addListener(onRemoved);
        }
      });
    } else {
      authStore.clearLoginProgress();
    }
  };

  return (
    <CTAButton
      className={`w-full py-4 px-6 text-lg font-semibold ${isLoginInProgress ? "bg-gray-300 text-gray-600 border-gray-300 hover:bg-gray-300" : ""}`}
      onClick={handleExtensionLogin}
      disabled={isLoginInProgress}
    >
      Start with Āagman
    </CTAButton>
  );
};

// Main LoginPage Component
const LoginPage: React.FC = () => {
  const { isAuthenticated, setAuthenticated } = useNavStore();
  const loginMode = useLoginMode();
  const [showSessionExpired, setShowSessionExpired] = React.useState(false);

  // Check authentication status on mount
  useEffect(() => {
    // Mock mode: always authenticated (skip login)
    if (import.meta.env.VITE_USE_MOCK === "true") {
      setAuthenticated(true);
      return;
    }

    // Local mode: check localStorage for auth status
    const authToken = localStorage.getItem("authToken");
    if (authToken) {
      setAuthenticated(true);
    }
  }, [setAuthenticated]);

  // One-time session expired banner control
  useEffect(() => {
    if (sessionStorage.getItem("session_expired") === "1") {
      setShowSessionExpired(true);
      // Clear immediately so it only shows once per session
      sessionStorage.removeItem("session_expired");
    }
  }, []);

  // If authenticated, navigate to chat (handled by pageNavigator)
  useEffect(() => {
    if (isAuthenticated) {
      navigate();
    }
  }, [isAuthenticated]);

  // If web mode, render full-page login flow
  if (loginMode === "web") {
    return <WebLoginPage />;
  }

  // Extension mode - render existing dialog-based login
  return (
    <div
      className="h-screen relative overflow-hidden flex flex-col"
      style={{ backgroundColor: designTokens.gray50 }}
    >
      {/* Background Illustration */}
      <div className="pointer-events-none select-none absolute inset-0 z-0">
        <img
          src={loginFrontImage}
          alt="loginFrontImage"
          className="absolute left-1/2 top-[40%] -translate-x-1/2 -translate-y-1/2 w-full h-auto max-w-none"
        />
      </div>

      {/* Top Section */}
      <div className="z-10 pt-6 px-12 text-center">
        <div className="flex flex-col gap-2 items-center">
          <div className="flex items-center gap-1">
            <Avatar title="AI" icon={ĀagmanIcon} size="lg" variant="aagman" />
            <span
              className="font-['Inter'] text-lg text-[#43556e] bg-clip-text text-transparent"
              style={{
                backgroundImage:
                  "linear-gradient(100.076deg, rgb(92, 84, 253) 9.8213%, rgb(163, 48, 229) 80.799%) ",
                textShadow: "rgba(16,24,40,0.05) 0px 1px 2px",
                WebkitTextFillColor: "transparent",
              }}
            >
              Āagman
            </span>
          </div>

          <p className="text-[#181E29] text-sm font-medium font-['Inter'] leading-tight text-center">
            Your AI Trading Partner — trade smarter, faster, safer.
          </p>
        </div>
      </div>

      {/* Bottom Section */}
      <div className="z-10 mt-auto px-5 pb-6">
        <div className="mx-2 flex flex-col gap-1 text-center mb-6">
          <p className="text-gray-900 text-lg font-bold font-['Inter'] leading-relaxed">
            Talk to trade
          </p>
          <p className="text-[#181E29] text-base font-medium font-['Inter'] leading-normal">
            Talk or type your trades in your language. No menus, no friction –
            just say it and Āagman will place it.
          </p>
        </div>
        {showSessionExpired && (
          <NotificationCard
            variant="negative"
            title="Session expired"
            time="Now"
            description="Your session has expired. Please log in again to continue."
            actionText="Dismiss"
            onActionClick={() => {
              setShowSessionExpired(false);
              sessionStorage.removeItem("session_expired");
            }}
            isRead={false}
            className="mb-3"
          />
        )}
        <LoginButton
          onBeforeLogin={() => {
            if (showSessionExpired) setShowSessionExpired(false);
            sessionStorage.removeItem("session_expired");
          }}
        />
      </div>
    </div>
  );
};

export default LoginPage;
