/**
 * ChatHomePage Component
 *
 * This component implements a multi-view SPA interface with dynamic content switching.
 *
 * Features:
 * - Header with <PERSON><PERSON><PERSON> branding and broker info (persistent)
 * - Sidebar navigation spanning full height (persistent)
 * - Dynamic content area that switches based on active sidebar tab
 * - Zustand store for global sidebar state management
 * - Switch-style content navigator following ARCHITECTURE.md pattern
 *
 * Content Views:
 * - Chat: AI trading co-pilot interface with model selection and input
 * - Orders: Trading orders management and portfolio overview
 * - Monitoring: Real-time alerts and market monitoring dashboard
 * - Notifications: Trading notifications and activity feed
 *
 * Architecture:
 * - Global state management via useSidebarStore (Zustand)
 * - Switch-based content rendering similar to pageNavigator pattern
 * - Modular content components in components/content/ directory
 * - Header and Sidebar remain common across all views
 * - Content area dynamically renders based on activeTab state
 */

import React, { useEffect } from "react";
import Header from "../components/Header";
import Sidebar from "../components/Sidebar";
import ChatContent from "../components/content/ChatContent";
import OrdersContent from "../components/content/OrdersContent";
import MonitoringContent from "../components/content/MonitoringContent";
import NotificationsContent from "../components/content/NotificationsContent";
import ProfileContent from "../components/content/ProfileContent";
import DialogManager from "../components/DialogManager";
import { useSidebarStore } from "../stores/sidebarStore";
import { useWebSocketStore } from "../stores/websocketStore";
import { useNetworkStore } from "../stores/networkStore";
import { initializeTabSession } from "../utils/navigationManager";

const ChatHomePage: React.FC = () => {
  // console.warn("[ChatHomePage] Component rendering");
  const { activeTab } = useSidebarStore();
  const { isConnected } = useWebSocketStore();
  const { isOnline } = useNetworkStore();

  // 🆕 AUTO-CLOSE LOGIC: If ChatHomePage opens in web tab during extension login, close it
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const mode = urlParams.get("mode");

    // Check if we're in extension mode (web tab opened by extension)
    if (mode === "extension") {
      // console.warn(
      //   "[ChatHomePage] 🚨 ChatHomePage opened in web tab during extension login!"
      // );
      // console.warn(
      //   "[ChatHomePage] 🔒 This should not happen - extension should handle navigation"
      // );
      // console.warn("[ChatHomePage] 🗑️ Auto-closing web tab in 1 second...");

      // Close the tab after a short delay to allow logs to be seen
      setTimeout(() => {
        // console.warn(
        //   "[ChatHomePage] ✅ Closing web tab - extension should handle ChatHomePage"
        // );
        window.close();
      }, 1000);

      return; // Don't initialize anything else
    }

    // Also check if we're in an extension context but somehow ended up in web
    if (
      window.location.protocol === "http:" ||
      window.location.protocol === "https:"
    ) {
      // We're in a web browser
      const extensionIndicators = [
        window.location.search.includes("mode=extension"),
        document.referrer.includes("chrome-extension://"),
        window.opener &&
          window.opener.location.protocol === "chrome-extension:",
      ];

      if (extensionIndicators.some((indicator) => indicator)) {
        // console.warn(
        //   "[ChatHomePage] 🚨 Extension login detected in web context!"
        // );
        // console.warn(
        //   "[ChatHomePage] 🗑️ Auto-closing to prevent duplicate UI..."
        // );

        setTimeout(() => {
          window.close();
        }, 1000);

        return;
      }
    }
  }, []);

  // Initialize session and load chat history
  useEffect(() => {
    let isMounted = true;
    // console.warn("[ChatHomePage] Effect triggered with activeTab:", activeTab);

    const loadHistory = async () => {
      // Check if tab is already initialized (only for main tabs)
      const websocketStore = useWebSocketStore.getState();
      const isInitialized =
        activeTab === "chat" ||
        activeTab === "orders" ||
        activeTab === "monitoring"
          ? websocketStore.isTabInitialized(
              activeTab as "chat" | "orders" | "monitoring"
            )
          : false;

      // console.warn(`[ChatHomePage] Checking for ${activeTab}:`, {
      //   isInitialized,
      //   messageCount:
      //     activeTab === "chat"
      //       ? websocketStore.chatMessages.length
      //       : activeTab === "orders"
      //         ? websocketStore.orderMessages.length
      //         : websocketStore.monitoringMessages.length,
      //   isMounted,
      //   chatMessages: websocketStore.chatMessages.length,
      //   orderMessages: websocketStore.orderMessages.length,
      //   monitoringMessages: websocketStore.monitoringMessages.length,
      // });

      // Skip if tab is already initialized or unmounted
      if (isInitialized || !isMounted) {
        // console.warn(
        //   `[ChatHomePage] Skipping history load for ${activeTab} - already initialized or unmounted`
        // );
        return;
      }

      // console.warn("[ChatHomePage] Loading history for tab:", activeTab);
      if (
        activeTab === "chat" ||
        activeTab === "orders" ||
        activeTab === "monitoring"
      ) {
        // Mark as initialized immediately to prevent duplicate calls
        websocketStore.markTabAsInitialized(
          activeTab as "chat" | "orders" | "monitoring"
        );
        await initializeTabSession(activeTab);
        // console.warn(`[ChatHomePage] History loaded for ${activeTab}`);
      }
    };

    loadHistory();

    // Cleanup function
    return () => {
      // console.warn("[ChatHomePage] Effect cleanup for tab:", activeTab);
      isMounted = false;
    };
  }, [activeTab]); // Only depend on activeTab

  // React to network changes
  useEffect(() => {
    // Send network status to worker
    if (window.worker) {
      window.worker.postMessage({
        type: "NETWORK_STATUS_CHANGE",
        payload: { isOnline },
      });
    }
  }, [isOnline]);

  const handleHeaderClockClick = () => {
    console.warn("Clock clicked");
  };

  const handleHeaderPinClick = () => {
    console.warn("Pin clicked");
  };

  // Content renderer switch - following ARCHITECTURE.md pattern
  const renderContent = () => {
    switch (activeTab) {
      case "chat":
        return <ChatContent />;
      case "orders":
        return <OrdersContent />;
      case "monitoring":
        return <MonitoringContent />;
      case "notifications":
        return <NotificationsContent />;
      case "profile":
        return <ProfileContent />;
      default:
        return <ChatContent />;
    }
  };

  return (
    <div
      className="flex min-h-screen bg-[#f4f6fa] w-full"
      style={{
        height: "100dvh",
        minHeight: "-webkit-fill-available",
      }}
    >
      {/* Connection Status */}
      {!isOnline && (
        <div className="fixed top-0 left-0 right-0 bg-red-500 text-white p-2 text-center">
          You are offline. Please check your internet connection.
        </div>
      )}
      {/* Error banner removed - errors now appear as messages in chat */}
      {isOnline && !isConnected && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-white p-2 text-center">
          Connecting to chat server...
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-0 w-0">
        {/* Header - Fixed */}
        <div className="shrink-0">
          <Header
            className="rounded-none"
            brokerName="Zerodha"
            onClockClick={handleHeaderClockClick}
            onPinClick={handleHeaderPinClick}
          />
        </div>

        {/* Dynamic Content Area - Scrollable */}
        <div className="flex-1 min-h-0 overflow-y-auto">{renderContent()}</div>
      </div>

      {/* Sidebar - Fixed, spanning full height */}
      <div className="shrink-0 h-full min-h-screen flex flex-col">
        <Sidebar className="flex-1 h-full" />
      </div>

      {/* Dialog */}
      <DialogManager />
    </div>
  );
};

export default ChatHomePage;
