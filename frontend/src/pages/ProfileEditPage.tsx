import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogClose,
} from "../components/Dialog";
import Input from "../components/Input";
import VerifiableEmailInput from "../components/VerifiableEmailInput";
import OTPScreen from "../components/OTPScreen";
import { cn } from "../utils/cn";
import CloseIcon from "../assets/x.svg";

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Verification states
type VerificationState = "unverified" | "verifying" | "otp-sent" | "verified";

interface ProfileEditDialogProps {
  children: React.ReactNode;
}

interface FormData {
  phoneNumber: string;
  fullName: string;
  email: string;
}

const ProfileEditDialog: React.FC<ProfileEditDialogProps> = ({ children }) => {
  const [formData, setFormData] = useState<FormData>({
    phoneNumber: "",
    fullName: "",
    email: "",
  });

  const [emailVerificationState, setEmailVerificationState] =
    useState<VerificationState>("unverified");
  const [verificationAction, setVerificationAction] = useState<
    "send-otp" | "verify-otp"
  >("send-otp");
  const [isValidEmail, setIsValidEmail] = useState(false);
  const [isValidPhone, setIsValidPhone] = useState(false);
  const [otpValue, setOtpValue] = useState("");
  const [otpError, setOtpError] = useState("");

  // Dummy API functions
  const sendEmailVerificationOTP = async (email: string) => {
    console.warn("API Call: Sending OTP to email:", email);
    // Simulate API call delay
    return new Promise((resolve) => setTimeout(resolve, 2000));
  };

  const verifyEmailOTP = async (email: string, otp: string) => {
    console.warn("API Call: Verifying OTP for email:", email, "OTP:", otp);
    // Simulate API call delay
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Mock OTP verification - accept "123456" as correct
        if (otp === "123456") {
          resolve(true);
        } else {
          reject(new Error("Invalid OTP"));
        }
      }, 1500);
    });
  };

  // Check if email is valid and manage verification state when email changes
  useEffect(() => {
    const isValid = EMAIL_REGEX.test(formData.email);
    setIsValidEmail(isValid);

    // Only reset verification state if email becomes invalid or empty
    // Don't interfere with ongoing verification process
    if (!formData.email || !isValid) {
      setEmailVerificationState("unverified");
      setVerificationAction("send-otp");
      setOtpError(""); // Clear any OTP errors
    }
  }, [formData.email]);

  // Check if phone number is valid (9-10 digits)
  useEffect(() => {
    const phoneDigits = formData.phoneNumber.replace(/\D/g, ""); // Remove non-digits
    const isValid = phoneDigits.length >= 9 && phoneDigits.length <= 10;
    setIsValidPhone(isValid);
  }, [formData.phoneNumber]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleVerificationClick = async () => {
    if (!isValidEmail) return;

    if (verificationAction === "send-otp") {
      // Send OTP
      setEmailVerificationState("verifying");

      try {
        await sendEmailVerificationOTP(formData.email);
        setEmailVerificationState("otp-sent");
        setVerificationAction("verify-otp");
        setOtpValue(""); // Clear any previous OTP
        setOtpError(""); // Clear any previous errors
      } catch (error) {
        console.error("Failed to send OTP:", error);
        setEmailVerificationState("unverified");
      }
    } else if (verificationAction === "verify-otp") {
      // Verify OTP
      if (!otpValue) return;

      setEmailVerificationState("verifying");

      try {
        await verifyEmailOTP(formData.email, otpValue);
        setEmailVerificationState("verified");
        setOtpValue("");
        setOtpError(""); // Clear any previous errors
      } catch (error) {
        console.error("Failed to verify OTP:", error);
        setEmailVerificationState("otp-sent");
        setOtpError("Invalid verification code. Please try again.");
      }
    }
  };

  const handleOTPComplete = (otp: string) => {
    setOtpValue(otp);
    // Auto-enable verify button when OTP is complete
    if (otp.length === 6) {
      setVerificationAction("verify-otp");
    }
  };

  const handleOTPChange = (otp: string) => {
    setOtpValue(otp);
    // Clear error when user starts typing
    if (otpError && otp) {
      setOtpError("");
    }
  };

  const handleSave = () => {
    console.warn("Saving profile data:", formData);
    // Implement save logic
  };

  const handleCancel = () => {
    console.warn("Cancelling profile edit");
    // Reset form or close dialog
  };

  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="h-[80vh] flex flex-col">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 border-b border-[#dee4f0] p-4">
          <DialogTitle className="text-lg font-semibold text-[#181e29]">
            Profile Edit
          </DialogTitle>
          <DialogClose className="absolute right-4 top-4">
            <img src={CloseIcon} alt="Close" className="w-6 h-6" />
          </DialogClose>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto bg-[#f4f6fa]">
          <div className="p-4 space-y-6 pb-24">
            {" "}
            {/* Extra padding bottom for footer */}
            {/* Phone Number Input */}
            <div className="space-y-2 w-full">
              <Input
                variant="phone"
                label="Phone number"
                value={formData.phoneNumber}
                onChange={(e) =>
                  handleInputChange("phoneNumber", e.target.value)
                }
                placeholder="9975846515"
                className="w-full"
                inputClassName={cn(
                  "w-full",
                  !isValidPhone && formData.phoneNumber && "border-red-500"
                )}
              />
              {!isValidPhone && formData.phoneNumber && (
                <p className="text-red-500 text-sm">
                  Phone number must be 9-10 digits
                </p>
              )}
            </div>
            {/* Full Name Input */}
            <Input
              variant="text"
              label="Full Name"
              value={formData.fullName}
              onChange={(e) => handleInputChange("fullName", e.target.value)}
              placeholder="Enter your full name"
              className="w-full"
              inputClassName="w-full"
            />
            {/* Email Address Input with Verification */}
            <VerifiableEmailInput
              value={formData.email}
              onChange={(e) => handleInputChange("email", e.target.value)}
              isValidEmail={isValidEmail}
              verificationState={emailVerificationState}
              onVerifyClick={handleVerificationClick}
              otpValue={otpValue}
              errorMessage={
                !isValidEmail && formData.email
                  ? "Please enter a valid email address"
                  : ""
              }
            />
            {/* OTP Verification Section */}
            {emailVerificationState === "otp-sent" && (
              <div className="bg-white rounded-lg border border-[#dee4f0] p-3 space-y-4">
                <OTPScreen
                  title="Verification code"
                  otpLength={6}
                  value={otpValue}
                  onChange={handleOTPChange}
                  onComplete={handleOTPComplete}
                  errorMessage={otpError}
                />

                <div className="text-center text-sm">
                  <span className="text-[#43556e]">Didn't get a code? </span>
                  <button
                    className="text-[#5c54fd] font-semibold hover:underline"
                    onClick={async () => {
                      setVerificationAction("send-otp");
                      setOtpError(""); // Clear any errors when resending
                      await handleVerificationClick();
                    }}
                  >
                    Click to resend
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Fixed Footer */}
        <div className="flex-shrink-0 bg-white border-t border-[#dee4f0] p-4">
          <div className="flex justify-end gap-4">
            <DialogClose asChild>
              <button
                onClick={handleCancel}
                className="px-4 py-3 bg-white border border-[#7d76fd] text-[#5c54fd] font-medium rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </DialogClose>

            <button
              onClick={handleSave}
              disabled={
                !formData.phoneNumber.trim() ||
                !isValidPhone ||
                !formData.fullName.trim() ||
                !formData.email.trim() ||
                emailVerificationState !== "verified"
              }
              className={cn(
                "px-4 py-3 bg-[#5c54fd] border-2 border-[#7d76fd] text-white font-medium rounded-lg transition-colors",
                !formData.phoneNumber.trim() ||
                  !isValidPhone ||
                  !formData.fullName.trim() ||
                  !formData.email.trim() ||
                  emailVerificationState !== "verified"
                  ? "opacity-50 cursor-not-allowed"
                  : "hover:bg-[#4a43ca]"
              )}
            >
              Save
            </button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileEditDialog;

/*
 * Usage Example:
 *
 * import ProfileEditDialog from '../pages/ProfileEditPage';
 *
 * const MyComponent = () => {
 *   return (
 *     <ProfileEditDialog>
 *       <button className="bg-blue-500 text-white px-4 py-2 rounded">
 *         Edit Profile
 *       </button>
 *     </ProfileEditDialog>
 *   );
 * };
 *
 * Features Implemented:
 * ✅ Dialog wrapper with "Profile Edit" header and close button
 * ✅ Phone number input with country dropdown (IN)
 * ✅ Full name input using Input component
 * ✅ Email input with regex validation
 * ✅ Green "Verify" button appears when email is valid
 * ✅ Loading spinner when verifying email
 * ✅ OTP screen appears after clicking verify
 * ✅ Check mark icon when email is verified
 * ✅ Fixed footer with Cancel and Save buttons
 * ✅ Scrollable content that goes behind footer
 * ✅ Proper state management for verification flow
 * ✅ Disabled save button until email is verified
 * ✅ Exact Figma design colors and spacing
 * ✅ Responsive layout with proper overflow handling
 *
 * Email Verification Flow:
 * 1. User enters valid email → "Verify" button appears
 * 2. User clicks "Verify" → Loading spinner shows
 * 3. After 2s → OTP screen appears below email input
 * 4. User enters correct OTP (123456) → Check mark appears, OTP disappears
 * 5. Save button becomes enabled
 *
 * Design Variables (from Figma):
 * - Background: #f4f6fa
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Success Green: #27ae60
 * - Brand Purple: #5c54fd
 * - Border: #dee4f0, #c6d0e3
 * - Input Background: #ffffff, #e9edf6
 */
