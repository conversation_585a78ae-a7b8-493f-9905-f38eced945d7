import React from "react";
import {
  Di<PERSON>,
  DialogTrigger,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
  DialogClose,
} from "../components/Dialog";
import Card from "../components/Card";
import InfoRow from "../components/InfoRow";
import CloseIcon from "../assets/x.svg";

interface AccountDetails {
  email: string;
  pan: string;
  phone: string;
  dematBO: string;
  segments: string;
  dematAuthorisation: string;
  bankAccount: {
    accountNumber: string; // 12 digits account number
    bankName: string;
  };
}

interface BrokerDetailsPageProps {
  children: React.ReactNode;
  brokerName: string;
  accountDetails: AccountDetails;
}

const BrokerDetailsPage: React.FC<BrokerDetailsPageProps> = ({
  children,
  brokerName,
  accountDetails,
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="h-[80vh] flex flex-col">
        {/* Header */}
        <DialogHeader className="flex-shrink-0 border-b border-[#dee4f0] p-4">
          <DialogTitle className="text-lg font-semibold text-[#181e29]">
            {brokerName}
          </DialogTitle>
          <DialogClose className="absolute right-4 top-4">
            <img src={CloseIcon} alt="Close" className="w-6 h-6" />
          </DialogClose>
        </DialogHeader>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto bg-[#f4f6fa]">
          <div className="p-4 space-y-4">
            {/* Account Details Card */}
            <Card className="border border-[#dee4f0]">
              <div className="p-3 space-y-4">
                <h3 className="font-bold text-[18px] leading-[26px] text-[#181e29]">
                  Account Details
                </h3>
                <div className="space-y-4">
                  <InfoRow label="E-mail" value={accountDetails.email} />
                  <InfoRow label="PAN" value={accountDetails.pan} />
                  <InfoRow label="Phone" value={accountDetails.phone} />
                  <InfoRow label="Demat (BO)" value={accountDetails.dematBO} />
                  <InfoRow label="Segments" value={accountDetails.segments} />
                  <InfoRow
                    label="Demat authorisation"
                    value={accountDetails.dematAuthorisation}
                  />
                </div>
              </div>
            </Card>

            {/* Bank Account Card */}
            <Card className="border border-[#dee4f0]">
              <div className="p-3 space-y-4">
                <h3 className="font-bold text-[18px] leading-[26px] text-[#181e29]">
                  Bank Account
                </h3>
                <div className="space-y-4">
                  <InfoRow
                    label="E-Bank"
                    value={`*${accountDetails.bankAccount.accountNumber.slice(-4)} ${accountDetails.bankAccount.bankName}`}
                  />
                </div>
              </div>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BrokerDetailsPage;

/*
 * Usage Example:
 *
 * import BrokerDetailsPage from '../pages/BrokerDetailsPage';
 * import CTAButton from '../components/CTAButton';
 *
 * const MyComponent = () => {
 *   const sampleAccountDetails = {
 *     email: "<EMAIL>",
 *     pan: "*248F",
 *     phone: "*9215",
 *     dematBO: "****************",
 *     segments: "BSE, MF, NSE",
 *     dematAuthorisation: "eDIS",
 *     bankAccount: {
 *       accountNumber: "************",
 *       bankName: "HDFC BANK LTD"
 *     }
 *   };
 *
 *   return (
 *     <BrokerDetailsPage
 *       brokerName="Zerodha"
 *       accountDetails={sampleAccountDetails}
 *     >
 *       <CTAButton>View Broker Details</CTAButton>
 *     </BrokerDetailsPage>
 *   );
 * };
 *
 * Features Implemented:
 * ✅ Dialog wrapper with broker name as title and close button
 * ✅ Two Card components for Account Details and Bank Account
 * ✅ Modular InfoRow component for all label-value pairs
 * ✅ Exact Figma design colors and spacing
 * ✅ Scrollable content with proper overflow handling
 * ✅ TypeScript interfaces for data structure
 * ✅ Responsive layout matching Figma specifications
 * ✅ Proper border and background styling
 *
 * Design Variables (from Figma):
 * - Background: #f4f6fa
 * - Card Background: #ffffff
 * - Text Primary: #181e29
 * - Text Secondary: #43556e
 * - Border: #dee4f0
 * - Title Font: Inter Bold 18px/26px
 * - Body Font: Inter Regular/Medium 16px/24px
 * - Spacing: 16px padding, 12px card padding, 4px gaps
 * - Border Radius: 16px (rounded-2xl)
 */
