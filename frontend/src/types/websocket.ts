/**
 * WebSocket Communication Types
 * Types for WebSocket messages and trading platform communication
 */

import type {
  OrderType,
  ProductType,
  MessageType,
  WebSocketMessageType,
} from "./trading";

/** Primitive action for trade execution - compatible with existing PrimitiveType */
export interface TradePrimitive {
  id?: string;
  action?: string; // Made optional to match existing PrimitiveType
  arguments?: Record<string, unknown>; // Made optional to match existing PrimitiveType
  human_friendly_explanation?: string;
  need_more_info?: string[];
  clarification?: string;
  // Order-specific fields
  symbol?: string;
  quantity?: number;
  price?: number;
  orderType?: OrderType;
  product?: ProductType;
  // Monitoring-specific fields
  condition?: {
    symbol?: string; // Made optional for flexibility
    operator?: string; // Made optional for flexibility
    value?: number; // Made optional for flexibility
  };
  on_trigger?: {
    action?: string; // Made optional for flexibility
    symbol?: string;
    quantity?: number;
    arguments?: Record<string, unknown>;
  };
}

/** Structured WebSocket response from backend */
export interface WebSocketResponse {
  user_id: string;
  conversation_id: string;
  textMessage: string;
  messageType: WebSocketMessageType;
  primitives?: TradePrimitive[];
  actions?: Array<{
    description: string;
    type: MessageType;
    message: string;
  }>;
  sender: "user" | "system";
  typeOfMessage?: MessageType;
  // Backend-provided exec id to correlate orders/alerts
  execution_request_id?: string;
  // UI enhancement fields
  executionOrders?: ExecutionOrderStatus[];
  executionCompleted?: boolean;
  monitoringStarted?: boolean;
  // Optional group fields
  group_human_friendly_explanation?: string;
  group_clarification_message?: string;
}

/** Order status for execution progress display - compatible with websocketStore */
export interface ExecutionOrderStatus {
  id: string;
  symbol: string;
  status: string; // Use string to match websocketStore.ts
  quantity?: number;
  price?: string; // Use string only to match websocketStore.ts
  orderType?: string; // Use string to match websocketStore.ts
  product?: string; // Use string to match websocketStore.ts
  broker?: string; // Use string to match websocketStore.ts
  exchange?: string;
  timestamp?: string;
  action_id?: string;
  primitive?: string;
  // Monitoring-specific fields
  isMonitoring?: boolean;
  triggerPrice?: string | number;
  currentPrice?: string | number;
  conditionOperator?: string;
  conditionValue?: string | number;
  onTriggerAction?: string;
  onTriggerQuantity?: number;
  onTriggerSymbol?: string;
}

/** Message history structure */
export interface MessageHistory {
  id: string;
  timestamp: number;
  data: WebSocketResponse | UserMessage;
}

/** User message structure */
export interface UserMessage {
  sender: "user";
  message?: string;
  textMessage?: string;
}

/** Main thread message for worker communication */
export interface MainThreadMessage {
  type: "CONNECTION_STATUS" | "WEBSOCKET_MESSAGE" | "ERROR";
  payload: WebSocketResponse | ConnectionStatus | ErrorPayload;
}

/** Connection status payload */
export interface ConnectionStatus {
  connected: boolean;
  reconnectAttempts?: number;
  lastError?: string;
}

/** Error payload structure */
export interface ErrorPayload {
  error: string;
  details?: string;
  timestamp: number;
}
