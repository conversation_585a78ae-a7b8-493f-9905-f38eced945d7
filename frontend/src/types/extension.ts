/**
 * Chrome Extension Types
 * Types for Chrome extension API interactions and context detection
 */

/** Chrome tab object */
export interface ChromeTab {
  id?: number;
  url?: string;
  title?: string;
  active?: boolean;
  index?: number;
  windowId?: number;
}

/** Extension message structure */
export interface ExtensionMessage {
  type: string;
  [key: string]: unknown;
}

/** Extension message sender */
export interface ExtensionMessageSender {
  tab?: ChromeTab;
  id?: string;
  url?: string;
}

/** Extension message listener function */
export type ExtensionMessageListener = (
  message: ExtensionMessage,
  sender: ExtensionMessageSender,
  sendResponse: (response?: unknown) => void
) => boolean | void;

/** Chrome runtime API interface */
export interface ChromeRuntime {
  id?: string;
  sendMessage: (message: ExtensionMessage, callback?: (response: unknown) => void) => void;
  onMessage: {
    addListener: (callback: ExtensionMessageListener) => void;
    removeListener: (callback: ExtensionMessageListener) => void;
  };
}

/** Chrome tabs API interface */
export interface ChromeTabs {
  create: (createProperties: { url: string }, callback?: (tab: ChromeTab) => void) => void;
  sendMessage: (tabId: number, message: ExtensionMessage, callback?: (response: unknown) => void) => void;
  remove: (tabId: number, callback?: () => void) => void;
  query: (queryInfo: { active?: boolean; currentWindow?: boolean }, callback: (tabs: ChromeTab[]) => void) => void;
}

/** Chrome extension context detection */
export interface ChromeExtensionContext {
  chrome?: {
    runtime?: ChromeRuntime;
    tabs?: ChromeTabs;
  };
}

/** Login mode detection */
export type LoginMode = 'extension' | 'web';

/** Extension-specific message types */
export type ExtensionMessageType =
  | 'EXTENSION_SEND_OTP'
  | 'EXTENSION_VERIFY_OTP'
  | 'LOGIN_COMPLETE'
  | 'BROKER_LOGIN_REQUIRED'
  | 'BROKER_LOGIN_SUCCESS'
  | 'EXTENSION_READY'
  | 'TAB_NAVIGATION';

/** OTP-related message interfaces */
export interface OTPSendMessage extends ExtensionMessage {
  type: 'EXTENSION_SEND_OTP';
  phone: string;
  captcha?: string;
}

export interface OTPVerifyMessage extends ExtensionMessage {
  type: 'EXTENSION_VERIFY_OTP';
  otp: string;
}

export interface LoginCompleteMessage extends ExtensionMessage {
  type: 'LOGIN_COMPLETE';
  success: boolean;
  userId?: string;
  sessionData?: Record<string, unknown>;
}
