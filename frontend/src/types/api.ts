/**
 * API Client Types
 * Types for API requests, responses, and client operations
 */

import type { Broker<PERSON>ame, MessageType, WebSocketMessageType } from "./trading";
import type { TradePrimitive } from "./websocket";

/** Generic API response wrapper */
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/** Authentication API types */
export interface SignupRequest {
  firebase_token: string;
  name: string;
  phone?: string;
}

export interface SignupResponse {
  user_id: string;
  success: boolean;
}

export interface UserCheckRequest {
  firebase_token: string;
}

export interface UserCheckResponse {
  userExists: boolean;
  sessionUserId?: string;
}

/** Profile API types */
export interface ProfileData {
  name?: string;
  phone?: string;
  email?: string;
  broker?: BrokerName;
}

export interface ProfileUpdateRequest {
  firebase_token: string;
  profile_data: ProfileData;
}

export interface ProfileUpdateResponse {
  success: boolean;
  message?: string;
}

/** Chat history API types */
export interface ChatHistoryRequest {
  user_id: string;
  conversation_id?: string;
  type: MessageType;
  brokerName: BrokerName;
}

export interface ChatHistoryItem {
  sender: "user" | "system";
  textMessage: string;
  timestamp: string;
  messageType?: WebSocketMessageType;
  primitives?: TradePrimitive[];
  actions?: Array<{
    description: string;
    type: MessageType;
    message: string;
  }>;
  group_human_friendly_explanation?: string;
  group_clarification_message?: string;
  execution_request_id?: string;
}

export interface ChatHistoryResponse {
  user_id: string;
  conversation_id: string;
  type: MessageType;
  brokerName: BrokerName;
  history: ChatHistoryItem[];
}

/** HTTP method types */
export type HttpMethod = "GET" | "POST" | "PUT" | "DELETE" | "PATCH";

/** API client configuration */
export interface ApiClientConfig {
  baseUrl: string;
  timeout?: number;
  headers?: Record<string, string>;
}

/** Request configuration */
export interface RequestConfig {
  url: string;
  method: HttpMethod;
  headers?: Record<string, string>;
  body?: unknown;
  params?: Record<string, string>;
}

/** Error response from API */
export interface ApiError {
  error: string;
  message?: string;
  status?: number;
  details?: unknown;
}

/** Pagination parameters */
export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

/** Paginated response */
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

/** Minimal Firebase User interface for auth operations */
export interface PartialFirebaseUser {
  uid: string;
  email?: string | null;
  phoneNumber?: string;
  emailVerified?: boolean;
  displayName?: string | null;
  getIdToken?: (forceRefresh?: boolean) => Promise<string>;
}
