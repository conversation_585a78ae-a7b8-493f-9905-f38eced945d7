/**
 * Trading & Financial Domain Types
 * Core types for the trading platform
 */

/** Supported brokers in the platform */
export type BrokerName = 'zerodha' | 'upstox' | 'angelone' | 'fyers' | 'aliceblue';

/** Order types supported across brokers */
export type OrderType = 'MARKET' | 'LIMIT' | 'SL-M' | 'SL-L';

/** Product types for trading */
export type ProductType = 'MIS' | 'CNC' | 'NRML';

/** Order status from broker systems */
export type OrderStatus = 'pending' | 'inProgress' | 'executed' | 'cancelled' | 'rejected';

/** Monitoring alert status */
export type MonitoringStatus = 'pending' | 'inProgress' | 'triggered' | 'stopped';

/** Message types for different tabs */
export type MessageType = 'chat' | 'orders' | 'monitoring';

/** WebSocket message classification */
export type WebSocketMessageType =
  | 'order_confirmation'
  | 'monitor_order'
  | 'chat_response'
  | 'order_planning'
  | 'order_execution';

/** Trade direction */
export type TradeType = 'buy' | 'sell';

/** Exchange identifiers */
export type Exchange = 'NSE' | 'BSE' | 'NFO' | 'BFO' | 'MCX';

/** Market segment */
export type MarketSegment = 'equity' | 'commodity' | 'currency' | 'derivatives';
