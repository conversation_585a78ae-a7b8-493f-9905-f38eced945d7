/**
 * PouchDB Document Types
 * Types for PouchDB documents and database operations
 */

import type {
  BrokerName,
  OrderType,
  ProductType,
  OrderStatus,
  MonitoringStatus,
  TradeType,
  Exchange,
} from "./trading";
import type { TradePrimitive } from "./websocket";

/** Base document interface for PouchDB */
export interface PouchDocument {
  _id?: string;
  _rev?: string;
  type: string;
  firebase_uid: string;
  created_at: string;
  updated_at: string;
}

/** Execution request document */
export interface ExecutionRequest extends PouchDocument {
  type: "execution_request";
  conversation_id: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  primitives: TradePrimitive[];
}

/** Order result document from PouchDB */
export interface OrderDocument extends PouchDocument {
  type: "order_result";
  id: string;
  symbol: string;
  tradeType: TradeType;
  quantity: string | number;
  price: string;
  status: OrderStatus;
  broker_status?: string;
  timestamp: string;
  orderType: OrderType;
  product: ProductType;
  broker: BrokerName;
  exchange?: Exchange;
  market?: string;
  action?: string; // NEW: action like BUY/SELL
  execution_request_id?: string;
  primitive?: string; // NEW: display action/primitive label (e.g., BUY, SELL, MONITOR)
  action_id?: string;
  triggerPrice?: string;
  limitPrice?: string;
  execution_details?: Record<string, string>; // NEW: detailed execution info
}

/** Monitoring alert document from PouchDB */
export interface MonitoringDocument extends PouchDocument {
  type: "monitoring_alert";
  id: string;
  description: string;
  symbol: string;
  triggerPrice: string;
  currentPrice: string;
  progress: string;
  progressPercent: number;
  status: MonitoringStatus;
  orderType: OrderType;
  stopLoss: string;
  product: ProductType;
  execution_request_id?: string;
  action_id?: string;
  condition?: {
    symbol: string;
    operator: string;
    value: number;
  };
  onTrigger?: {
    action: string;
    ACTION?: string;
    symbol?: string;
    SYMBOL?: string;
    quantity?: number;
    QUANTITY?: number;
    arguments?: Record<string, unknown>;
  };
}

/** PouchDB change event */
export interface PouchChangeEvent<T = PouchDocument> {
  id: string;
  seq: number;
  changes: Array<{ rev: string }>;
  doc?: T;
  deleted?: boolean;
}

/** PouchDB query options */
export interface PouchQueryOptions {
  include_docs?: boolean;
  descending?: boolean;
  limit?: number;
  skip?: number;
  startkey?: string;
  endkey?: string;
}

/** PouchDB bulk docs response */
export interface PouchBulkDocsResponse {
  ok: boolean;
  id: string;
  rev: string;
}

/** PouchDB find selector */
export interface PouchFindSelector {
  [field: string]: unknown;
}

/** PouchDB find options */
export interface PouchFindOptions {
  selector: PouchFindSelector;
  fields?: string[];
  sort?: Array<string | { [field: string]: "asc" | "desc" }>;
  limit?: number;
  skip?: number;
  use_index?: string;
}

/** PouchDB find result */
export interface PouchFindResult<T = PouchDocument> {
  docs: T[];
  bookmark?: string;
  warning?: string;
}
