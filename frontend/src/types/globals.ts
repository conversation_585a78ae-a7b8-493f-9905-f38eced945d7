/**
 * Global & Polyfill Types
 * Types for global objects, polyfills, and browser compatibility
 */

/** OTP promise resolvers for extension communication */
export interface OTPPromiseResolvers {
  resolve: (value: void) => void;
  reject: (reason: unknown) => void;
}

/** Global polyfill declarations - extends existing vite-env.d.ts */
declare global {
  interface Window {
    // Extend existing Window interface from vite-env.d.ts
    otpPromiseResolvers?: OTPPromiseResolvers;
  }
}

/** Environment variable types */
export interface EnvironmentVariables {
  NODE_ENV?: "development" | "production" | "test";
  VITE_API_URL?: string;
  VITE_WS_URL?: string;
  VITE_USE_MOCK?: string;
  VITE_LOGIN_MODE?: string;
}

/** Build configuration types */
export interface BuildConfig {
  useMock: boolean;
  loginMode: "extension" | "web";
  apiUrl: string;
  wsUrl: string;
}
