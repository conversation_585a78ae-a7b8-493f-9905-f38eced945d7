/**
 * Type Definitions Index
 * Centralized exports for all type definitions
 */

// Trading & Financial Domain Types
export type {
  BrokerName,
  OrderType,
  ProductType,
  OrderStatus,
  MonitoringStatus,
  MessageType,
  WebSocketMessageType,
  TradeType,
  Exchange,
  MarketSegment,
} from "./trading";

// WebSocket Communication Types
export type {
  TradePrimitive,
  WebSocketResponse,
  ExecutionOrderStatus,
  MessageHistory,
  UserMessage,
  MainThreadMessage,
  ConnectionStatus,
  ErrorPayload,
} from "./websocket";

// Chrome Extension Types
export type {
  ChromeTab,
  ExtensionMessage,
  ExtensionMessageSender,
  ExtensionMessageListener,
  ChromeRuntime,
  ChromeTabs,
  ChromeExtensionContext,
  LoginMode,
  ExtensionMessageType,
  OTPSendMessage,
  OTPVerifyMessage,
  LoginCompleteMessage,
} from "./extension";

// PouchDB Document Types
export type {
  PouchDocument,
  ExecutionRequest,
  OrderDocument,
  MonitoringDocument,
  PouchChangeEvent,
  PouchQueryOptions,
  PouchBulkDocsResponse,
  PouchFindSelector,
  PouchFindOptions,
  PouchFindResult,
} from "./database";

// API Client Types
export type {
  ApiResponse,
  SignupRequest,
  SignupResponse,
  UserCheckRequest,
  UserCheckResponse,
  ProfileData,
  ProfileUpdateRequest,
  ProfileUpdateResponse,
  ChatHistoryRequest,
  ChatHistoryItem,
  ChatHistoryResponse,
  HttpMethod,
  ApiClientConfig,
  RequestConfig,
  ApiError,
  PaginationParams,
  PaginatedResponse,
  PartialFirebaseUser,
} from "./api";

// Global & Polyfill Types
export type {
  OTPPromiseResolvers,
  EnvironmentVariables,
  BuildConfig,
} from "./globals";
