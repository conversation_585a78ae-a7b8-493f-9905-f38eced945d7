# Project Scratchpad

## Background and Motivation

The objective is to build the foundational structure and navigation for a new Vite-based Single Page Application (SPA), following the specifications outlined in `ARCHITECTURE.md`. The core of the project is to implement a custom, state-driven page navigator using Zustand for state management, which will control the application's flow based on a page stack.

---

## Key Challenges and Analysis

The primary challenge is ensuring the application's internal state (the Zustand `navStore`) remains perfectly synchronized with the browser's URL and history. The architecture document details how to push new states to the browser history but omits handling two critical scenarios:

1.  **Initial URL Loading:** The app must initialize its navigation state from the URL when a user lands on a deep link (e.g., `/profile`).
2.  **Browser Navigation:** The app must listen for and handle the browser's back and forward button events (`onpopstate`) to update the navigation state accordingly.

My plan explicitly includes tasks to address these gaps, ensuring the navigation feels robust and predictable to the end-user.

---

## High-level Task Breakdown

1.  **Install Dependencies:** Add `zustand` to the project to manage state.
2.  **Create Directory Structure:** Set up the folders for `navigation`, `pages`, `stores`, and `utils` as defined in the architecture.
3.  **Configure Vite:** Update `vite.config.ts` to define the `__USE_MOCK__` global variable for switching data sources.
4.  **Implement API Client:** Create the `apiClient.ts` utility that respects the `__USE_MOCK__` flag.
5.  **Implement Zustand Store:** Create `navStore.ts` with the navigation stack and related state management logic (`stack`, `isAuthenticated`, state-mutating actions, etc.).
6.  **Implement Navigation Logic:** Create `pageNavigator.ts` with the core `switch`-based logic to determine navigation outcomes.
7.  **Create Placeholder Page Components:** Create the four required page components (`Home`, `Chat`, `Profile`, `Settings`) that render basic text.
8.  **Implement Root `App` Component:** Modify `App.tsx` to read from the `navStore` and render the correct page component based on the navigation stack.
9.  **Synchronize with Browser History:** Enhance `App.tsx` to initialize state from the URL on load and add a `popstate` event listener to handle browser back/forward buttons.
10. **Add Navigation UI:** Add basic buttons to the page components to trigger the `navigate()` and `goBack()` functions to test the end-to-end flow.
11. **(Optional) Create Mock Data:** Add mock JSON files in `/public/assets/mock-data` to allow for testing with the `VITE_USE_MOCK` flag enabled.

---

## Project Status Board

- [x] **Task 1:** Install Dependencies
- [x] **Task 2:** Create Directory Structure
- [x] **Task 3:** Configure Vite
- [x] **Task 4:** Implement API Client
- [x] **Task 5:** Implement Zustand Store
- [x] **Task 6:** Implement Navigation Logic
- [x] **Task 7:** Create Placeholder Page Components
- [x] **Task 8:** Implement Root `App` Component
- [x] **Task 9:** Synchronize with Browser History
- [x] **Task 10:** Add Navigation UI
- [x] **Task 11:** (Optional) Create Mock Data

---

## Current Status / Progress Tracking

*(The Executor will document its progress on the current task here.)*

---

## Executor's Feedback or Assistance Requests

*(The Executor will log any blockers, questions, or requests for help here.)*

---

## Lessons

*(Document reusable findings, bug fixes, or key learnings here to avoid repeating mistakes.)*

### User Specified Lessons
- Include info useful for debugging in the program output.
- Read the file before you try to edit it.
- If there are vulnerabilities that appear in the terminal, run `npm audit` before proceeding.
- Always ask before using the `--force` git command.
