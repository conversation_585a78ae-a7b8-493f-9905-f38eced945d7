import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { nodePolyfills } from "vite-plugin-node-polyfills";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  console.warn(`Running in mode: ${mode}`);

  // Load env file(s) based on mode
  // For mode 'development', will load .env.development
  // For mode 'staging', will load .env.staging
  // For mode 'production', will load .env.production
  // Note: .env and .env.local is loaded automatically for all modes except 'test'
  const env = loadEnv(mode, process.cwd(), "");

  console.warn(`Using mock: ${env.VITE_USE_MOCK === "true"}`);
  console.warn(`Login mode: ${env.VITE_LOGIN_MODE || "extension"}`);
  console.warn(`Base URL: ${env.VITE_API_BASE_URL || "default"}`);
  console.warn(`WebSocket URL: ${env.VITE_WS_URL || "default"}`);

  const plugins = [react(), nodePolyfills()];

  return {
    plugins,
    define: {
      __USE_MOCK__: JSON.stringify(env.VITE_USE_MOCK === "true"),
      __WS_URL__: JSON.stringify(env.VITE_WS_URL),
      __WS_CHAT_ENDPOINT__: JSON.stringify(env.VITE_WS_CHAT_ENDPOINT),
      __LOGIN_MODE__: JSON.stringify(env.VITE_LOGIN_MODE || "extension"),
      global: "globalThis",
      "process.env": "{}",
    },
    worker: {
      format: "es",
      plugins: () => [],
    },
    server: {
      allowedHosts: true,
    },
    optimizeDeps: {
      include: [
        "pouchdb",
        "pouchdb-find",
        "events",
        "util",
        "inherits",
        "buffer",
        "process/browser",
      ],
      esbuildOptions: {
        define: {
          global: "globalThis",
        },
      },
    },
    resolve: {
      alias: {
        immediate: "immediate/lib/index.js",
        events: "events",
        util: "util",
        inherits: "inherits",
        buffer: "buffer",
        process: "process/browser",
      },
    },
    build: {
      // Ensure proper minification and chunking
      minify: "terser",
      sourcemap: mode !== "production",
      // Use relative paths for assets and resources
      assetsDir: "",
      rollupOptions: {
        output: {
          // Create a single main chunk for the application
          manualChunks: undefined,
          // Ensure the main entry point is named index.js
          entryFileNames: "index.js",
          // Keep worker files separate
          chunkFileNames: "[name].js",
          assetFileNames: "[name].[ext]",
        },
      },
    },
    // Configure base path for relative URLs
    base: "./",
  };
});
