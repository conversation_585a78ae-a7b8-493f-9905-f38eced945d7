# OrderGPT Frontend Environment Variables
# Copy this file to .env.development, .env.staging, or .env.production and update the values

# ================================
# CORE CONFIGURATION
# ================================

# Mock Mode - Enable to use mock data instead of real API calls
# Set to 'true' for development with mock data, 'false' for real API integration
VITE_USE_MOCK=false

# Login Mode - Used to simulate the login flow for extension
# Set to 'auto' for regular behavior, 'extension' to simulate
VITE_LOGIN_MODE=auto

# ================================
# API CONFIGURATION
# ================================

# Base API URL - Main backend server address
VITE_API_BASE_URL=http://localhost:8000

# WebSocket URL - Real-time communication endpoint
VITE_WS_URL=ws://localhost:8000

# Frontend URL - Main frontend application URL
VITE_FRONTEND_URL=http://localhost:5173

# ================================
# CouchDB CONFIGURATION
# ================================
VITE_COUCHDB_URL=************************************
VITE_COUCHDB_DB_PREFIX=user_

# ================================
# API ENDPOINTS
# ================================

# Individual API endpoints (relative to base URL)
# These can be overridden if your backend uses different paths

# Notifications API endpoint
VITE_NOTIFICATIONS_ENDPOINT=/api/notifications

# User profile API endpoint
VITE_PROFILE_ENDPOINT=/api/profile

# Trading orders API endpoint
VITE_ORDERS_ENDPOINT=/api/v1/orders

# Monitoring alerts API endpoint
VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances

# Chat history API endpoint (for session initialization)
VITE_CHAT_ENDPOINT=/api/v1/chatHistory

# Health check API endpoint
VITE_HEALTH_ENDPOINT=/health

# WebSocket chat endpoint (for real-time messaging)
VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat

# ================================
# ENVIRONMENT SPECIFIC EXAMPLES
# ================================

# --- DEVELOPMENT ENVIRONMENT ---
# VITE_USE_MOCK=false
# VITE_API_BASE_URL=http://localhost:8000
# VITE_WS_URL=ws://localhost:8000
# VITE_FRONTEND_URL=http://localhost:5173
# VITE_NOTIFICATIONS_ENDPOINT=/api/notifications
# VITE_PROFILE_ENDPOINT=/api/profile
# VITE_ORDERS_ENDPOINT=/api/v1/orders
# VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances
# VITE_CHAT_ENDPOINT=/api/v1/chatHistory
# VITE_HEALTH_ENDPOINT=/health
# VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat

# --- STAGING ENVIRONMENT ---
# VITE_USE_MOCK=false
# VITE_API_BASE_URL=https://staging.aagman.ai
# VITE_WS_URL=wss://staging.aagman.ai
# VITE_FRONTEND_URL=https://staging.aagman.ai
# VITE_NOTIFICATIONS_ENDPOINT=/api/notifications
# VITE_PROFILE_ENDPOINT=/api/profile
# VITE_ORDERS_ENDPOINT=/api/v1/orders
# VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances
# VITE_CHAT_ENDPOINT=/api/v1/chatHistory
# VITE_HEALTH_ENDPOINT=/health
# VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat

# --- PRODUCTION ENVIRONMENT ---
# VITE_USE_MOCK=false
# VITE_API_BASE_URL=https://aagman.ai
# VITE_WS_URL=wss://aagman.ai
# VITE_FRONTEND_URL=https://aagman.ai
# VITE_NOTIFICATIONS_ENDPOINT=/api/notifications
# VITE_PROFILE_ENDPOINT=/api/profile
# VITE_ORDERS_ENDPOINT=/api/v1/orders
# VITE_MONITORING_ENDPOINT=/api/v1/monitoring/instances
# VITE_CHAT_ENDPOINT=/api/v1/chatHistory
# VITE_HEALTH_ENDPOINT=/health
# VITE_WS_CHAT_ENDPOINT=/api/v1/ws/chat

# ================================
# ADDITIONAL CONFIGURATION
# ================================
VITE_PUBLIC_POSTHOG_KEY=
VITE_PUBLIC_POSTHOG_UI_HOST=https://eu.i.posthog.com
VITE_PUBLIC_POSTHOG_API_HOST=https://ph.aagman.ai