name: Backend PR Checks

on:
  pull_request:
    branches:
      - main
    paths:
      - "backend_api_module/**"
      - ".github/workflows/backend-pr-checks.yml"

jobs:
  backend-checks:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.12"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Cache pip dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('backend_api_module/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-${{ matrix.python-version }}-

      - name: Install dependencies
        run: |
          cd backend_api_module
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Setup Firebase credentials
        run: |
          cd backend_api_module
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}' > firebase-service-account.json
          echo "✅ Firebase credentials configured"

      - name: Setup environment variables
        run: |
          cd backend_api_module
          cat > .env << EOF
          APP_ENV=test
          HOST=0.0.0.0
          PORT=8000
          DEBUG=false
          LOG_LEVEL=INFO
          JWT_SECRET_KEY=${{ secrets.JWT_SECRET_KEY }}
          JWT_ALGORITHM=HS256
          JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
          GOOGLE_APPLICATION_CREDENTIALS=firebase-service-account.json
          OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
          COUCHDB_HOST=localhost
          COUCHDB_PORT=5984
          COUCHDB_USERNAME=${{ secrets.COUCHDB_USERNAME }}
          COUCHDB_PASSWORD=${{ secrets.COUCHDB_PASSWORD }}
          COUCHDB_DATABASE=aagmanai
          COUCHDB_USE_SSL=false
          COUCHDB_USER_CRED_SALT=${{ secrets.COUCHDB_USER_CRED_SALT }}
          EOF
          echo "✅ Environment variables configured"

      - name: Run make check (format, lint, type-check)
        run: |
          cd backend_api_module
          make check

      - name: Verify app imports successfully
        run: |
          cd backend_api_module
          python -c "import src.main; print('✅ Backend app imports successfully')"

      - name: Verify FastAPI app creation
        run: |
          cd backend_api_module
          python -c "
          from src.main import app
          print(f'✅ FastAPI app created successfully: {type(app)}')
          print(f'✅ App title: {app.title}')
          print(f'✅ App version: {app.version}')
          "

      - name: Check for syntax errors
        run: |
          cd backend_api_module
          python -m py_compile src/main.py
          find src/ -name "*.py" -exec python -m py_compile {} \;
          echo "✅ All Python files compile successfully"

      - name: Cleanup sensitive files
        if: always()
        run: |
          cd backend_api_module
          rm -f firebase-service-account.json .env
          echo "✅ Sensitive files cleaned up"

  backend-build-verification:
    runs-on: ubuntu-latest
    needs: backend-checks
    if: always()

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.11
        uses: actions/setup-python@v4
        with:
          python-version: "3.11"

      - name: Install dependencies
        run: |
          cd backend_api_module
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Setup Firebase credentials
        run: |
          cd backend_api_module
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON }}' > firebase-service-account.json
          echo "✅ Firebase credentials configured"

      - name: Setup environment variables
        run: |
          cd backend_api_module
          cat > .env << EOF
          APP_ENV=test
          HOST=0.0.0.0
          PORT=8000
          DEBUG=false
          LOG_LEVEL=INFO
          JWT_SECRET_KEY=${{ secrets.JWT_SECRET_KEY }}
          JWT_ALGORITHM=HS256
          JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
          GOOGLE_APPLICATION_CREDENTIALS=firebase-service-account.json
          OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}
          COUCHDB_HOST=db.aagman.ai
          COUCHDB_PORT=80
          COUCHDB_USERNAME=${{ secrets.COUCHDB_USERNAME }}
          COUCHDB_PASSWORD=${{ secrets.COUCHDB_PASSWORD }}
          COUCHDB_DATABASE=aagmanai
          COUCHDB_USE_SSL=false
          COUCHDB_USER_CRED_SALT=${{ secrets.COUCHDB_USER_CRED_SALT }}
          EOF
          echo "✅ Environment variables configured"

      - name: Verify virtual environment setup
        run: |
          cd backend_api_module
          python -m venv .venv
          source .venv/bin/activate
          pip install -r requirements.txt
          echo "✅ Virtual environment setup successful"

      - name: Verify app startup (dry run)
        run: |
          cd backend_api_module
          source .venv/bin/activate
          # Test that the app can be imported and configured without starting the server
          python -c "
          import os
          os.environ['APP_ENV'] = 'test'
          os.environ['JWT_SECRET_KEY'] = '${{ secrets.JWT_SECRET_KEY }}'
          os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'firebase-service-account.json'
          from src.main import app
          print('✅ App configuration successful')
          print(f'App routes: {[route.path for route in app.routes]}')
          "

      - name: Cleanup sensitive files
        if: always()
        run: |
          cd backend_api_module
          rm -f firebase-service-account.json .env
          rm -rf .venv
          echo "✅ Sensitive files and virtual environment cleaned up"
