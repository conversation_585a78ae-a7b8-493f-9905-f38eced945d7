name: Frontend PR Checks

on:
  pull_request:
    branches:
      - main
    paths:
      - "frontend/**"
      - ".github/workflows/frontend-pr-checks.yml"

jobs:
  frontend-checks:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd frontend
          npm ci

      - name: Run lint
        run: |
          cd frontend
          npm run lint -- --max-warnings 0

      - name: Run build
        run: |
          cd frontend
          npm run build
