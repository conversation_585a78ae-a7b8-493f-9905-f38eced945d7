name: Build Executor Extension

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to build for"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - staging
          - production
      version_bump_type:
        description: "Version bump type for the release"
        required: false
        default: "patch"
        type: choice
        options:
          - patch
          - minor
          - major

jobs:
  build-executor:
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.get-version.outputs.version }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd frontend
          npm ci

      - name: Setup environment files
        run: |
          cd frontend
          # Copy environment example files for all environments
          cp env.example .env.development
          cp env.example .env.staging
          cp env.example .env.production

          # Set default values for required environment variables
          echo "VITE_API_BASE_URL=http://localhost:8000" >> .env.development
          echo "VITE_WS_URL=ws://localhost" >> .env.development
          echo "VITE_FRONTEND_URL=http://localhost" >> .env.development

          echo "VITE_API_BASE_URL=https://staging.aagman.ai" >> .env.staging
          echo "VITE_WS_URL=wss://staging.aagman.ai" >> .env.staging
          echo "VITE_FRONTEND_URL=https://staging.aagman.ai" >> .env.staging

          echo "VITE_API_BASE_URL=https://app.aagman.ai" >> .env.production
          echo "VITE_WS_URL=wss://app.aagman.ai" >> .env.production
          echo "VITE_FRONTEND_URL=https://app.aagman.ai" >> .env.production

      - name: Build extension
        run: |
          cd frontend
          npm run build:executor:${{ github.event.inputs.environment }}:${{ github.event.inputs.version_bump_type }}

      - name: Find zip file
        id: find-zip
        run: |
          # Find the most recent zip file in the root directory
          ZIP_FILE=$(find . -name "aagman-extension-*.zip" -type f | xargs ls -t | head -1)
          if [ -n "$ZIP_FILE" ]; then
            echo "zip_file=$ZIP_FILE" >> $GITHUB_OUTPUT
            echo "zip_name=$(basename "$ZIP_FILE")" >> $GITHUB_OUTPUT
            echo "Found zip file: $ZIP_FILE"
          else
            echo "No zip file found!"
            exit 1
          fi

      - name: Setup buildcrx tool
        run: |
          # Download and setup buildcrx tool
          mkdir -p tools
          cd tools

          # Download buildcrx for Linux (Ubuntu)
          wget -O buildcrx https://github.com/kylehuff/buildcrx/releases/download/v0.2/buildcrx_linux_x86_64
          chmod +x buildcrx

          # Verify the tool is executable
          ./buildcrx --help || echo "buildcrx tool setup complete"

      - name: Create CRX file
        id: create-crx
        run: |
          cd tools

          # Create private key file from repository secret
          if [ -n "${{ secrets.EXTENSION_PRIVATE_KEY }}" ]; then
            echo "${{ secrets.EXTENSION_PRIVATE_KEY }}" > ../executor/private_key.pem
            PRIVATE_KEY="../executor/private_key.pem"
          else
            echo "❌ EXTENSION_PRIVATE_KEY secret not found"
            exit 1
          fi

          # Verify the private key was created
          if [ -s "$PRIVATE_KEY" ]; then
            echo "✅ Private key loaded from repository secrets"
          else
            echo "❌ Failed to load private key from repository secrets"
            exit 1
          fi

          # Create CRX file from the zip
          ZIP_PATH="../${{ steps.find-zip.outputs.zip_file }}"
          # We'll use a temporary name first, then rename after getting the version
          CRX_OUTPUT="../aagman-extension-temp.crx"

          echo "Creating CRX file from: $ZIP_PATH"
          echo "Output CRX: $CRX_OUTPUT"

          # Create the CRX file
          ./buildcrx "$ZIP_PATH" "$PRIVATE_KEY" "$CRX_OUTPUT"

          if [ -f "$CRX_OUTPUT" ]; then
            echo "✅ CRX file created successfully: $CRX_OUTPUT"
            echo "crx_file=$CRX_OUTPUT" >> $GITHUB_OUTPUT
            echo "crx_name=$(basename "$CRX_OUTPUT")" >> $GITHUB_OUTPUT
          else
            echo "❌ Failed to create CRX file"
            exit 1
          fi

          # Clean up private key file for security
          rm -f "$PRIVATE_KEY"

          # Go back to root directory
          cd ..

      - name: Get updated version from manifest
        id: get-version
        run: |
          cd executor
          VERSION=$(node -e "console.log(JSON.parse(require('fs').readFileSync('manifest.json', 'utf8')).version)")
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "Updated version: $VERSION"

      - name: Rename CRX file with version
        id: rename-crx
        run: |
          # Rename the CRX file to include the version
          OLD_CRX="aagman-extension-temp.crx"
          NEW_CRX="aagman-extension-${{ steps.get-version.outputs.version }}.crx"

          if [ -f "$OLD_CRX" ]; then
            mv "$OLD_CRX" "$NEW_CRX"
            echo "✅ CRX file renamed to: $NEW_CRX"
            echo "crx_file=$NEW_CRX" >> $GITHUB_OUTPUT
            echo "crx_name=$NEW_CRX" >> $GITHUB_OUTPUT
          else
            echo "❌ CRX file not found for renaming: $OLD_CRX"
            exit 1
          fi

      - name: Configure Git
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: Commit updated manifest.json
        run: |
          git add executor/manifest.json
          git commit -m "chore: bump executor version to ${{ steps.get-version.outputs.version }} for ${{ github.event.inputs.environment }} environment (bump: ${{ github.event.inputs.version_bump_type }}) [skip ci]"

      - name: Create and push Git tag
        run: |
          git tag -a "v${{ steps.get-version.outputs.version }}-${{ github.event.inputs.environment }}" -m "Release ${{ steps.get-version.outputs.version }} for ${{ github.event.inputs.environment }} environment (bump: ${{ github.event.inputs.version_bump_type }})"
          git push origin "v${{ steps.get-version.outputs.version }}-${{ github.event.inputs.environment }}"

      - name: Push manifest changes
        run: |
          git push origin ${{ github.ref }}

      - name: Create GitHub Release
        id: create-release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: v${{ steps.get-version.outputs.version }}-${{ github.event.inputs.environment }}
          release_name: Aagman Extension ${{ steps.get-version.outputs.version }} (${{ github.event.inputs.environment }})
          body: |
            ## Aagman Extension Release

            **Version:** ${{ steps.get-version.outputs.version }}
            **Environment:** ${{ github.event.inputs.environment }}
            **Version Bump Type:** ${{ github.event.inputs.version_bump_type }}

            ### What's New

            This release includes the latest version of the Aagman Extension for the ${{ github.event.inputs.environment }} environment.

            ### Installation

            **Option 1: Load unpacked extension (Recommended for development)**
            1. Download the `${{ steps.find-zip.outputs.zip_name }}` file
            2. Unzip the file
            3. Load the extension in Chrome/Edge:
               - Go to `chrome://extensions/`
               - Enable "Developer mode"
               - Click "Load unpacked"
               - Select the unzipped extension folder

            **Option 2: Install signed CRX file (For production use)**
            1. Download the `${{ steps.rename-crx.outputs.crx_name }}` file
            2. Drag and drop the .crx file into Chrome/Edge extensions page
            3. Confirm the installation when prompted

            ### Changes

            - Version bumped from previous release
            - Built for ${{ github.event.inputs.environment }} environment
            - All dependencies updated and bundled

            ### Support

            For issues or questions, please check the repository or contact the development team.
          draft: false
          prerelease: false

      - name: Upload Release Asset (ZIP)
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create-release.outputs.upload_url }}
          asset_path: ${{ steps.find-zip.outputs.zip_file }}
          asset_name: ${{ steps.find-zip.outputs.zip_name }}
          asset_content_type: application/zip

      - name: Upload Release Asset (CRX)
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create-release.outputs.upload_url }}
          asset_path: ${{ steps.rename-crx.outputs.crx_file }}
          asset_name: ${{ steps.rename-crx.outputs.crx_name }}
          asset_content_type: application/x-chrome-extension

      - name: Display build info
        run: |
          echo "✅ Executor extension built and released successfully!"
          echo "📦 Environment: ${{ github.event.inputs.environment }}"
          echo "📦 Version: ${{ steps.get-version.outputs.version }}"
          echo "📦 Version bump type: ${{ github.event.inputs.version_bump_type }}"
          echo "📦 Zip file: ${{ steps.find-zip.outputs.zip_name }}"
          echo "📦 CRX file: ${{ steps.rename-crx.outputs.crx_name }}"
          echo "📦 Git tag: v${{ steps.get-version.outputs.version }}-${{ github.event.inputs.environment }}"
          echo "📦 Release: Aagman Extension ${{ steps.get-version.outputs.version }} (${{ github.event.inputs.environment }})"
          echo "📦 Run number: ${{ github.run_number }}"
          echo "📦 Release URL: https://github.com/${{ github.repository }}/releases/tag/v${{ steps.get-version.outputs.version }}-${{ github.event.inputs.environment }}"

      - name: Debug job outputs
        run: |
          echo "🔍 Debugging job outputs:"
          echo "Job outputs that will be available to other jobs:"
          echo "version: ${{ steps.get-version.outputs.version }}"
          echo "zip_file: ${{ steps.find-zip.outputs.zip_file }}"
          echo "zip_name: ${{ steps.find-zip.outputs.zip_name }}"
          echo "crx_file: ${{ steps.rename-crx.outputs.crx_file }}"
          echo "crx_name: ${{ steps.rename-crx.outputs.crx_name }}"

  # Build and push Docker images to GitHub Container Registry
  build-docker-images:
    needs: build-executor
    runs-on: ubuntu-latest
    if: github.event.inputs.environment != 'development'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push Backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend_api_module
          file: ./backend_api_module/Dockerfile
          push: true
          tags: |
            ghcr.io/${{ github.repository }}/aagman-backend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}
            ghcr.io/${{ github.repository }}/aagman-backend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Debug image tags
        run: |
          echo "Repository: ${{ github.repository }}"
          echo "Version: ${{ needs.build-executor.outputs.version }}"
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Backend image: ghcr.io/${{ github.repository }}/aagman-backend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}"
          echo "Frontend image: ghcr.io/${{ github.repository }}/aagman-frontend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}"

      - name: Build and push Frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          build-args: |
            BUILD_ENV=${{ github.event.inputs.environment }}
          tags: |
            ghcr.io/${{ github.repository }}/aagman-frontend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}
            ghcr.io/${{ github.repository }}/aagman-frontend:latest
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Debug final image names
        run: |
          echo "Final image names that will be created:"
          echo "Backend: ghcr.io/${{ github.repository }}/aagman-backend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}"
          echo "Frontend: ghcr.io/${{ github.repository }}/aagman-frontend:${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}"
          echo "Repository owner: ${{ github.repository_owner }}"
          echo "Full repository: ${{ github.repository }}"

  # Deploy to staging server
  deploy-to-staging:
    needs: [build-executor, build-docker-images]
    runs-on: self-hosted-staging
    if: github.event.inputs.environment == 'staging'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Prepare secrets (staging)
        env:
          FIREBASE_JSON: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON_STAGING }}
          BACKEND_ENV: ${{ secrets.BACKEND_ENV_STAGING }}
        run: |
          echo "Writing Firebase service account and backend env from secrets (staging)"
          mkdir -p backend_api_module
          [ -n "$FIREBASE_JSON" ] || { echo "FIREBASE_SERVICE_ACCOUNT_JSON_STAGING secret is empty or missing"; exit 1; }
          [ -n "$BACKEND_ENV" ] || { echo "BACKEND_ENV_STAGING secret is empty or missing"; exit 1; }
          printf "%s" "$FIREBASE_JSON" > backend_api_module/firebase-service-account.json
          printf "%s\n" "$BACKEND_ENV" > backend_api_module/.env.staging

      - name: Deploy application
        env:
          FIREBASE_CREDENTIALS_HOST_PATH: ${{ github.workspace }}/backend_api_module/firebase-service-account.json
          BACKEND_ENV_FILE: ${{ github.workspace }}/backend_api_module/.env.staging
        run: |
          echo "🚀 Deploying to staging server..."
          export TAG=${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}
          export APP_ENV=staging
          ./deploy/deploy.sh staging "$TAG"
          echo "✅ Staging deployment completed"

  # Deploy to production server
  deploy-to-production:
    needs: [build-executor, build-docker-images]
    runs-on: self-hosted-production
    if: github.event.inputs.environment == 'production'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Prepare secrets (production)
        env:
          FIREBASE_JSON: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_JSON_PRODUCTION }}
          BACKEND_ENV: ${{ secrets.BACKEND_ENV_PRODUCTION }}
        run: |
          echo "Writing Firebase service account and backend env from secrets (production)"
          mkdir -p backend_api_module
          [ -n "$FIREBASE_JSON" ] || { echo "FIREBASE_SERVICE_ACCOUNT_JSON_PRODUCTION secret is empty or missing"; exit 1; }
          [ -n "$BACKEND_ENV" ] || { echo "BACKEND_ENV_PRODUCTION secret is empty or missing"; exit 1; }
          printf "%s" "$FIREBASE_JSON" > backend_api_module/firebase-service-account.json
          printf "%s\n" "$BACKEND_ENV" > backend_api_module/.env.prod

      - name: Deploy application
        env:
          FIREBASE_CREDENTIALS_HOST_PATH: ${{ github.workspace }}/backend_api_module/firebase-service-account.json
          BACKEND_ENV_FILE: ${{ github.workspace }}/backend_api_module/.env.prod
        run: |
          echo "🚀 Deploying to production server..."
          export TAG=${{ needs.build-executor.outputs.version }}-${{ github.event.inputs.environment }}
          export APP_ENV=production
          ./deploy/deploy.sh production "$TAG"
          echo "✅ Production deployment completed"
