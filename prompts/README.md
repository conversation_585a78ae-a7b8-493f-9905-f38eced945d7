# Prompts Directory

This directory contains all the prompt files used by the LLM integration modules.

## Structure

```
prompts/
├── README.md                           # This file
├── trading-assistant-prompt.md         # Main trading assistant system prompt (markdown)
├── trading-assistant-prompt.txt        # Legacy text version (fallback)
└── [future prompt files...]            # Additional prompts as needed
```

## File Format Support

The prompt loader supports both **Markdown (.md)** and **Text (.txt)** files:

- **Markdown files (.md)** are preferred and loaded first
- **Text files (.txt)** are used as fallback if markdown doesn't exist
- This provides backward compatibility while encouraging better formatting

## Usage

### Loading Prompts in Code

```javascript
const { loadPrompt } = require("./prompt-loader");

// Load a prompt (will prefer .md over .txt)
const systemPrompt = await loadPrompt("trading-assistant-prompt");

// Use the prompt in your LLM calls
const messages = [
  {
    role: "system",
    content: systemPrompt,
  },
  // ... other messages
];
```

### Prompt Loader Features

- **Markdown First**: Automatically loads `.md` files if available, falls back to `.txt`
- **Caching**: Prompts are cached in memory after first load for better performance
- **Error Handling**: Proper error handling for missing or corrupted prompt files
- **Async Loading**: All prompt loading is asynchronous
- **Flexible Paths**: Can specify custom prompt directories

### Available Functions

```javascript
const {
  loadPrompt,
  clearPromptCache,
  getCacheStats,
} = require("./prompt-loader");

// Load a prompt (tries .md first, then .txt)
const prompt = await loadPrompt("prompt-name");

// Clear the cache (useful for development)
clearPromptCache();

// Get cache statistics
const stats = getCacheStats();
console.log(stats); // { size: 1, keys: ['../prompts/trading-assistant-prompt'] }
```

## Adding New Prompts

1. Create a new `.md` file in this directory (preferred)
2. Name it descriptively (e.g., `error-handling-prompt.md`)
3. Load it in your code using `loadPrompt('error-handling-prompt')`

## File Naming Convention

- Use kebab-case for file names
- Use `.md` extension for new prompts (preferred)
- Use `.txt` extension for legacy prompts (fallback)
- Make names descriptive and clear
- Avoid spaces and special characters

## Benefits of Markdown Prompts

1. **Better Readability**: Headers, lists, code blocks, and formatting
2. **Syntax Highlighting**: Most editors support markdown syntax highlighting
3. **Version Control**: Git handles markdown diffs better than plain text
4. **Documentation Standard**: Markdown is the de facto standard for documentation
5. **Collaboration**: Non-technical users can edit markdown more easily
6. **Backward Compatibility**: Still supports .txt files for existing prompts

## Benefits of External Prompt Files

1. **Maintainability**: Easier to edit and version control prompts
2. **Reusability**: Same prompts can be used across different modules
3. **Separation of Concerns**: Code and content are separated
4. **Collaboration**: Non-developers can edit prompts without touching code
5. **Testing**: Easier to test different prompt variations
6. **Performance**: Caching reduces file I/O overhead

## Current Prompts

### trading-assistant-prompt.md

The main system prompt for the trading assistant that:

- Defines the trading DSL primitives
- Provides guidelines for intent extraction
- Specifies output format requirements
- Includes examples and validation rules
- Handles missing information scenarios

This prompt is used by both OpenAI and Gemini integrations.

**Features:**

- Proper markdown formatting with headers and sections
- Code blocks for JSON examples
- Better visual structure and readability
- Maintains all original functionality
