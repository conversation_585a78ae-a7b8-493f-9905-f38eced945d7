services:
  # Backend API Service
  backend:
    image: ghcr.io/kotilabs/smart-agent/aagman-backend:${TAG:-latest}
    container_name: aagman-backend
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ${BACKEND_ENV_FILE:-./backend_api_module/.env.prod}:/app/.env:ro
      - ${FIREBASE_CREDENTIALS_HOST_PATH:-./backend_api_module/firebase-service-account.json}:/app/firebase-service-account.json:ro
    networks:
      - aagman-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


networks:
  aagman-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
