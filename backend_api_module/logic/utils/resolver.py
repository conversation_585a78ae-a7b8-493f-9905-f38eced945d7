# resolver.py
"""
Company/Symbol Resolver for trading applications.

This module provides fuzzy matching and symbol resolution capabilities
for mapping user inputs to canonical trading symbols.
"""

import json
import re
import logging
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional, Union
from dataclasses import dataclass
from collections import defaultdict

from pydantic import Field
from rapidfuzz import fuzz, process


@dataclass
class MatchResult:
    """Result of a symbol matching operation."""

    canonical: str
    score: float
    matched_alias: str
    sentence: Optional[str] = None


@dataclass
class RuleStats:
    """Statistics for rule generation."""

    count: int = 0
    best_score: float = -1
    best_sentence: str = ""
    aliases: Set[str] = Field(default_factory=set)


class CompanyResolver:
    """
    Resolves company names and aliases to canonical trading symbols.

    Features:
    - Exact word-boundary matching
    - Fuzzy string matching with configurable threshold
    - Multi-entity detection from text
    - Rule generation for LLM prompts
    - Caching for improved performance
    """

    # Compiled regex patterns for better performance
    TOKEN_PATTERN = re.compile(r"[a-zA-Z0-9&.]+")
    MIN_TOKEN_LENGTH = 3
    EXACT_MATCH_SCORE = 100

    def __init__(self, mapping_file: Union[str, Path], threshold: int = 50):
        """
        Initialize the resolver with a mapping file.

        Args:
            mapping_file: Path to JSON file containing canonical -> aliases mapping
            threshold: Minimum fuzzy matching score (0-100)

        Raises:
            FileNotFoundError: If mapping file doesn't exist
            ValueError: If threshold is not in valid range
            json.JSONDecodeError: If mapping file contains invalid JSON
        """
        self.threshold = self._validate_threshold(threshold)
        self.mapping_file = Path(mapping_file)

        # Load and validate mapping
        self.mapping = self._load_mapping()

        # Build optimized lookup structures
        self.alias_to_canonical = self._build_alias_mapping()
        self.canonical_to_aliases = self._build_reverse_mapping()
        self.all_aliases = list(self.alias_to_canonical.keys())

        # Performance cache
        self._fuzzy_match_cache: Dict[str, Optional[MatchResult]] = {}

        logging.info(
            f"Resolver initialized with {len(self.mapping)} canonical symbols, "
            f"{len(self.alias_to_canonical)} total aliases, threshold={threshold}"
        )

    def _validate_threshold(self, threshold: int) -> int:
        """Validate and return threshold value."""
        if not isinstance(threshold, int) or not (0 <= threshold <= 100):
            raise ValueError(f"Threshold must be an integer between 0 and 100, got: {threshold}")
        return threshold

    def _load_mapping(self) -> Dict[str, List[str]]:
        """Load and validate the symbol mapping file."""
        try:
            with open(self.mapping_file, "r", encoding="utf-8") as f:
                mapping = json.load(f)

            if not isinstance(mapping, dict):
                raise ValueError("Mapping file must contain a JSON object")

            # Validate structure
            for canonical, aliases in mapping.items():
                if not isinstance(canonical, str) or not canonical.strip():
                    raise ValueError(f"Invalid canonical symbol: {canonical}")
                if not isinstance(aliases, list):
                    raise ValueError(f"Aliases must be a list for {canonical}")
                if not all(isinstance(alias, str) and alias.strip() for alias in aliases):
                    raise ValueError(f"All aliases must be non-empty strings for {canonical}")

            return mapping

        except FileNotFoundError:
            raise FileNotFoundError(f"Mapping file not found: {self.mapping_file}")
        except json.JSONDecodeError as e:
            raise e

    def _build_alias_mapping(self) -> Dict[str, str]:
        """Build optimized alias -> canonical mapping."""
        alias_to_canonical = {}

        for canonical, aliases in self.mapping.items():
            # Add canonical as alias to itself (case-insensitive)
            canonical_lower = canonical.lower().strip()
            if canonical_lower:
                alias_to_canonical[canonical_lower] = canonical

            # Add all aliases (case-insensitive)
            for alias in aliases:
                alias_lower = alias.lower().strip()
                if alias_lower and alias_lower not in alias_to_canonical:
                    alias_to_canonical[alias_lower] = canonical
                elif alias_lower in alias_to_canonical and alias_to_canonical[alias_lower] != canonical:
                    logging.warning(
                        f"Alias '{alias}' maps to multiple canonicals: "
                        f"{alias_to_canonical[alias_lower]} and {canonical}"
                    )

        return alias_to_canonical

    def _build_reverse_mapping(self) -> Dict[str, Set[str]]:
        """Build canonical -> aliases reverse mapping."""
        reverse_mapping = defaultdict(set)

        for alias, canonical in self.alias_to_canonical.items():
            reverse_mapping[canonical].add(alias)

        return dict(reverse_mapping)

    def _extract_tokens(self, text: str) -> List[str]:
        """Extract meaningful tokens from text for fuzzy matching."""
        if not text:
            return []

        tokens = self.TOKEN_PATTERN.findall(text.lower())
        return [token for token in tokens if len(token) >= self.MIN_TOKEN_LENGTH]

    def _fuzzy_match(self, token: str) -> Optional[MatchResult]:
        """Find fuzzy matches for a single token."""
        if not token or not fuzz or not process:
            return None

        # Check cache first
        if token in self._fuzzy_match_cache:
            return self._fuzzy_match_cache[token]

        try:
            match, score, _ = process.extractOne(token, self.all_aliases, scorer=fuzz.partial_ratio)

            if score >= self.threshold:
                canonical = self.alias_to_canonical[match]
                result = MatchResult(canonical=canonical, score=score, matched_alias=match)
            else:
                result = None

        except Exception as e:
            logging.warning(f"Fuzzy matching failed for token '{token}': {e}")
            result = None

        # Cache the result
        self._fuzzy_match_cache[token] = result
        return result

    def build_symbol_rules_from_texts(self, texts: List[str], limit: Optional[int] = None) -> str:
        """
        Build symbol rules from multiple text inputs with statistics.

        Args:
            texts: List of input texts to analyze
            limit: Maximum number of rules to return (None for unlimited)

        Returns:
            Newline-separated bullet points of symbol rules with context
        """
        if not texts:
            return ""

        # Collect statistics for each canonical
        stats: Dict[str, RuleStats] = {}

        for sentence in texts:
            if not isinstance(sentence, str) or not sentence.strip():
                continue

            sentence_clean = sentence.strip()
            text_lower = sentence.lower()

            # Process exact matches
            sorted_aliases = sorted(self.alias_to_canonical.items(), key=lambda x: len(x[0]), reverse=True)

            for alias, canonical in sorted_aliases:
                pattern = rf"\b{re.escape(alias)}\b"
                if re.search(pattern, text_lower):
                    if canonical not in stats:
                        stats[canonical] = RuleStats()

                    entry = stats[canonical]
                    entry.count += 1
                    entry.aliases.add(alias)

                    # Update best sentence if this is a better match
                    if self.EXACT_MATCH_SCORE > entry.best_score:
                        entry.best_score = self.EXACT_MATCH_SCORE
                        entry.best_sentence = sentence_clean

            # Process fuzzy matches
            tokens = self._extract_tokens(text_lower)
            for token in tokens:
                # Skip tokens already covered by exact matching
                if any(re.search(rf"\b{re.escape(token)}\b", alias) for alias in self.all_aliases):
                    continue

                fuzzy_result = self._fuzzy_match(token)
                if fuzzy_result:
                    canonical = fuzzy_result.canonical
                    if canonical not in stats:
                        stats[canonical] = RuleStats()

                    entry = stats[canonical]
                    entry.count += 1
                    entry.aliases.add(fuzzy_result.matched_alias)

                    # Update best sentence if this is a better match
                    if fuzzy_result.score > entry.best_score:
                        entry.best_score = fuzzy_result.score
                        entry.best_sentence = sentence_clean

        if not stats:
            return ""

        # Rank by frequency, then by score
        ranked_items = sorted(stats.items(), key=lambda kv: (kv[1].count, kv[1].best_score), reverse=True)

        # Apply limit if specified
        if limit is not None and limit > 0:
            ranked_items = ranked_items[:limit]

        # Generate rule bullets
        bullets = []
        for canonical, rule_stats in ranked_items:
            aliases = sorted(list(rule_stats.aliases))[:3]  # Top 3 aliases

            if aliases:
                alias_text = ", ".join(aliases)
                # context = f' (seen in: "{rule_stats.best_sentence}")' if rule_stats.best_sentence else ""
                bullets.append(f'- If user mentions {alias_text}, use symbol "{canonical}"')
            else:
                bullets.append(f'- Use symbol "{canonical}" when mentioned')

        return "\n".join(bullets)
