import inspect
import json
import os
import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union
from pydantic import SecretStr

from dotenv import load_dotenv
from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
from langchain_google_genai import <PERSON>t<PERSON>oogleGenerativeAI
from langchain_openai import ChatOpenAI

# Note: Using OpenAI's built-in search capabilities instead of external tools
WEB_SEARCH_AVAILABLE = True  # Always available with GPT-4.1

from logic.observability import langfuse
from logic.promptHandler import load_prompt, render_prompt

# Load environment variables
load_dotenv()


def is_web_search_enabled() -> bool:
    """
    Check if web search is enabled via environment variable
    
    Returns:
        True if web search is enabled, False otherwise
    """
    return os.getenv("ENABLE_WEB_SEARCH", "false").lower() == "true"


def detect_market_insight_request(user_input: str, system_prompt: str) -> bool:
    """
    Detect if the user request is likely to need MarketInsight functionality
    
    Args:
        user_input: The user's input message
        system_prompt: The system prompt content
        
    Returns:
        True if MarketInsight is likely needed, False otherwise
    """
    # Keywords that suggest market insight is needed (more specific)
    market_insight_keywords = [
        "market trend", "market update", "market news", "market analysis",
        "stock news", "financial news", "current price", "latest price",
        "what's happening", "market insight", "stock update", "today's market",
        "market today", "current market", "latest news", "market data"
    ]
    
    # Keywords that suggest general market info but not necessarily web search
    general_trading_keywords = [
        "buy", "sell", "order", "limit", "stop loss", "quantity", "shares"
    ]
    
    user_input_lower = user_input.lower()
    
    # First check for specific market insight keywords
    for keyword in market_insight_keywords:
        if keyword in user_input_lower:
            return True
    
    # Check for questions about market data
    if any(word in user_input_lower for word in ["what", "how", "why", "when"]) and \
       any(word in user_input_lower for word in ["market", "price", "stock", "nifty", "sensex"]):
        return True
    
    return False


def create_traced_chat_model(model_name: str, provider: str, additional_config: Optional[Dict[str, Any]] = None, enable_web_search: bool = False) -> Any:
    """
    Create a chat model instance with optional web search capabilities

    Args:
        model_name: Model name to use
        provider: Provider (openai/gemini)
        additional_config: Additional configuration options
        enable_web_search: Whether to enable web search for this model instance

    Returns:
        Configured chat model instance
    """
    if additional_config is None:
        additional_config = {}

    if provider == "gemini":
        # Gemini doesn't support web search in the same way
        return ChatGoogleGenerativeAI(
            google_api_key=os.getenv("GOOGLE_API_KEY"),
            model=model_name,
            temperature=0,
            max_output_tokens=1000,
            convert_system_message_to_human=True,
            **additional_config,
        )
    else:
        openai_api_key = os.getenv("OPENAI_API_KEY")
        
        # Configure model with web search capabilities if enabled
        model_config = {
            "api_key": SecretStr(openai_api_key) if openai_api_key else None,
            "model": model_name,
            "temperature": 0,
            "max_tokens": 1000,
            **additional_config,
        }
        
        # Enable web search for GPT-4 models when requested
        if enable_web_search and is_web_search_enabled():
            print(f"Enabling web search for {model_name}")
            # GPT-4 models with web search capabilities will automatically search when needed
            # No additional configuration required - the model handles this natively
        
        return ChatOpenAI(**model_config)


def validate_json(json_str: str) -> Dict[str, Any]:
    """
    Validate if a string is valid JSON

    Args:
        json_str: String to validate

    Returns:
        Dictionary with isValid, parsed, and error keys
    """
    try:
        parsed = json.loads(json_str)
        return {"isValid": True, "parsed": parsed, "error": None}
    except json.JSONDecodeError as error:
        return {"isValid": False, "parsed": None, "error": str(error)}


def handle_openai_error(error: Exception) -> str:
    """Handle OpenAI API errors and return user-friendly messages"""
    print(f"OpenAI API Error: {error}")

    error_message = str(error)

    # Check for specific error types
    if "rate_limit_exceeded" in error_message:
        return "I'm currently experiencing high traffic. Please try again in a few moments."

    if "quota_exceeded" in error_message:
        return "I've reached my rate limit. Please wait a moment before trying again."

    if "invalid_request_error" in error_message:
        return "I couldn't process your request. Please try rephrasing your message."

    if "authentication_error" in error_message or "invalid_api_key" in error_message:
        return "Authentication error. Please check your API configuration."

    if "timeout" in error_message:
        return "Request timed out. Please try again."

    if "network" in error_message:
        return "Network error. Please check your internet connection and try again."

    # Default error message
    return "I'm having trouble processing your request right now. Please try again later."


async def generate_response_v2(user_input: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    Generate response from OpenAI API using LangChain

    Args:
        user_input: The user's input
        options: Configuration options

    Returns:
        Dictionary containing response and metadata
    """
    if options is None:
        options = {}

    start_time = time.time() * 1000  # Convert to milliseconds
    model_name = options.get("model", "gpt-4o-mini")
    chat_history = options.get("chatHistory", [])
    user_id = options.get("userId", "unknown")
    session_id = options.get("sessionId", f"session_{int(time.time() * 1000)}")
    provider = options.get("provider", "openai")
    firebase_uid = options.get("firebase_uid", "unknown")

    # Initialize Langfuse tracing (v3 syntax)
    # Note: user_id is backend's user_id, session_id is backend's conversation_id
    stack = inspect.stack()
    with langfuse.start_as_current_span(
        name=stack[0].function if len(stack) > 0 else "generate_response_v2",
        metadata={
            "user_id": firebase_uid,
            "session_id": session_id,
            "prompt": "TradingPrompt.md",
        },
    ) as span:
        try:
            print(f'Generating response with model: {model_name}, provider: {provider}')
            
            # Load the system prompt
            system_prompt = await load_prompt("TradingPrompt")
            
            # Detect if this request might need MarketInsight (web search)
            needs_market_insight = detect_market_insight_request(user_input, system_prompt)
            print(f"MarketInsight detection: {needs_market_insight}")
            
            # Create a new chat model instance with conditional web search support
            chat_model_for_request = create_traced_chat_model(
                model_name, 
                provider, 
                enable_web_search=needs_market_insight and provider == "openai"  # Only enable for OpenAI
            )

            # Build conversation history for context using LangChain message types
            messages: List[Union[SystemMessage, HumanMessage, AIMessage]] = [SystemMessage(content=system_prompt)]

            if len(chat_history) > 0:
                # Add recent conversation context
                print(f"Chat history length: {len(chat_history)}")
                for index, msg in enumerate(chat_history):
                    print(
                        f'Processing message {index}: user_message="{msg.get("user_message")}", llm_response type={type(msg.get("llm_response"))}'
                    )

                    user_message = msg.get("user_message").get("message", "")
                    llm_response = msg.get("llm_response").get("message", "")

                    if (
                        not user_message
                        or not isinstance(user_message, str)
                        or (
                            isinstance(llm_response, dict)
                            and llm_response.get("primitives", [{}])[0].get("action") == "llmChat"
                        )
                    ):
                        continue

                    messages.append(HumanMessage(content=user_message))

                    # Only add assistant message if llm_response exists
                    if llm_response:
                        assistant_content = llm_response if isinstance(llm_response, str) else json.dumps(llm_response)
                        print(f"Adding assistant message {index}: content type={type(assistant_content)}")
                        messages.append(AIMessage(content=assistant_content))
                    else:
                        print(f"Skipping assistant message {index}: llm_response is null/undefined")

            # Add the current user message
            messages.append(HumanMessage(content=user_input))

            # Validate all messages have content
            messages = [msg for msg in messages if msg.content and isinstance(msg.content, str)]
            print(f"Final messages array length: {len(messages)}")

            # Update span input with actual messages and additional context
            span.update(
                input={"messages": [msg.content for msg in messages]},
                metadata={
                    "chat_history_length": len(chat_history),
                    "final_messages_count": len(messages),
                    "system_prompt_length": len(system_prompt),
                },
            )

            # Call the LLM (OpenAI / Gemini) and capture observability data
            with span.start_as_current_span(
                name="core_llm_api_call",
                metadata={"provider": provider, "model_name": model_name, "web_search_enabled": needs_market_insight},
            ) as llm_span:
                # GPT-4 models with web search enabled will automatically search when needed
                # No manual tool calling required - the model handles this natively
                response = await chat_model_for_request.ainvoke(messages)
                
                assistant_msg = response.content
                end_time = time.time() * 1000
                latency = end_time - start_time

                # Extract usage information from response metadata
                usage = response.response_metadata.get("usage", {}) if hasattr(response, "response_metadata") else {}
                input_tokens = usage.get("prompt_tokens", 0)
                output_tokens = usage.get("completion_tokens", 0)
                total_tokens = usage.get("total_tokens", 0)

                # Update span outputs & metadata so they show up in Langfuse UI
                llm_span.update(
                    output=assistant_msg,
                    metadata={
                        "latency_ms": latency,
                        "input_tokens": input_tokens,
                        "output_tokens": output_tokens,
                        "total_tokens": total_tokens,
                        "web_search_detected": needs_market_insight,
                    },
                )

            print(f"Response generated successfully. Latency: {latency}ms, Tokens: {total_tokens}")

            # Clean the response
            import re

            cleaned_response = re.sub(r"```json\s*|\s*```", "", assistant_msg).strip()

            # Validate if the cleaned response is valid JSON
            json_validation = validate_json(cleaned_response)

            # Update span with final output and validation
            span.update(
                output=cleaned_response,
                metadata={"json_validation": json_validation["isValid"]},
            )

            # Log JSON parse error if invalid
            if not json_validation["isValid"]:
                print(f'JSON parse error: {json_validation["error"]}')
                print(f"Response content: {cleaned_response[:500]}...")

            return {
                "response": cleaned_response,
                "isValidJson": json_validation["isValid"],
                "parsedJson": json_validation["parsed"],
                "metadata": {
                    "latency_ms": latency,
                    "generation_config": {"model": model_name, "temperature": 0},
                    "model_name": model_name,
                    "input_tokens": input_tokens,
                    "output_tokens": output_tokens,
                    "total_tokens": total_tokens,
                    "json_validation": json_validation["isValid"],
                    "session_id": session_id,
                    "user_id": user_id,
                },
            }

        except Exception as error:
            # Record the error in Langfuse and re-raise
            span.update(status_message=str(error))
            print(f"Error generating LangChain OpenAI V2 response: {error}")

            user_friendly_message = handle_openai_error(error)
            raise Exception(user_friendly_message)


async def log_custom_event(event_name: str, event_data: Dict[str, Any], run_id: Optional[str] = None) -> None:
    """
    Log custom event - placeholder for your implementation

    Args:
        event_name: Name of the event
        event_data: Event data to log
        run_id: Optional run ID to associate with
    """
    # TODO: Implement your custom event logging logic here
    print(f"Custom event logged: {event_name} - {event_data}")


# Export the functions
__all__ = [
    "generate_response_v2",
    "log_custom_event",
    "create_traced_chat_model",
    "validate_json",
    "load_prompt",
]
