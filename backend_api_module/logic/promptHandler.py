import os
import re
from pathlib import Path
from typing import Any, Dict, Optional

# Cache for loaded prompts
prompt_cache: Dict[str, str] = {}


async def load_prompt(prompt_name: str, prompts_dir: str = "./prompts") -> str:
    """
    Load a prompt from a file

    Args:
        prompt_name: Name of the prompt file (without extension)
        prompts_dir: Directory containing prompt files (default: '../prompts')

    Returns:
        str: The loaded prompt content
    """
    cache_key = f"{prompts_dir}/{prompt_name}"

    # Check cache first
    if cache_key in prompt_cache:
        return prompt_cache[cache_key]

    try:
        # Try markdown file first, then fallback to txt
        current_dir = Path(__file__).parent
        prompt_path = current_dir / prompts_dir / f"{prompt_name}.md"
        print(f"Attempting to load markdown prompt: {prompt_path}")
        try:
            with open(prompt_path, "r", encoding="utf-8") as f:
                prompt_content = f.read()
            print(f"Loaded markdown prompt: {prompt_name}.md")
        except FileNotFoundError:
            # If markdown file doesn't exist, try txt file
            prompt_path = current_dir / prompts_dir / f"{prompt_name}.txt"
            with open(prompt_path, "r", encoding="utf-8") as f:
                prompt_content = f.read()
            print(f"Loaded text prompt: {prompt_name}.txt")

        # Cache the loaded prompt
        prompt_cache[cache_key] = prompt_content
        return prompt_content

    except Exception as error:
        print(f"Error loading prompt {prompt_name}: {error}")
        raise Exception(
            f"Failed to load prompt: {prompt_name} (tried both .md and .txt files)"
        )


def clear_prompt_cache() -> None:
    """Clear the prompt cache"""
    prompt_cache.clear()
    print("Prompt cache cleared")


def get_cache_stats() -> Dict[str, Any]:
    """
    Get cache statistics

    Returns:
        Dict: Cache statistics
    """
    return {"size": len(prompt_cache), "keys": list(prompt_cache.keys())}


def render_prompt(prompt: str, variables: Dict[str, str]) -> str:
    """
    Render a prompt with variables using {{var}} syntax

    Args:
        prompt: The prompt template
        variables: Key-value pairs for interpolation

    Returns:
        str: The rendered prompt
    """

    def replace_var(match: re.Match[str]) -> str:
        key = match.group(1)
        return variables.get(key, match.group(0))

    return re.sub(r"{{(\w+)}}", replace_var, prompt)
