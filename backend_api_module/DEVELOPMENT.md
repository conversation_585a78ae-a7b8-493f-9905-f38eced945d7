# Development Setup Guide

This guide explains how to set up and use the code formatters and linters for the backend API module.

## Prerequisites

- Python 3.9+
- Virtual environment (`.venv`)

## Installation

1. **Activate your virtual environment:**
   ```bash
   source .venv/bin/activate  # On macOS/Linux
   # or
   .venv\Scripts\activate     # On Windows
   ```

2. **Install development dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install pre-commit hooks (optional but recommended):**
   ```bash
   pre-commit install
   ```

## Available Commands

### Using Makefile (Recommended)

```bash
# Show all available commands
make help

# Format code with black and isort
make format

# Run flake8 linting
make lint

# Run mypy type checking
make type-check

# Run all checks (format, lint, type-check)
make check

# Run tests
make test

# Clean up cache files
make clean

# Run pre-commit on all files
make pre-commit
```

### Manual Commands

```bash
# Format code
black src/ tests/
isort src/ tests/

# Lint code
flake8 src/ tests/

# Type check
mypy src/

# Run tests
pytest tests/ -v
```

## Tools Configuration

### Black (Code Formatter)
- **Line length:** 88 characters
- **Target Python version:** 3.9+
- **Configuration:** `pyproject.toml`

### isort (Import Sorter)
- **Profile:** Black-compatible
- **Line length:** 88 characters
- **Configuration:** `pyproject.toml`

### Flake8 (Linter)
- **Line length:** 88 characters
- **Ignores:** E203, W503 (Black-compatible)
- **Configuration:** `.flake8` and `pyproject.toml`

### MyPy (Type Checker)
- **Python version:** 3.9
- **Strict mode:** Enabled
- **Configuration:** `pyproject.toml`

## Pre-commit Hooks

Pre-commit hooks automatically run formatters and linters before each commit:

1. **Install hooks:**
   ```bash
   make install-pre-commit
   ```

2. **Run manually:**
   ```bash
   make pre-commit
   ```

## VS Code Integration

The `.vscode/settings.json` file configures VS Code to:
- Use Black as the default formatter
- Enable format on save
- Enable organize imports on save
- Configure linting with Flake8 and MyPy
- Set up pytest integration

## Troubleshooting

### Common Issues

1. **Import errors in MyPy:**
   - Check that all dependencies are installed
   - MyPy ignores missing imports for external packages

2. **Formatting conflicts:**
   - Ensure Black and isort are both installed
   - Run `make format` to resolve conflicts

3. **Pre-commit fails:**
   - Run `pre-commit run --all-files` to see detailed errors
   - Fix issues and try again

### Resetting Configuration

```bash
# Remove all cache files
make clean

# Reinstall pre-commit hooks
make install-pre-commit
```

## Best Practices

1. **Always run `make check` before committing**
2. **Use pre-commit hooks for automatic checks**
3. **Keep line length at 88 characters (Black standard)**
4. **Organize imports with isort**
5. **Add type hints for better MyPy coverage**
