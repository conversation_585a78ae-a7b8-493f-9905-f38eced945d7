{"python.defaultInterpreterPath": "./.venv/bin/python", "python.sortImports.args": ["--profile", "black"], "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.linting.flake8Args": ["--max-line-length=88"], "python.linting.mypyArgs": ["--ignore-missing-imports"], "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests/"], "[python]": {"editor.defaultFormatter": "ms-python.black-formatter", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, "**/.mypy_cache": true, "**/.pytest_cache": true, "**/*.egg-info": true}}