# Backend Observability & Tracing (Langfuse v3)

This document explains **how tracing is implemented inside `backend_api_module`**, how you can extend / customise it, and how to verify that traces look correct in the Langfuse dashboard.

> NOTE – The codebase uses **Langfuse Python SDK v3 (OpenTelemetry-based)**. Refer to <https://langfuse.com/docs/sdk/python> for the full API reference.

---
## 1. Architecture Overview

```
┌───────────────────────┐
│   FastAPI Endpoints   │
└────────────┬──────────┘        (HTTP / WS)
             │
             ▼
┌──────────────────────────────────────────┐
│ WebSocketChatService.handle_message      │  ← top-level span per client msg
│   • dynamic span name: websocket_handle_<type>  │
│   • enriches input / metadata            │
│   • updates trace-level user & session   │
│   ├─ _process_message (child span)       │
│   │    ├─ _call_llm_function (child)     │
│   │    │    └─ render_chat(..)           │
│   │    │         └─ process_chat_message │
│   │    │              └─ generate_response_v2│
│   │    │                    (LLM call)   │
│   │    └─ storage helper spans (todo)    │
│   └─ create_event("primitive_requested")│
└──────────────────────────────────────────┘
```

*All other modules (promptRunner, ask<PERSON><PERSON>, etc.) follow the same pattern – they create child spans starting from the current context.*

---
## 2. Entry Point – `logic/observability.py`

```python
from dotenv import load_dotenv
from langfuse import Langfuse

load_dotenv()
langfuse = Langfuse(
    public_key=os.getenv("LANGFUSE_PUBLIC_KEY"),
    secret_key=os.getenv("LANGFUSE_SECRET_KEY"),
    host=os.getenv("LANGFUSE_HOST", "https://cloud.langfuse.com"),
)
```

* `get_client()` is **not** required because we keep a single export.
* The client is automatically a singleton – all spans share context.

---
## 3. How We Use the SDK

### 3.1 Creating spans / generations

```python
with langfuse.start_as_current_span(name="websocket_handle_chat", input={...}) as span:
    ...  # nested work

with langfuse.start_as_current_generation(name="llm-call", model="gpt-4o") as gen:
    ...
```

*Context managers* ensure `end()` is called and child spans nest automatically.

### 3.2 Updating data

```python
span.update(input=dict(...))          # add / modify inputs
span.update(output=dict(...))         # outputs / return value
span.update(metadata={...})           # arbitrary structured metadata
span.update(level="ERROR", status_message="boom!")
```

### 3.3 Events

`span.create_event(name="primitive_requested", metadata={...})` – recorded for each trading primitive so we can build custom dashboards (count, latency, etc.).

### 3.4 Trace-level attributes

Use **once per trace** (usually in `handle_message`) so every descendant inherits:

```python
# Backend variable names, semantic Langfuse keys (best of both worlds!)
firebase_uid = connection_meta.get('firebase_uid', 'unknown')  # backend's firebase_uid
span.update_trace(user_id=firebase_uid, session_id=request.conversation_id)
```

**Pattern: Backend Variable Names → Semantic Langfuse Keys:**
- `firebase_uid` variable → `user_id` key (real user identity)
- `conversation_id` variable → `session_id` key (conversation thread identifier)
- Backend's `user_id` is used only where needed for database operations


**ID Availability & Fallbacks:**
- `firebase_uid` will be "unknown" if WebSocket connection lacks Firebase authentication
- `conversation_id` is always available (generated if not provided)
- Firebase authentication is validated at the start of each message processing with warning logs
- The system will still function with "unknown" firebase_uid but Langfuse user tracking will be limited

---
## 4. Environment Variables

| Variable | Purpose |
|----------|---------|
| `LANGFUSE_PUBLIC_KEY` | project public key |
| `LANGFUSE_SECRET_KEY` | project secret key |
| `LANGFUSE_HOST`       | host (defaults to cloud) |
| `OTEL_SERVICE_NAME`   | sets service.name in resource attrs – *set to* `backend_api` |

All keys are stored in `.env` for local development – see `backend_api_module/env.example`.

---
## 5. Viewing Traces

1. Run backend, generate a chat message via WebSocket.
2. Open Langfuse → **Traces**. Filter `service.name == backend_api`.
3. You should see spans named e.g. `websocket_handle_chat`, `websocket_handle_orders`, etc.
4. Inside each trace:
   * Input tab shows `message`, `recent_messages_len`.
   * Output tab shows **entire** `WebSocketChatResponse` JSON.
   * Events tab lists `primitive_requested` occurrences.

If something is missing, confirm that:
* Environment keys are loaded.
* No-op client isn’t active (`LANGFUSE_PUBLIC_KEY` unset → noop).

---
## 6. Extending / Customising

* **Add DB-storage spans** – wrap `_store_message`, `_store_system_response` and other I/O heavy helpers with `start_as_current_span` to get latency charts.
* **Token & cost tracking** – forward `usage_details` from LLM responses to parent spans (`span.update(metadata={"usage": {...}})`).
* **Sampling** – `LANGFUSE_SAMPLE_RATE=0.2` to sample 20 % of traces in prod.

---
## 7. Shutdown & Flushing

SDK batches events; in FastAPI add:

```python
from contextlib import asynccontextmanager
from logic.observability import langfuse

@asynccontextmanager
def lifespan(app):
    yield
    langfuse.flush()
```

This guarantees no data loss on pod shutdown.

---
## 8. Changelog of Observability Refactor

See PR-summary inside project description or consult commit history – includes:
* migration from v2 → v3
* dynamic span names
* proper events API
* error level updates
* summary normalisation
* removal of redundant metadata keys

---
## 9. FAQ

**Q: I can’t see any traces.** Check that public & secret keys are present and not the placeholder. SDK falls back to noop if missing.

**Q: Why is `service.name` "unknown_service"?** Set `OTEL_SERVICE_NAME=backend_api` before launch, or pass `resource_attributes` when initialising Langfuse.

**Q: How do I link front-end traces?** Use the same `session_id` (conversation_id) when instrumenting the React app – Langfuse will stitch them together automatically.

---
© KotiLabs 2025
