"""Enhanced performance tests for the backend API using real CouchDB data."""

import asyncio
import threading
import time
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime

import psutil
import pytest
from fastapi.testclient import TestClient

# Performance test parameters
STRESS_TEST_USERS = 50
STRESS_TEST_REQUESTS_PER_USER = 10
LARGE_DATASET_SIZE = 1000
MEMORY_TEST_DURATION = 30  # seconds

# NOTE: FastAPI's TestClient is not fully thread-safe for high concurrency or stress tests.
# For true load testing, use an external tool (e.g., locust, k6) against a running server.
# These tests use low concurrency to avoid deadlocks/hangs with TestClient.


class TestEnhancedPerformance:
    """Enhanced performance tests for network conditions, load testing, and resource monitoring."""

    def test_network_condition_simulation(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test API performance under various network conditions simulation."""
        # Create test data for network condition testing
        test_orders = []
        for i in range(20):
            order = {
                "_id": f"order_network_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i % 5}",
                "quantity": 50 + (i * 10),
                "price": 1000.0 + (i * 50),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "network_test": True},
            }
            real_couchdb_connection.save(order)
            test_orders.append(order)

        # Simulate different network conditions
        network_conditions = [
            {"name": "normal", "latency": 0, "timeout": 30},
            {"name": "high_latency", "latency": 2, "timeout": 60},
            {"name": "very_high_latency", "latency": 5, "timeout": 120},
            {"name": "low_bandwidth", "latency": 1, "timeout": 45},
        ]

        results = {}

        for condition in network_conditions:
            print(f"\nTesting network condition: {condition['name']}")

            # Simulate network latency
            if condition["latency"] > 0:
                time.sleep(condition["latency"])

            # Make requests with different timeouts
            start_time = time.time()
            response_times = []
            successful_requests = 0
            failed_requests = 0

            # Make 10 requests for each condition
            for i in range(10):
                request_start = time.time()
                try:
                    response = test_client.post(
                        "/api/v1/orders",
                        json={"user_id": test_user_id, "broker": "zerodha"},
                        timeout=condition["timeout"],
                    )

                    request_time = time.time() - request_start
                    response_times.append(request_time)

                    if response.status_code == 200:
                        successful_requests += 1
                    else:
                        failed_requests += 1

                except Exception:
                    failed_requests += 1
                    response_times.append(condition["timeout"])  # Max timeout

            total_time = time.time() - start_time

            results[condition["name"]] = {
                "total_requests": 10,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate": successful_requests / 10 * 100,
                "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "total_time": total_time,
            }

            print(f"  Success Rate: {results[condition['name']]['success_rate']:.1f}%")
            print(f"  Avg Response Time: {results[condition['name']]['avg_response_time']:.2f}s")

        # Assertions for network condition testing
        for condition_name, result in results.items():
            assert (
                result["success_rate"] >= 80
            ), f"Success rate for {condition_name} below 80%: {result['success_rate']}%"
            assert (
                result["avg_response_time"] < 30
            ), f"Average response time for {condition_name} too high: {result['avg_response_time']}s"

        # Cleanup
        for order in test_orders:
            try:
                real_couchdb_connection.delete(order)
            except Exception:
                pass

    def test_load_testing_stress_test(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Conduct load testing to determine system behavior under heavy load (reduced concurrency for TestClient)."""
        # Create large dataset for load testing
        print("Creating large dataset for load testing...")
        load_test_data = []

        # Create 100 orders for load testing
        for i in range(100):
            order = {
                "_id": f"order_load_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i % 10}",
                "quantity": 50 + (i * 5),
                "price": 1000.0 + (i * 25),
                "status": "executed" if i % 3 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "load_test": True, "index": i},
            }
            real_couchdb_connection.save(order)
            load_test_data.append(order)

        # Define load test scenarios
        load_scenarios = [
            {"name": "low_load", "concurrent_users": 2, "requests_per_user": 3},
            {"name": "medium_load", "concurrent_users": 3, "requests_per_user": 4},
            {"name": "high_load", "concurrent_users": 4, "requests_per_user": 5},
            {"name": "stress_load", "concurrent_users": 5, "requests_per_user": 6},
        ]

        load_results = {}

        for scenario in load_scenarios:
            print(f"\nTesting load scenario: {scenario['name']}")
            print(f"  Concurrent users: {scenario['concurrent_users']}")
            print(f"  Requests per user: {scenario['requests_per_user']}")

            def make_load_requests(user_id: str, request_count: int):
                """Make multiple requests for load testing."""
                user_results = []
                for i in range(request_count):
                    start_time = time.time()
                    try:
                        response = test_client.post(
                            "/api/v1/orders",
                            json={"user_id": user_id, "broker": "zerodha"},
                            timeout=30,
                        )

                        response_time = time.time() - start_time
                        user_results.append(
                            {
                                "status_code": response.status_code,
                                "response_time": response_time,
                                "success": response.status_code == 200,
                            }
                        )
                    except Exception as e:
                        user_results.append(
                            {
                                "status_code": 0,
                                "response_time": 30,  # Timeout
                                "success": False,
                                "error": str(e),
                            }
                        )
                return user_results

            # Execute load test
            start_time = time.time()
            all_results = []

            with ThreadPoolExecutor(max_workers=scenario["concurrent_users"]) as executor:
                futures = []
                for i in range(scenario["concurrent_users"]):
                    user_id = f"{test_user_id}_load_{i}"
                    future = executor.submit(make_load_requests, user_id, scenario["requests_per_user"])
                    futures.append(future)

                # Collect results
                for future in as_completed(futures):
                    try:
                        user_results = future.result()
                        all_results.extend(user_results)
                    except Exception as e:
                        print(f"Error in load test: {e}")

            total_time = time.time() - start_time
            total_requests = len(all_results)
            successful_requests = sum(1 for r in all_results if r["success"])
            failed_requests = total_requests - successful_requests
            response_times = [r["response_time"] for r in all_results]

            load_results[scenario["name"]] = {
                "total_requests": total_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "success_rate": (successful_requests / total_requests * 100) if total_requests > 0 else 0,
                "avg_response_time": sum(response_times) / len(response_times) if response_times else 0,
                "max_response_time": max(response_times) if response_times else 0,
                "min_response_time": min(response_times) if response_times else 0,
                "total_time": total_time,
                "requests_per_second": total_requests / total_time if total_time > 0 else 0,
            }

            print(f"  Success Rate: {load_results[scenario['name']]['success_rate']:.1f}%")
            print(f"  Requests/sec: {load_results[scenario['name']]['requests_per_second']:.1f}")
            print(f"  Avg Response Time: {load_results[scenario['name']]['avg_response_time']:.2f}s")

        # Assertions for load testing
        for scenario_name, result in load_results.items():
            if scenario_name in ["low_load", "medium_load"]:
                assert (
                    result["success_rate"] >= 95
                ), f"Success rate for {scenario_name} below 95%: {result['success_rate']}%"
            elif scenario_name == "high_load":
                assert (
                    result["success_rate"] >= 90
                ), f"Success rate for {scenario_name} below 90%: {result['success_rate']}%"
            else:  # stress_load
                assert (
                    result["success_rate"] >= 80
                ), f"Success rate for {scenario_name} below 80%: {result['success_rate']}%"

            assert (
                result["avg_response_time"] < 10
            ), f"Average response time for {scenario_name} too high: {result['avg_response_time']}s"

        # Cleanup
        for order in load_test_data:
            try:
                real_couchdb_connection.delete(order)
            except Exception:
                pass

    @pytest.mark.skip(reason="Test takes too long - needs optimization")
    def test_resource_utilization_monitoring(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Monitor CPU and memory usage during peak loads (reduced concurrency for TestClient)."""
        # Get initial resource usage
        initial_cpu_percent = psutil.cpu_percent(interval=1)
        initial_memory = psutil.virtual_memory()

        print(f"Initial CPU usage: {initial_cpu_percent}%")
        print(f"Initial memory usage: {initial_memory.percent}%")

        # Create test data for resource monitoring
        resource_test_data = []
        for i in range(50):
            order = {
                "_id": f"order_resource_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i % 8}",
                "quantity": 50 + (i * 8),
                "price": 1000.0 + (i * 40),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "resource_test": True},
            }
            real_couchdb_connection.save(order)
            resource_test_data.append(order)

        # Start resource monitoring in background
        resource_usage = []
        monitoring_active = True

        def monitor_resources():
            """Monitor CPU and memory usage."""
            while monitoring_active:
                cpu_percent = psutil.cpu_percent(interval=0.5)
                memory = psutil.virtual_memory()
                resource_usage.append(
                    {
                        "timestamp": time.time(),
                        "cpu_percent": cpu_percent,
                        "memory_percent": memory.percent,
                        "memory_available": memory.available,
                    }
                )

        # Start monitoring thread
        monitor_thread = threading.Thread(target=monitor_resources)
        monitor_thread.start()

        # Perform intensive operations
        print("Starting intensive operations for resource monitoring...")
        start_time = time.time()

        def intensive_operation(operation_id: int):
            """Perform intensive database operations."""
            results = []
            for i in range(20):
                try:
                    # Multiple API calls
                    response1 = test_client.post(
                        "/api/v1/orders",
                        json={"user_id": test_user_id, "broker": "zerodha"},
                    )

                    response2 = test_client.post(
                        "/api/v1/monitoring/instances",
                        json={"user_id": test_user_id, "broker": "zerodha"},
                    )

                    response3 = test_client.post(
                        "/api/v1/chatHistory",
                        json={
                            "user_id": test_user_id,
                            "conversation_id": f"conv_resource_{operation_id}_{i}",
                            "type": "chat",
                            "brokerName": "zerodha",
                        },
                    )

                    results.append(
                        {
                            "operation_id": operation_id,
                            "request_id": i,
                            "orders_status": response1.status_code,
                            "monitoring_status": response2.status_code,
                            "chat_status": response3.status_code,
                        }
                    )
                except Exception as e:
                    results.append({"operation_id": operation_id, "request_id": i, "error": str(e)})
            return results

        # Execute intensive operations
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(intensive_operation, i) for i in range(3)]
            operation_results = []
            for future in as_completed(futures):
                try:
                    result = future.result()
                    operation_results.extend(result)
                except Exception as e:
                    print(f"Error in intensive operation: {e}")

        total_time = time.time() - start_time

        # Stop monitoring
        monitoring_active = False
        monitor_thread.join()

        # Analyze resource usage
        if resource_usage:
            max_cpu = max(r["cpu_percent"] for r in resource_usage)
            max_memory = max(r["memory_percent"] for r in resource_usage)
            avg_cpu = sum(r["cpu_percent"] for r in resource_usage) / len(resource_usage)
            avg_memory = sum(r["memory_percent"] for r in resource_usage) / len(resource_usage)

            print("\nResource Usage Summary:")
            print(f"  Max CPU: {max_cpu:.1f}%")
            print(f"  Max Memory: {max_memory:.1f}%")
            print(f"  Avg CPU: {avg_cpu:.1f}%")
            print(f"  Avg Memory: {avg_memory:.1f}%")
            print(f"  Total Operations: {len(operation_results)}")
            print(f"  Total Time: {total_time:.2f}s")

            # Assertions for resource utilization
            assert max_cpu < 90, f"CPU usage too high during peak load: {max_cpu}%"
            assert max_memory < 95, f"Memory usage too high during peak load: {max_memory}%"
            assert avg_cpu < 70, f"Average CPU usage too high: {avg_cpu}%"
            assert avg_memory < 85, f"Average memory usage too high: {avg_memory}%"

        # Cleanup
        for order in resource_test_data:
            try:
                real_couchdb_connection.delete(order)
            except Exception:
                pass

    @pytest.mark.asyncio
    async def test_long_running_sessions(self, test_client: TestClient, real_couchdb_connection, test_user_id):
        """Test the performance of long-running sessions for stability."""
        # Create test data for long-running sessions
        session_data = []
        for i in range(30):
            order = {
                "_id": f"order_session_{uuid.uuid4().hex[:8]}_{i}",
                "user_id": test_user_id,
                "broker": "zerodha",
                "symbol": f"STOCK_{i % 6}",
                "quantity": 50 + (i * 6),
                "price": 1000.0 + (i * 30),
                "status": "executed" if i % 2 == 0 else "pending",
                "timestamp": datetime.now().isoformat(),
                "order_type": "buy",
                "meta_json": {"test": True, "session_test": True},
            }
            real_couchdb_connection.save(order)
            session_data.append(order)

        # Simulate long-running session with periodic operations
        session_duration = 60  # 60 seconds
        operation_interval = 5  # Operations every 5 seconds
        start_time = time.time()

        session_results = []
        successful_operations = 0
        failed_operations = 0

        print(f"Starting long-running session test for {session_duration} seconds...")

        while time.time() - start_time < session_duration:
            try:
                # Perform periodic operations
                response1 = test_client.post(
                    "/api/v1/orders",
                    json={"user_id": test_user_id, "broker": "zerodha"},
                )

                response2 = test_client.post(
                    "/api/v1/monitoring/instances",
                    json={"user_id": test_user_id, "broker": "zerodha"},
                )

                response3 = test_client.post(
                    "/api/v1/chatHistory",
                    json={
                        "user_id": test_user_id,
                        "conversation_id": f"conv_session_{int(time.time())}",
                        "type": "chat",
                        "brokerName": "zerodha",
                    },
                )

                session_results.append(
                    {
                        "timestamp": time.time() - start_time,
                        "orders_status": response1.status_code,
                        "monitoring_status": response2.status_code,
                        "chat_status": response3.status_code,
                        "success": all(r.status_code == 200 for r in [response1, response2, response3]),
                    }
                )

                if session_results[-1]["success"]:
                    successful_operations += 1
                else:
                    failed_operations += 1

                print(
                    f"  Session time: {session_results[-1]['timestamp']:.1f}s - Success: {session_results[-1]['success']}"
                )

                # Wait for next operation
                await asyncio.sleep(operation_interval)

            except Exception as e:
                failed_operations += 1
                session_results.append(
                    {
                        "timestamp": time.time() - start_time,
                        "error": str(e),
                        "success": False,
                    }
                )
                print(f"  Session time: {session_results[-1]['timestamp']:.1f}s - Error: {e}")
                await asyncio.sleep(operation_interval)

        total_operations = len(session_results)
        success_rate = (successful_operations / total_operations * 100) if total_operations > 0 else 0

        print("\nLong-running Session Results:")
        print(f"  Total Operations: {total_operations}")
        print(f"  Successful Operations: {successful_operations}")
        print(f"  Failed Operations: {failed_operations}")
        print(f"  Success Rate: {success_rate:.1f}%")
        print(f"  Session Duration: {time.time() - start_time:.1f}s")

        # Assertions for long-running sessions
        assert success_rate >= 90, f"Success rate for long-running session below 90%: {success_rate}%"
        assert total_operations >= 10, f"Insufficient operations for long-running session: {total_operations}"

        # Check for degradation over time
        if len(session_results) >= 6:
            first_half = session_results[: len(session_results) // 2]
            second_half = session_results[len(session_results) // 2 :]

            first_half_success = sum(1 for r in first_half if r["success"])
            second_half_success = sum(1 for r in second_half if r["success"])

            first_half_rate = first_half_success / len(first_half) * 100
            second_half_rate = second_half_success / len(second_half) * 100

            # Ensure no significant degradation
            assert (
                second_half_rate >= first_half_rate * 0.9
            ), f"Significant performance degradation detected: {first_half_rate}% -> {second_half_rate}%"

        # Cleanup
        for order in session_data:
            try:
                real_couchdb_connection.delete(order)
            except Exception:
                pass
