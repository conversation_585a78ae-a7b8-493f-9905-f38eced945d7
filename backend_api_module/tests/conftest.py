"""Pytest configuration and fixtures for backend API testing with real CouchDB."""

import asyncio
import os
import sys
import uuid
from datetime import datetime

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from src.datastore import get_user_database_connection
from src.main import app
from src.models.schemas import (
    BrokerName,
    ConversationType,
    MonitoringInstancesRequest,
    NewChatHistoryRequest,
    OrdersRequest,
    WebSocketChatRequest,
    WebSocketMessageType,
)
from src.services.websocket_service import WebSocketChatService

# Enable testing mode for mock LLM responses
os.environ["MOCK_LLM_RESPONSES"] = "true"
os.environ["MOCK_LLM_RESPONSE_TYPE"] = "orders"  # or 'monitoring'

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))


sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_app() -> FastAPI:
    """Create a test FastAPI application."""
    return app


@pytest.fixture
def test_client(test_app: FastAPI) -> TestClient:
    """Create a test client for the FastAPI application."""
    return TestClient(test_app)


@pytest.fixture
def real_couchdb_connection():
    """Get real CouchDB connection for testing using the new per-user database approach."""
    # Use a test phone number for testing
    test_phone_number = "+1234567890"

    try:
        # Get connection to test user's database
        db = get_user_database_connection(test_phone_number)
        yield db
    except Exception as e:
        pytest.fail(f"Failed to connect to test CouchDB database: {e}")
    # No need to disconnect - couchdb3 handles connection cleanup automatically


@pytest.fixture
def test_user_id():
    """Generate a unique test user ID."""
    return f"test-user-{uuid.uuid4().hex[:8]}"


@pytest.fixture
def test_conversation_id():
    """Generate a unique test conversation ID."""
    return f"test-conversation-{uuid.uuid4().hex[:8]}"


@pytest.fixture
def real_chat_history_data(real_couchdb_connection, test_user_id, test_conversation_id):
    """Create real chat history data in CouchDB."""
    # Create test chat messages
    chat_messages = [
        {
            "_id": f"chat_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user",
            "message": "Hello, I want to place an order",
            "llm_model_version": "gpt-4",
            "meta_json": {
                "broker_name": "zerodha",
                "message_type": "chat",
                "actions": [
                    {
                        "description": "Place Order",
                        "type": "orders",
                        "message": "place_order",
                    }
                ],
            },
            "order_id": None,
            "message_type": "chat",
        },
        {
            "_id": f"chat_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "system",
            "message": "I'll help you place an order. What would you like to buy?",
            "llm_model_version": None,
            "meta_json": {
                "broker_name": "zerodha",
                "message_type": "order_confirmation",
                "actions": [
                    {
                        "description": "Buy Stock",
                        "type": "orders",
                        "message": "buy_stock",
                    }
                ],
            },
            "order_id": None,
            "message_type": "order_confirmation",
        },
    ]

    # Save to CouchDB
    for message in chat_messages:
        real_couchdb_connection.save(message)

    yield chat_messages

    # Cleanup - delete test data
    for message in chat_messages:
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass


@pytest.fixture
def real_orders_data(real_couchdb_connection, test_user_id):
    """Create real orders data in CouchDB."""
    # Create test orders
    orders = [
        {
            "_id": f"order_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "quantity": 100,
            "price": 2500.0,
            "status": "executed",
            "created_at": datetime.now().isoformat(),
            "order_type": "MARKET",
            "transaction_type": "BUY",
        },
        {
            "_id": f"order_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "TCS",
            "quantity": 50,
            "price": 3500.0,
            "status": "pending",
            "created_at": datetime.now().isoformat(),
            "order_type": "LIMIT",
            "transaction_type": "SELL",
        },
    ]

    # Save to CouchDB
    for order in orders:
        real_couchdb_connection.save(order)

    yield orders

    # Cleanup - delete test data
    for order in orders:
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass


@pytest.fixture
def real_monitoring_data(real_couchdb_connection, test_user_id):
    """Create real monitoring data in CouchDB."""
    # Create test monitoring instances
    monitoring_instances = [
        {
            "_id": f"monitor_{uuid.uuid4().hex[:8]}",
            "type": "monitoring_instance",
            "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "status": "active",
            "created_at": datetime.now().isoformat(),
            "desc": "Price Alert for RELIANCE",
            "price": 2500.0,
            "trigger_price": 2600.0,
        },
        {
            "_id": f"monitor_{uuid.uuid4().hex[:8]}",
            "type": "monitoring_instance",
            "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": "TCS",
            "status": "inactive",
            "created_at": datetime.now().isoformat(),
            "desc": "Portfolio Monitoring for TCS",
            "price": 3500.0,
            "trigger_price": 3400.0,
        },
    ]

    # Save to CouchDB
    for instance in monitoring_instances:
        real_couchdb_connection.save(instance)

    yield monitoring_instances

    # Cleanup - delete test data
    for instance in monitoring_instances:
        try:
            doc = real_couchdb_connection.get(instance["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass


@pytest.fixture
def sample_chat_history_request(test_user_id, test_conversation_id) -> NewChatHistoryRequest:
    """Sample chat history request for testing."""
    return NewChatHistoryRequest(
        user_id=test_user_id,
        conversation_id=test_conversation_id,
        type=ConversationType.CHAT,
        brokerName=BrokerName.ZERODHA,
    )


@pytest.fixture
def sample_orders_request(test_user_id) -> OrdersRequest:
    """Sample orders request for testing."""
    return OrdersRequest(user_id=test_user_id, broker=BrokerName.ZERODHA, status=None)


@pytest.fixture
def sample_monitoring_request(test_user_id) -> MonitoringInstancesRequest:
    """Sample monitoring request for testing."""
    return MonitoringInstancesRequest(user_id=test_user_id)


@pytest.fixture
def sample_websocket_request(test_user_id, test_conversation_id) -> WebSocketChatRequest:
    """Sample WebSocket request for testing."""
    return WebSocketChatRequest(
        user_id=test_user_id,
        conversation_id=test_conversation_id,
        brokerName=BrokerName.ZERODHA,
        message="Hello, I want to place an order",
        typeOfMessage=WebSocketMessageType.CHAT,
        modelId="gpt-4",
        sender="user",
    )


@pytest.fixture
def test_environment():
    """Set up test environment variables."""
    original_env = os.environ.copy()

    # Ensure CouchDB environment variables are set for testing
    if "COUCHDB_PRIVATE_USERNAME" not in os.environ:
        os.environ["COUCHDB_PRIVATE_USERNAME"] = "admin"
    if "COUCHDB_PRIVATE_PASSWORD" not in os.environ:
        os.environ["COUCHDB_PRIVATE_PASSWORD"] = "password"
    if "COUCHDB_PRIVATE_HOST" not in os.environ:
        os.environ["COUCHDB_PRIVATE_HOST"] = "localhost"
    if "COUCHDB_PRIVATE_PORT" not in os.environ:
        os.environ["COUCHDB_PRIVATE_PORT"] = "5984"
    if "COUCHDB_PRIVATE_USE_SSL" not in os.environ:
        os.environ["COUCHDB_PRIVATE_USE_SSL"] = "false"
    # New environment variable required for per-user database approach
    if "COUCHDB_USER_CRED_SALT" not in os.environ:
        os.environ["COUCHDB_USER_CRED_SALT"] = "test_salt_for_testing"

    # Enable testing mode for mock LLM responses
    os.environ["MOCK_LLM_RESPONSES"] = "true"
    os.environ["MOCK_LLM_RESPONSE_TYPE"] = "orders"

    yield

    # Restore original environment
    os.environ.clear()
    os.environ.update(original_env)


@pytest.fixture
def large_test_dataset(real_couchdb_connection, test_user_id, test_conversation_id):
    """Create large test dataset in CouchDB for performance testing."""
    # Create large chat history dataset
    large_chat_history = []
    for i in range(1000):
        message = {
            "_id": f"chat_perf_{uuid.uuid4().hex[:8]}",
            "type": "chat_message",
            "user_id": test_user_id,
            "conversation_id": test_conversation_id,
            "timestamp": datetime.now().isoformat(),
            "role": "user" if i % 2 == 0 else "system",
            "message": f"Performance test message {i}",
            "meta_json": {"broker_name": "zerodha"},
            "message_type": "chat",
        }
        large_chat_history.append(message)
        real_couchdb_connection.save(message)

    # Create large orders dataset
    large_orders = []
    for i in range(500):
        order = {
            "_id": f"order_perf_{uuid.uuid4().hex[:8]}",
            "type": "order",
            "order_id": f"ORD{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": f"STOCK_{i % 10}",
            "quantity": 100 + i,
            "price": 1000.0 + i,
            "status": "executed" if i % 3 == 0 else "pending",
            "created_at": datetime.now().isoformat(),
        }
        large_orders.append(order)
        real_couchdb_connection.save(order)

    # Create large monitoring dataset
    large_monitoring = []
    for i in range(200):
        instance = {
            "_id": f"monitor_perf_{uuid.uuid4().hex[:8]}",
            "type": "monitoring_instance",
            "monitoring_id": f"MON{uuid.uuid4().hex[:6].upper()}",
            "user_id": test_user_id,
            "broker_id": "zerodha",
            "symbol": f"STOCK_{i % 10}",
            "status": "active" if i % 2 == 0 else "inactive",
            "created_at": datetime.now().isoformat(),
            "desc": f"Performance monitoring {i}",
        }
        large_monitoring.append(instance)
        real_couchdb_connection.save(instance)

    yield {
        "large_chat_history": large_chat_history,
        "large_orders": large_orders,
        "large_monitoring": large_monitoring,
    }

    # Cleanup - delete all test data
    for message in large_chat_history:
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass

    for order in large_orders:
        try:
            doc = real_couchdb_connection.get(order["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass

    for instance in large_monitoring:
        try:
            doc = real_couchdb_connection.get(instance["_id"])
            real_couchdb_connection.delete(doc)
        except Exception:
            pass


@pytest.fixture
def websocket_service():
    """Get real WebSocket service instance."""
    return WebSocketChatService()
