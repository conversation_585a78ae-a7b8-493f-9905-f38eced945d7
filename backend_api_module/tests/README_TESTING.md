# Backend API Testing Module

This document provides comprehensive information about the testing framework for the backend API module, which uses **real CouchDB data** instead of mocks.

## Overview

The testing module is designed to provide rigorous testing of the backend API using actual CouchDB connections and real data. This approach ensures that tests reflect real-world scenarios and catch issues that might not be apparent with mocked data.

## Test Categories

### 1. Unit Tests (`tests/unit/`)

- **Purpose**: Test individual functions and methods in isolation
- **Scope**: Data mappers, connection managers, utility functions
- **Data**: Uses real CouchDB connections with test data
- **Files**:
  - `test_data_mapper.py` - Tests data transformation functions

### 2. Integration Tests (`tests/integration/`)

- **Purpose**: Test interactions between different components
- **Scope**: API endpoints, database services, data flow
- **Data**: Uses real CouchDB with test datasets
- **Files**:
  - `test_api_endpoints.py` - Tests all API endpoints with real data

### 3. Performance Tests (`tests/performance/`)

- **Purpose**: Assess API performance under various load conditions
- **Scope**: Response times, concurrent requests, scalability
- **Data**: Uses large datasets in CouchDB for realistic testing
- **Files**:
  - `test_api_performance.py` - Performance and load testing

### 4. Security Tests (`tests/security/`)

- **Purpose**: Verify API security against common vulnerabilities
- **Scope**: Input validation, authentication, data exposure
- **Data**: Uses real CouchDB with malicious test data
- **Files**:
  - `test_api_security.py` - Security vulnerability testing

## Key Features

### Real CouchDB Integration

- **No Mock Data**: All tests use actual CouchDB connections
- **Test Data Management**: Automatic creation and cleanup of test data
- **Isolation**: Each test uses unique data to prevent interference
- **Realistic Scenarios**: Tests reflect actual production conditions

### Comprehensive Coverage

- **API Endpoints**: All `/chatHistory`, `/orders`, `/monitoring/instances`, `/ws/chat` endpoints
- **Data Processing**: Complete data flow from CouchDB to API responses
- **Error Handling**: Tests for various error conditions and edge cases
- **Security**: Input validation, authentication, and vulnerability testing

### Performance Testing

- **Response Time**: Measures API response times under different loads
- **Concurrent Requests**: Tests system behavior with multiple simultaneous users
- **Scalability**: Assesses how performance scales with data size
- **Memory Usage**: Monitors memory consumption during operations

## Setup and Installation

### Prerequisites

1. **CouchDB**: Ensure CouchDB is running and accessible
2. **Python Environment**: Python 3.8+ with virtual environment
3. **Dependencies**: Install all required packages

### Environment Variables

Set the following environment variables for CouchDB connection:

```bash
export COUCHDB_PRIVATE_HOST=localhost
export COUCHDB_PRIVATE_PORT=5984
export COUCHDB_PRIVATE_USE_SSL=false
export COUCHDB_PRIVATE_USERNAME=admin
export COUCHDB_PRIVATE_PASSWORD=password

export COUCHDB_PUBLIC_HOST=localhost
export COUCHDB_PUBLIC_PORT=5984
export COUCHDB_PUBLIC_USE_SSL=false
```

### Installation

```bash
# Navigate to the backend_api_module directory
cd smart-agent/backend_api_module

# Activate virtual environment
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Verify installation
python tests/run_tests.py --check-env
```

## Running Tests

### Using the Test Runner Script

```bash
# Run all tests
python tests/run_tests.py

# Run specific test category
python tests/run_tests.py --category unit
python tests/run_tests.py --category integration
python tests/run_tests.py --category performance
python tests/run_tests.py --category security

# Run with verbose output
python tests/run_tests.py --verbose

# Generate coverage report
python tests/run_tests.py --coverage

# Check environment only
python tests/run_tests.py --check-env
```

### Using pytest Directly

```bash
# Run all tests
pytest

# Run specific category
pytest tests/unit/
pytest tests/integration/
pytest tests/performance/
pytest tests/security/

# Run with markers
pytest -m unit
pytest -m integration
pytest -m performance
pytest -m security

# Run with coverage
pytest --cov=src --cov-report=html
```

## Test Data Management

### Automatic Data Creation

Each test fixture automatically creates necessary test data in CouchDB:

```python
@pytest.fixture
def real_chat_history_data(real_couchdb_connection, test_user_id, test_conversation_id):
    """Create real chat history data in CouchDB."""
    # Create test messages
    chat_messages = [...]

    # Save to CouchDB
    for message in chat_messages:
        real_couchdb_connection.save(message)

    yield chat_messages

    # Cleanup - delete test data
    for message in chat_messages:
        try:
            doc = real_couchdb_connection.get(message["_id"])
            real_couchdb_connection.delete(doc)
        except:
            pass
```

### Data Isolation

- **Unique IDs**: Each test uses unique document IDs to prevent conflicts
- **User Isolation**: Tests use different user IDs to ensure data separation
- **Automatic Cleanup**: Test data is automatically removed after each test

## Test Scenarios

### Chat History Tests

- **Basic Retrieval**: Fetch chat history for a conversation
- **Empty Results**: Handle cases with no chat data
- **Large Datasets**: Performance with 1000+ messages
- **Multiple Users**: Concurrent access to same conversation
- **Data Validation**: Verify message structure and content

### Orders Tests

- **Order Retrieval**: Fetch orders with various filters
- **Status Filtering**: Filter by order status (executed, pending, etc.)
- **Broker Filtering**: Filter by broker (Zerodha, Upstox, etc.)
- **Data Consistency**: Verify order data integrity
- **Performance**: Handle large order datasets

### Monitoring Tests

- **Instance Retrieval**: Fetch monitoring instances
- **Status Filtering**: Filter by active/inactive status
- **Data Validation**: Verify monitoring data structure
- **User Isolation**: Ensure users can only see their own monitoring

### WebSocket Tests

- **Connection Management**: Test WebSocket connections
- **Message Handling**: Process real-time chat messages
- **Error Handling**: Handle malformed messages
- **Concurrent Connections**: Multiple simultaneous users
- **Security**: Authentication and authorization

### Security Tests

- **SQL Injection**: Test against SQL injection attempts
- **XSS Protection**: Verify XSS payload handling
- **Authentication**: Test unauthorized access attempts
- **Data Exposure**: Check for sensitive data leakage
- **Input Validation**: Validate all input parameters

## Performance Benchmarks

### Response Time Targets

- **Small Dataset (< 100 records)**: < 1 second
- **Medium Dataset (100-1000 records)**: < 3 seconds
- **Large Dataset (1000+ records)**: < 5 seconds

### Concurrent User Targets

- **10 Concurrent Users**: < 2 seconds average response time
- **50 Concurrent Users**: < 3 seconds average response time
- **100 Concurrent Users**: < 5 seconds average response time

### Memory Usage Limits

- **Chat History (1000 messages)**: < 100MB memory increase
- **Orders (500 orders)**: < 50MB memory increase
- **Monitoring (200 instances)**: < 30MB memory increase

## Troubleshooting

### Common Issues

#### CouchDB Connection Failed

```bash
# Check if CouchDB is running
curl http://localhost:5984

# Verify credentials
curl -u admin:password http://localhost:5984/_utils
```

#### Missing Dependencies

```bash
# Install missing packages
pip install pytest pytest-asyncio pytest-cov psutil

# Check installed packages
pip list | grep pytest
```

### Debug Mode

```bash
# Run tests with debug output
pytest -v -s --tb=long

# Run specific test with debug
pytest tests/integration/test_api_endpoints.py::TestChatHistoryEndpoint::test_get_chat_history_success -v -s
```

## Continuous Integration

### GitHub Actions Example

```yaml
name: Backend API Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    services:
      couchdb:
        image: couchdb:3.3
        env:
          COUCHDB_USER: admin
          COUCHDB_PASSWORD: password
        ports:
          - 5984:5984
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        run: |
          pip install -r requirements.txt
      - name: Run tests
        run: |
          python tests/run_tests.py --coverage
        env: 
          COUCHDB_PRIVATE_HOST=localhost
          COUCHDB_PRIVATE_PORT=5984
          COUCHDB_PRIVATE_USE_SSL=false
          COUCHDB_PRIVATE_USERNAME=admin
          COUCHDB_PRIVATE_PASSWORD=password
          COUCHDB_PUBLIC_HOST=localhost
          COUCHDB_PUBLIC_PORT=5984
          COUCHDB_PUBLIC_USE_SSL=false
```

## Best Practices

### Writing Tests

1. **Use Real Data**: Always use real CouchDB connections
2. **Clean Up**: Ensure test data is properly cleaned up
3. **Isolation**: Each test should be independent
4. **Descriptive Names**: Use clear, descriptive test names
5. **Assertions**: Include meaningful assertions

### Test Organization

1. **Group Related Tests**: Use test classes to group related functionality
2. **Use Fixtures**: Leverage pytest fixtures for common setup
3. **Mark Tests**: Use appropriate pytest markers
4. **Documentation**: Include docstrings explaining test purpose

### Performance Considerations

1. **Efficient Queries**: Use efficient CouchDB queries
2. **Batch Operations**: Group operations where possible
3. **Resource Cleanup**: Ensure proper resource cleanup
4. **Monitoring**: Monitor memory and CPU usage during tests

## Contributing

### Adding New Tests

1. **Follow Naming Convention**: Use `test_*.py` for test files
2. **Use Real Data**: Always use real CouchDB data, not mocks
3. **Include Cleanup**: Ensure proper test data cleanup
4. **Add Documentation**: Include clear docstrings
5. **Update This Document**: Update relevant sections of this README

### Test Review Process

1. **Code Review**: All tests must be reviewed
2. **Performance Check**: Ensure tests don't impact performance
3. **Coverage Analysis**: Verify adequate test coverage
4. **Documentation**: Ensure tests are well-documented

## Support

For questions or issues with the testing framework:

1. **Check Documentation**: Review this README and inline comments
2. **Run Environment Check**: Use `python tests/run_tests.py --check-env`
3. **Review Logs**: Check test output for specific error messages
4. **Verify Setup**: Ensure CouchDB and dependencies are properly configured

---

**Note**: This testing framework is designed to use real CouchDB data to ensure comprehensive and realistic testing. All tests create and clean up their own test data to maintain isolation and prevent interference between tests.
