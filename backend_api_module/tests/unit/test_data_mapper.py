"""Unit tests for data mapper functions."""

from unittest.mock import patch

from src.utils.data_mapper import (
    chat_history_dict_to_response,
    monitoring_dict_to_response,
    orders_dict_to_response,
    prepare_chat_message_for_save,
    prepare_monitoring_for_save,
    prepare_order_for_save,
    prepare_summary_for_save,
)


class TestChatHistoryDataMapper:
    """Test cases for chat history data mapping functions."""

    def test_chat_history_dict_to_response_valid_data(self):
        """Test converting valid chat history document to response format."""
        chat_doc = {
            "_id": "chat_123",
            "user_id": "user_456",
            "conversation_id": "conv_789",
            "timestamp": "2024-01-01T10:00:00Z",
            "role": "user",
            "message": "Hello, world!",
            "llm_model_version": "gpt-4",
            "meta_json": {"broker_name": "zerodha"},
            "order_id": "order_123",
            "type": "chat",
        }

        result = chat_history_dict_to_response(chat_doc)

        assert result["chat_id"] == "chat_123"
        assert result["user_id"] == "user_456"
        assert result["conversation_id"] == "conv_789"
        assert result["timestamp"] == "2024-01-01T10:00:00Z"
        assert result["role"] == "user"
        assert result["message"] == "Hello, world!"
        assert result["llm_model_version"] == "gpt-4"
        assert result["meta_json"] == {"broker_name": "zerodha"}
        assert result["order_id"] == "order_123"
        assert result["type"] == "message"

    def test_chat_history_dict_to_response_missing_fields(self):
        """Test converting chat history document with missing fields."""
        chat_doc = {"_id": "chat_123", "user_id": "user_456"}

        result = chat_history_dict_to_response(chat_doc)

        assert result["chat_id"] == "chat_123"
        assert result["user_id"] == "user_456"
        assert result["conversation_id"] == ""
        assert result["timestamp"] == ""
        assert result["role"] == ""
        assert result["message"] == ""
        assert result["llm_model_version"] is None
        assert result["meta_json"] == {}
        assert result["order_id"] is None
        assert result["type"] == "message"

    def test_chat_history_dict_to_response_empty_document(self):
        """Test converting empty chat history document."""
        chat_doc = {}

        result = chat_history_dict_to_response(chat_doc)

        assert result["chat_id"] == ""
        assert result["user_id"] == ""
        assert result["conversation_id"] == ""
        assert result["timestamp"] == ""
        assert result["role"] == ""
        assert result["message"] == ""
        assert result["llm_model_version"] is None
        assert result["meta_json"] == {}
        assert result["order_id"] is None
        assert result["type"] == "message"

    def test_prepare_chat_message_for_save(self):
        """Test preparing chat message for saving to CouchDB."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            result = prepare_chat_message_for_save(
                user_id="user_123",
                conversation_id="conv_456",
                role="user",
                message="Test message",
                llm_model_version="gpt-4",
                meta_json={"broker_name": "zerodha"},
                order_id="order_123",
                type="chat",
            )

            assert result["type"] == "chat_message"
            assert result["user_id"] == "user_123"
            assert result["conversation_id"] == "conv_456"
            assert result["timestamp"] == "2024-01-01T10:00:00Z"
            assert result["role"] == "user"
            assert result["message"] == "Test message"
            assert result["llm_model_version"] == "gpt-4"
            assert result["meta_json"] == {"broker_name": "zerodha"}
            assert result["order_id"] == "order_123"
            assert result["message_type"] == "chat"

    def test_prepare_chat_message_for_save_minimal_data(self):
        """Test preparing chat message with minimal required data."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            result = prepare_chat_message_for_save(
                user_id="user_123",
                conversation_id="conv_456",
                role="user",
                message="Test message",
            )

            assert result["type"] == "chat_message"
            assert result["user_id"] == "user_123"
            assert result["conversation_id"] == "conv_456"
            assert result["role"] == "user"
            assert result["message"] == "Test message"
            assert result["llm_model_version"] is None
            assert result["meta_json"] == {}
            assert result["order_id"] is None
            assert result["message_type"] == "message"


class TestOrdersDataMapper:
    """Test cases for orders data mapping functions."""

    def test_orders_dict_to_response_valid_data(self):
        """Test converting valid order document to response format."""
        order_doc = {
            "_id": "order_123",
            "order_id": "ORD001",
            "user_id": "user_456",
            "account_id": "acc_789",
            "broker_id": "zerodha",
            "instrument_id": "inst_123",
            "symbol": "RELIANCE",
            "broker_order_id": "BROK_001",
            "order_type": "MARKET",
            "transaction_type": "BUY",
            "quantity": 100,
            "price": 2500.0,
            "trigger_price": 2400.0,
            "status": "executed",
            "validity": "DAY",
            "product_type": "CNC",
            "created_at": "2024-01-01T10:00:00Z",
            "updated_at": "2024-01-01T10:01:00Z",
            "completed_at": "2024-01-01T10:02:00Z",
            "parent_order_id": "PARENT_001",
            "comments": "Test order",
            "submitted_by": "user_456",
            "source": "API",
            "llm_intent_id": "intent_123",
            "strategy_id": "strategy_456",
            "is_automated": True,
            "risk_score": 0.5,
            "stop_loss_price": 2400.0,
            "take_profit_price": 2600.0,
            "trailing_stop_percent": 2.0,
            "portfolio_id": "portfolio_123",
            "goal_id": "goal_456",
        }

        result = orders_dict_to_response(order_doc)

        assert result["order_id"] == "ORD001"
        assert result["user_id"] == "user_456"
        assert result["account_id"] == "acc_789"
        assert result["broker_id"] == "zerodha"
        assert result["instrument_id"] == "inst_123"
        assert result["symbol"] == "RELIANCE"
        assert result["broker_order_id"] == "BROK_001"
        assert result["order_type"] == "MARKET"
        assert result["transaction_type"] == "BUY"
        assert result["quantity"] == 100
        assert result["price"] == 2500.0
        assert result["trigger_price"] == 2400.0
        assert result["status"] == "executed"
        assert result["validity"] == "DAY"
        assert result["product_type"] == "CNC"
        assert result["created_at"] == "2024-01-01T10:00:00Z"
        assert result["updated_at"] == "2024-01-01T10:01:00Z"
        assert result["completed_at"] == "2024-01-01T10:02:00Z"
        assert result["parent_order_id"] == "PARENT_001"
        assert result["comments"] == "Test order"
        assert result["submitted_by"] == "user_456"
        assert result["source"] == "API"
        assert result["llm_intent_id"] == "intent_123"
        assert result["strategy_id"] == "strategy_456"
        assert result["is_automated"] is True
        assert result["risk_score"] == 0.5
        assert result["stop_loss_price"] == 2400.0
        assert result["take_profit_price"] == 2600.0
        assert result["trailing_stop_percent"] == 2.0
        assert result["portfolio_id"] == "portfolio_123"
        assert result["goal_id"] == "goal_456"

    def test_orders_dict_to_response_missing_fields(self):
        """Test converting order document with missing fields."""
        order_doc = {"_id": "order_123", "order_id": "ORD001", "user_id": "user_456"}

        result = orders_dict_to_response(order_doc)

        assert result["order_id"] == "ORD001"
        assert result["user_id"] == "user_456"
        assert result["account_id"] == ""
        assert result["broker_id"] is None
        assert result["instrument_id"] == ""
        assert result["symbol"] == ""
        assert result["broker_order_id"] is None
        assert result["order_type"] == ""
        assert result["transaction_type"] == ""
        assert result["quantity"] == 0
        assert result["price"] is None
        assert result["trigger_price"] is None
        assert result["status"] == ""
        assert result["validity"] == ""
        assert result["product_type"] == ""
        assert result["created_at"] == ""
        assert result["updated_at"] == ""
        assert result["completed_at"] is None
        assert result["parent_order_id"] is None
        assert result["comments"] is None
        assert result["submitted_by"] == ""
        assert result["source"] == ""
        assert result["llm_intent_id"] is None
        assert result["strategy_id"] is None
        assert result["is_automated"] is False
        assert result["risk_score"] == 0.0
        assert result["stop_loss_price"] is None
        assert result["take_profit_price"] is None
        assert result["trailing_stop_percent"] is None
        assert result["portfolio_id"] is None
        assert result["goal_id"] is None

    def test_prepare_order_for_save(self):
        """Test preparing order data for saving to CouchDB."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            order_data = {
                "order_id": "ORD001",
                "user_id": "user_123",
                "symbol": "RELIANCE",
                "quantity": 100,
                "price": 2500.0,
            }

            result = prepare_order_for_save(order_data)

            assert result["type"] == "order"
            assert result["created_at"] == "2024-01-01T10:00:00Z"
            assert result["updated_at"] == "2024-01-01T10:00:00Z"
            assert result["order_id"] == "ORD001"
            assert result["user_id"] == "user_123"
            assert result["symbol"] == "RELIANCE"
            assert result["quantity"] == 100
            assert result["price"] == 2500.0


class TestMonitoringDataMapper:
    """Test cases for monitoring data mapping functions."""

    def test_monitoring_dict_to_response_valid_data(self):
        """Test converting valid monitoring document to response format."""
        monitoring_doc = {
            "_id": "monitor_123",
            "monitoring_id": "MON001",
            "user_id": "user_456",
            "account_id": "acc_789",
            "broker_id": "zerodha",
            "symbol": "RELIANCE",
            "broker_trade_id": "TRADE_001",
            "execution_time": "2024-01-01T10:00:00Z",
            "executed_quantity": 100,
            "executed_price": 2500.0,
            "transaction_type": "BUY",
            "exchange": "NSE",
            "brokerage_fee": 20.0,
            "taxes_fees": 10.0,
            "net_amount": 2510.0,
            "status": "active",
            "exec_ref": "EXEC_001",
            "created_at": "2024-01-01T10:00:00Z",
            "order_type": "LIMIT",
            "quantity": 100,
            "price": 2500.0,
            "trigger_price": 2400.0,
            "validity": "DAY",
            "product_type": "CNC",
            "desc": "Price Alert for RELIANCE",
            "stop_loss_price": 2400.0,
            "take_profit_price": 2600.0,
            "trailing_stop_percent": 2.0,
        }

        result = monitoring_dict_to_response(monitoring_doc)

        assert result["monitoring_id"] == "MON001"
        assert result["user_id"] == "user_456"
        assert result["account_id"] == "acc_789"
        assert result["broker_id"] == "zerodha"
        assert result["symbol"] == "RELIANCE"
        assert result["broker_trade_id"] == "TRADE_001"
        assert result["execution_time"] == "2024-01-01T10:00:00Z"
        assert result["executed_quantity"] == 100
        assert result["executed_price"] == 2500.0
        assert result["transaction_type"] == "BUY"
        assert result["exchange"] == "NSE"
        assert result["brokerage_fee"] == 20.0
        assert result["taxes_fees"] == 10.0
        assert result["net_amount"] == 2510.0
        assert result["status"] == "active"
        assert result["exec_ref"] == "EXEC_001"
        assert result["created_at"] == "2024-01-01T10:00:00Z"
        assert result["order_type"] == "LIMIT"
        assert result["quantity"] == 100
        assert result["price"] == 2500.0
        assert result["trigger_price"] == 2400.0
        assert result["validity"] == "DAY"
        assert result["product_type"] == "CNC"
        assert result["desc"] == "Price Alert for RELIANCE"
        assert result["stop_loss_price"] == 2400.0
        assert result["take_profit_price"] == 2600.0
        assert result["trailing_stop_percent"] == 2.0

    def test_monitoring_dict_to_response_missing_fields(self):
        """Test converting monitoring document with missing fields."""
        monitoring_doc = {
            "_id": "monitor_123",
            "monitoring_id": "MON001",
            "user_id": "user_456",
        }

        result = monitoring_dict_to_response(monitoring_doc)

        assert result["monitoring_id"] == "MON001"
        assert result["user_id"] == "user_456"
        assert result["account_id"] == ""
        assert result["broker_id"] == ""
        assert result["symbol"] == ""
        assert result["broker_trade_id"] == ""
        assert result["execution_time"] == ""
        assert result["executed_quantity"] == 0
        assert result["executed_price"] == 0.0
        assert result["transaction_type"] == ""
        assert result["exchange"] == ""
        assert result["brokerage_fee"] == 0.0
        assert result["taxes_fees"] == 0.0
        assert result["net_amount"] == 0.0
        assert result["status"] == ""
        assert result["exec_ref"] == ""
        assert result["created_at"] == ""
        assert result["order_type"] == ""
        assert result["quantity"] == 0
        assert result["price"] == 0.0
        assert result["trigger_price"] is None
        assert result["validity"] == ""
        assert result["product_type"] == ""
        assert result["desc"] == ""
        assert result["stop_loss_price"] is None
        assert result["take_profit_price"] is None
        assert result["trailing_stop_percent"] is None

    def test_prepare_monitoring_for_save(self):
        """Test preparing monitoring data for saving to CouchDB."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            monitoring_data = {
                "monitoring_id": "MON001",
                "user_id": "user_123",
                "symbol": "RELIANCE",
                "status": "active",
            }

            result = prepare_monitoring_for_save(monitoring_data)

            assert result["type"] == "monitoring_instance"
            assert result["created_at"] == "2024-01-01T10:00:00Z"
            assert result["monitoring_id"] == "MON001"
            assert result["user_id"] == "user_123"
            assert result["symbol"] == "RELIANCE"
            assert result["status"] == "active"


class TestSummaryDataMapper:
    """Test cases for summary data mapping functions."""

    def test_prepare_summary_for_save(self):
        """Test preparing summary data for saving to CouchDB."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            result = prepare_summary_for_save(
                user_id="user_123",
                conversation_id="conv_456",
                summary="This is a test summary",
                llm_model_version="gpt-4",
                meta_json={"context_topics": ["orders", "monitoring"]},
            )

            assert result["type"] == "summary"
            assert result["user_id"] == "user_123"
            assert result["conversation_id"] == "conv_456"
            assert result["timestamp"] == "2024-01-01T10:00:00Z"
            assert result["summary"] == "This is a test summary"
            assert result["llm_model_version"] == "gpt-4"
            assert result["meta_json"] == {"context_topics": ["orders", "monitoring"]}

    def test_prepare_summary_for_save_minimal_data(self):
        """Test preparing summary data with minimal required data."""
        with patch("src.utils.data_mapper.datetime") as mock_datetime:
            mock_datetime.now.return_value.isoformat.return_value = "2024-01-01T10:00:00Z"

            result = prepare_summary_for_save(
                user_id="user_123",
                conversation_id="conv_456",
                summary="This is a test summary",
            )

            assert result["type"] == "summary"
            assert result["user_id"] == "user_123"
            assert result["conversation_id"] == "conv_456"
            assert result["summary"] == "This is a test summary"
            assert result["llm_model_version"] is None
            assert result["meta_json"] == {}


class TestDataMapperEdgeCases:
    """Test edge cases for data mapper functions."""

    def test_none_values_handling(self):
        """Test handling of None values in data mapping."""
        # Test with None values in chat history
        chat_doc = {"_id": None, "user_id": None, "message": None, "meta_json": None}

        result = chat_history_dict_to_response(chat_doc)

        assert result["chat_id"] == ""
        assert result["user_id"] == ""
        assert result["message"] == ""
        assert result["meta_json"] == {}

        # Test with None values in orders
        order_doc = {"_id": None, "order_id": None, "quantity": None, "price": None}

        result = orders_dict_to_response(order_doc)

        assert result["order_id"] == ""
        assert result["quantity"] == 0
        assert result["price"] is None

        # Test with None values in monitoring
        monitoring_doc = {
            "_id": None,
            "monitoring_id": None,
            "executed_quantity": None,
            "executed_price": None,
        }

        result = monitoring_dict_to_response(monitoring_doc)

        assert result["monitoring_id"] == ""
        assert result["executed_quantity"] == 0
        assert result["executed_price"] == 0.0

    def test_empty_strings_handling(self):
        """Test handling of empty strings in data mapping."""
        chat_doc = {"_id": "", "user_id": "", "message": "", "meta_json": {}}

        result = chat_history_dict_to_response(chat_doc)

        assert result["chat_id"] == ""
        assert result["user_id"] == ""
        assert result["message"] == ""
        assert result["meta_json"] == {}

    def test_large_numbers_handling(self):
        """Test handling of large numbers in data mapping."""
        order_doc = {
            "_id": "order_123",
            "order_id": "ORD001",
            "quantity": 999999999,
            "price": 999999.99,
        }

        result = orders_dict_to_response(order_doc)

        assert result["quantity"] == 999999999
        assert result["price"] == 999999.99

    def test_special_characters_handling(self):
        """Test handling of special characters in data mapping."""
        chat_doc = {
            "_id": "chat_123",
            "user_id": "user_456",
            "message": "Hello! @#$%^&*()_+-=[]{}|;':\",./<>?",
            "meta_json": {"special": "!@#$%^&*()"},
        }

        result = chat_history_dict_to_response(chat_doc)

        assert result["message"] == "Hello! @#$%^&*()_+-=[]{}|;':\",./<>?"
        assert result["meta_json"]["special"] == "!@#$%^&*()"
