.PHONY: help format lint type-check test clean install-dev install-pre-commit

help:  ## Show this help message
	@echo "Available commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

install-dev:  ## Install development dependencies
	pip install -e ".[dev]"

install-pre-commit:  ## Install pre-commit hooks
	pre-commit install

format:  ## Format code with black and isort
	black src/ tests/
	isort src/ tests/

lint:  ## Run flake8 linting
	flake8 src/ tests/

type-check:  ## Run mypy type checking
	mypy --cache-fine-grained src/

check: format lint type-check  ## Run all checks (format, lint, type-check)

test:  ## Run tests
	pytest tests/ -v

clean:  ## Clean up cache files
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete

pre-commit:  ## Run pre-commit on all files
	pre-commit run --all-files

run-pr-workflow:
	act pull_request --secret-file .secrets -W .github/workflows/backend-pr-checks.yml 