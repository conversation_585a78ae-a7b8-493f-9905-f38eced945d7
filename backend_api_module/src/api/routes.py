"""API routes using real CouchDB for the backend module."""

import logging

from fastapi import APIRouter, HTTPException, Request, WebSocket, WebSocketDisconnect
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

# Import CouchDB-compatible functions from datastore
from ..datastore import (
    fetch_monitoring_by_user,
    fetch_orders_by_user,
    get_chat_messages,
)
from ..middleware.auth_middleware import firebase_auth_required
from ..models.schemas import (  # New schemas; Orders; Monitoring; WebSocket
    ChatHistoryAction,
    ChatHistoryMessage,
    ChatHistoryMessageType,
    ConversationType,
    MonitoringInstanceItem,
    MonitoringInstancesRequest,
    MonitoringInstancesResponse,
    NewChatHistoryRequest,
    NewChatHistoryResponse,
    OrderItem,
    OrdersRequest,
    OrdersResponse,
)

# Import proper services and connection manager
from ..services.websocket_service import websocket_chat_service
from ..utils.data_mapper import monitoring_dict_to_response, orders_dict_to_response

# Create router
router = APIRouter(prefix="/api/v1", tags=["api"])


@router.post("/chatHistory", response_model=NewChatHistoryResponse)
@firebase_auth_required
async def get_chat_history(request_body: NewChatHistoryRequest, request: Request) -> NewChatHistoryResponse:
    """Get chat history for a specific conversation using CouchDB."""
    try:
        try:
            messages = get_chat_messages(
                request.state.user["phone_number"],
                request_body.user_id,
                request_body.conversation_id,
            )
        except Exception as db_error:
            logging.exception(f"CouchDB connection/query error in chatHistory: {db_error}")
            # Return empty response if database is unavailable
            return NewChatHistoryResponse(
                user_id=request_body.user_id,
                conversation_id=request_body.conversation_id,
                type=request_body.type,
                brokerName=request_body.brokerName,
                history=[],
            )

        # Convert messages to response format
        chat_messages = []
        for msg in messages:
            # Skip invalid messages
            if not msg or not isinstance(msg, dict):
                continue

            # Get meta_json for actions and primitives
            meta_json = msg.get("meta_json", {})

            # Parse actions from meta_json
            actions = []
            actions_data = meta_json.get("actions", [])
            if isinstance(actions_data, list):
                for action in actions_data:
                    if not isinstance(action, dict):
                        continue

                    # Map action type properly
                    action_type = action.get("type", "chat").lower()
                    if action_type in ["orders", "monitoring", "chat"]:
                        conv_type = ConversationType(action_type)
                    else:
                        conv_type = ConversationType.CHAT  # Default fallback

                    actions.append(
                        ChatHistoryAction(
                            description=action.get("description", ""),
                            type=conv_type,
                            message=action.get("message", ""),
                        )
                    )

            # Create chat message using data from CouchDB
            # Map message type properly based on available enum values
            message_type = msg.get("message_type", msg.get("type", "order_confirmation")).lower()
            if message_type == "order_planning" or "planning" in message_type:
                msg_type = ChatHistoryMessageType.ORDER_PLANNING
            elif message_type == "orders" or "order" in message_type:
                if "execution" in message_type:
                    msg_type = ChatHistoryMessageType.ORDER_EXECUTION
                else:
                    msg_type = ChatHistoryMessageType.ORDER_CONFIRMATION
            elif message_type == "monitoring" or "monitor" in message_type:
                msg_type = ChatHistoryMessageType.MONITOR_ORDER
            else:
                msg_type = ChatHistoryMessageType.ORDER_CONFIRMATION  # Default fallback

            chat_message = ChatHistoryMessage(
                textMessage=msg.get("message", ""),
                messageType=msg_type,
                primitives=meta_json.get("primitives", []),
                sender=msg.get("role", "system"),
                actions=actions,
                execution_request_id=meta_json.get("execution_request_id"),
            )
            chat_messages.append(chat_message)

        return NewChatHistoryResponse(
            user_id=request_body.user_id,
            conversation_id=request_body.conversation_id,
            type=request_body.type,
            brokerName=request_body.brokerName,
            history=chat_messages,
        )

    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat history: {str(e)}",
        )


@router.post("/orders", response_model=OrdersResponse)
@firebase_auth_required
async def get_orders(request_body: OrdersRequest, request: Request) -> OrdersResponse:
    """Get orders for a user using CouchDB."""
    try:
        try:
            orders = fetch_orders_by_user(
                request.state.user["phone_number"],
                request_body.user_id,
                request_body.broker,
                request_body.status,
            )
        except Exception as db_error:
            logging.exception(f"CouchDB connection/query error in orders: {db_error}")
            # Return empty response if database is unavailable
            return OrdersResponse(orders=[])

        # Convert to OrderItem format using data mapper
        order_items = []
        for order in orders:
            # Use data mapper to convert CouchDB document to API format
            order_dict = orders_dict_to_response(order)

            # Create OrderItem with proper type conversion and field mapping
            try:
                # Handle potential None values for numeric fields
                quantity_val = order_dict.get("quantity")
                quantity = int(quantity_val) if quantity_val is not None else 0

                price_val = order_dict.get("price")
                price = float(price_val) if price_val is not None else 0.0

                order_item = OrderItem(
                    order_id=str(order_dict.get("order_id", "")),
                    broker=str(order_dict.get("broker_id", "")),
                    symbol=str(order_dict.get("symbol", "")),
                    quantity=quantity,
                    price=price,
                    status=str(order_dict.get("status", "")),
                    timestamp=str(order_dict.get("created_at", "")),
                )
                order_items.append(order_item)
            except (ValueError, TypeError) as e:
                logging.warning(f"Skipping invalid order data: {e}, order_dict: {order_dict}")
                continue

        return OrdersResponse(orders=order_items)

    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get orders: {str(e)}",
        )


@router.post("/monitoring/instances", response_model=MonitoringInstancesResponse)
@firebase_auth_required
async def get_monitoring_instances(
    request_body: MonitoringInstancesRequest, request: Request
) -> MonitoringInstancesResponse:
    """Get monitoring instances for a user using CouchDB."""
    try:
        try:
            monitoring_instances = fetch_monitoring_by_user(request.state.user["phone_number"], request_body.user_id)
        except Exception as db_error:
            logging.exception(f"CouchDB connection/query error in monitoring: {db_error}")
            # Return empty response if database is unavailable
            return MonitoringInstancesResponse(monitoring_instances=[])

        # Convert to MonitoringInstanceItem format using data mapper
        instance_items = []
        for instance in monitoring_instances:
            # Use data mapper to convert CouchDB document to API format
            instance_dict = monitoring_dict_to_response(instance)

            try:
                instance_item = MonitoringInstanceItem(
                    instance_id=str(instance_dict.get("monitoring_id", "")),
                    broker=str(instance_dict.get("broker_id", "")),
                    name=str(
                        instance_dict.get(
                            "desc",
                            f"Monitoring Instance " f"{instance_dict.get('monitoring_id', 'Unknown')}",
                        )
                    ),
                    target=str(instance_dict.get("symbol", "")),
                    status=str(instance_dict.get("status", "")),
                    created_at=str(instance_dict.get("created_at", "")),
                )
                instance_items.append(instance_item)
            except (ValueError, TypeError) as e:
                logging.warning(f"Skipping invalid monitoring instance data: {e}, " f"instance_dict: {instance_dict}")
                continue

        return MonitoringInstancesResponse(monitoring_instances=instance_items)

    except Exception as e:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get monitoring instances: {str(e)}",
        )


# WebSocket endpoint for real-time chat
@router.websocket("/ws/chat")
async def websocket_chat_endpoint(websocket: WebSocket) -> None:
    """Use websocket for real-time chat.

    Authentication is handled by WebSocketSessionAuthMiddleware which attaches
    `session_user` to the ASGI scope.
    """

    logging.info("🌐 [ROUTE] WebSocket endpoint hit (cookie-auth via middleware)")

    try:
        # Extract user_info from scope (set by middleware)
        user_info = websocket.scope.get("session_user")
        if not user_info:
            await websocket.close(code=4401)
            return

        # Connect with authenticated user
        await websocket_chat_service.connect_authenticated(websocket, user_info)

        while True:
            data = await websocket.receive_text()
            success = await websocket_chat_service.handle_message(websocket, data)
            if not success:
                break

    except WebSocketDisconnect:
        print("WebSocket disconnected")
    except Exception as e:
        print(f"WebSocket error: {e}")
    finally:
        websocket_chat_service.disconnect(websocket)
