"""Authentication API routes for user management."""

import logging
from typing import Optional

from fastapi import APIRouter, HTTPException, Request, Response, status
from pydantic import BaseModel, Field

# Import CouchDB-compatible functions from datastore
from ..datastore import (
    check_user_exists_simple,
    create_user_session_mapping_simple,
    get_couchdb_url,
    get_user_by_auth_uid_simple,
    get_user_database,
    get_user_id_by_auth_uid_simple,
    save_user_profile_simple,
)
from ..middleware.auth_middleware import firebase_auth_required

# Import Firebase service and database functions
from ..services.firebase_service import get_firebase_auth_service, verify_firebase_token
from ..utils.config import get_settings

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


# Request/Response Models
class SignupRequest(BaseModel):
    """Request model for user signup."""

    firebase_token: str = Field(..., description="Firebase ID token")
    name: str = Field(..., min_length=1, max_length=100, description="User's full name")
    phone: Optional[str] = Field(None, description="User's phone number")


class SignupResponse(BaseModel):
    """Response model for user signup."""

    success: bool
    message: str
    # None for new users, populated on first WebSocket message
    user_id: Optional[str]
    firebase_uid: str


class ProfileRequest(BaseModel):
    """Request model for user profile updates."""

    name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None)


class ProfileResponse(BaseModel):
    """Response model for user profile."""

    firebase_uid: str
    name: str
    phone: Optional[str]
    user_id: str


class CheckUserRequest(BaseModel):
    """Request model for checking if user exists."""

    firebase_token: str = Field(..., description="Firebase ID token")


class CheckUserResponse(BaseModel):
    """Response model for user existence check."""

    exists: bool
    user_id: Optional[str] = None
    firebase_uid: str


class UserDbDetailsResponse(BaseModel):
    """Response model for user database details."""

    couchdb_url: str
    couchdb_user: str
    couchdb_password: str
    couchdb_database: str


# ---- Session Cookie Login/Logout ----
class SessionLoginRequest(BaseModel):
    """Request body for establishing a session cookie from a Firebase ID token."""

    idToken: str = Field(..., description="Firebase ID token from client")
    expiresInDays: int = Field(
        default=14,
        ge=1,
        le=14,
        description="Cookie lifetime in days (max 14)",
    )


class SessionLoginResponse(BaseModel):
    """Response indicating whether session cookie creation succeeded."""

    success: bool
    message: str


class SessionLogoutResponse(BaseModel):
    """Response indicating whether session cookie deletion (logout) succeeded."""

    success: bool
    message: str


@router.post("/signup", response_model=SignupResponse)
async def signup_user(request: SignupRequest) -> SignupResponse:
    """
    Create a new user profile after Firebase authentication.

    This is called when a new user completes the name input step.
    """
    try:
        # Verify Firebase token
        logger.info(f"🔍 [AUTH-CREATE] About to verify Firebase token: " f"{request.firebase_token[:50]}...")
        token_claims = verify_firebase_token(request.firebase_token)
        logger.info(f"🔍 [AUTH-CREATE] Token verification result: " f"{token_claims is not None}")
        if not token_claims:
            logger.error("🔍 [AUTH-CREATE] Firebase token verification failed - " "token_claims is None")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Firebase token",
            )
        logger.info(
            f"🔍 [AUTH-CREATE] Firebase token verified successfully for user: " f"{token_claims.get('uid', 'unknown')}"
        )

        firebase_uid = token_claims["firebase_uid"]
        phone_number = token_claims.get("phone_number")

        if not phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number not found in Firebase token",
            )

        # Check if user already exists
        if check_user_exists_simple(phone_number, firebase_uid):
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="User already exists")

        # Save user profile to database
        save_user_profile_simple(phone_number, firebase_uid, request.name, request.phone)

        # Note: Session mapping will be created later when user sends first
        # message via WebSocket
        logger.info(f"New user profile created: firebase_uid={firebase_uid}")

        return SignupResponse(
            success=True,
            message="User created successfully",
            user_id=None,  # No user_id until first WebSocket message
            firebase_uid=firebase_uid,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create user",
        )


@router.post("/check-user", response_model=CheckUserResponse)
async def check_user_exists(request: CheckUserRequest) -> CheckUserResponse:
    """
    Check if a user exists in the system after Firebase authentication.

    This is called during login to determine if name input step should be skipped.
    """
    try:
        # Verify Firebase token
        logger.info(f"🔍 [AUTH-CHECK] About to verify Firebase token: " f"{request.firebase_token[:50]}...")
        token_claims = verify_firebase_token(request.firebase_token)
        logger.info(f"🔍 [AUTH-CHECK] Token verification result: " f"{token_claims is not None}")
        if not token_claims:
            logger.error("🔍 [AUTH-CHECK] Firebase token verification failed - " "token_claims is None")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid Firebase token",
            )
        logger.info(
            f"🔍 [AUTH-CHECK] Firebase token verified successfully for user: " f"{token_claims.get('uid', 'unknown')}"
        )

        firebase_uid = token_claims["firebase_uid"]
        phone_number = token_claims.get("phone_number")

        if not phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number not found in Firebase token",
            )

        # Check if user exists
        exists = check_user_exists_simple(phone_number, firebase_uid)
        user_id = None

        if exists:
            # Get or create session mapping
            user_id = get_user_id_by_auth_uid_simple(phone_number, firebase_uid)
            if not user_id:
                # Create new session mapping if it doesn't exist
                user_id = create_user_session_mapping_simple(phone_number, firebase_uid)

        return CheckUserResponse(exists=exists, user_id=user_id, firebase_uid=firebase_uid)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking user existence: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to check user",
        )


@router.get("/profile", response_model=ProfileResponse)
@firebase_auth_required
async def get_user_profile(http_request: Request) -> ProfileResponse:
    """Get the current user's profile information."""
    try:
        # Get authenticated user info from middleware
        user_info = http_request.state.user
        firebase_uid = user_info["firebase_uid"]
        session_user_id = user_info["user_id"]
        phone_number = user_info["phone_number"]

        # Get user profile from database
        user_profile = get_user_by_auth_uid_simple(phone_number, firebase_uid)
        if not user_profile:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

        return ProfileResponse(
            firebase_uid=firebase_uid,
            name=user_profile.get("name", ""),
            phone=user_profile.get("phone"),
            user_id=session_user_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile",
        )


@router.put("/profile", response_model=ProfileResponse)
@firebase_auth_required
async def update_user_profile(http_request: Request, request: ProfileRequest) -> ProfileResponse:
    """Update the current user's profile information."""
    try:
        # Get authenticated user info from middleware
        user_info = http_request.state.user
        firebase_uid = user_info["firebase_uid"]
        session_user_id = user_info["user_id"]
        phone_number = user_info["phone_number"]

        # Get current profile
        current_profile = get_user_by_auth_uid_simple(phone_number, firebase_uid)
        if not current_profile:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User profile not found")

        # Update profile with new values (keep existing if not provided)
        updated_name = request.name if request.name is not None else current_profile.get("name", "")
        updated_phone = request.phone if request.phone is not None else current_profile.get("phone")

        # Save updated profile
        save_user_profile_simple(phone_number, firebase_uid, updated_name, updated_phone)

        logger.info(f"User profile updated: firebase_uid={firebase_uid}")

        return ProfileResponse(
            firebase_uid=firebase_uid,
            name=updated_name,
            phone=updated_phone,
            user_id=session_user_id,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user profile",
        )


@router.get("/get-user-db-details", response_model=UserDbDetailsResponse)
@firebase_auth_required
async def get_user_db_details(http_request: Request) -> UserDbDetailsResponse:
    """
    Get user's CouchDB database details, creating database and user if they don't exist.

    Requires Firebase token in Authorization header.
    """
    try:
        # Get authenticated user info from middleware
        user_info = http_request.state.user
        # Get phone number from token claims
        phone_number = user_info["phone_number"]
        if not phone_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Phone number not found in Firebase token",
            )

        logger.info(f"🔍 [DB-DETAILS] Processing request for phone: {phone_number}")

        # Use centralized database provisioning from datastore
        db_details = get_user_database(phone_number)

        logger.info(f"✅ [DB-DETAILS] Database details prepared for phone: {phone_number}")

        return UserDbDetailsResponse(
            couchdb_url=get_couchdb_url(phone_number),
            couchdb_user=db_details["username"],
            couchdb_password=db_details["password"],
            couchdb_database=db_details["database_name"],
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Error getting user database details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user database details",
        )


@router.post("/sessionLogin", response_model=SessionLoginResponse)
async def session_login(request: SessionLoginRequest, response: Response) -> SessionLoginResponse:
    """Create a long-lived Firebase session cookie and set it as HTTP-only.

    The client should call this after sign-in with their Firebase ID token.
    """
    try:
        settings = get_settings()
        firebase_service = get_firebase_auth_service()

        # Cap at 14 days per Firebase limits
        days = min(max(request.expiresInDays, 1), 14)

        # Create session cookie
        import datetime

        from firebase_admin import auth as admin_auth

        expires_in = datetime.timedelta(days=days)
        session_cookie = admin_auth.create_session_cookie(
            request.idToken,
            expires_in=expires_in,
            app=firebase_service._app,
        )

        # Set cookie on response
        _set_session_cookie(
            response,
            session_cookie,
            max_age=int(expires_in.total_seconds()),
            cookie_name=settings.session_cookie_name,
            domain=settings.session_cookie_domain,
            path=settings.session_cookie_path,
            secure=bool(settings.session_cookie_secure),
            samesite=settings.session_cookie_samesite,
        )

        return SessionLoginResponse(success=True, message="Session established")
    except Exception as e:
        logger.error(f"Failed to create session cookie: {e}")
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid ID token")


@router.post("/sessionLogout", response_model=SessionLogoutResponse)
async def session_logout(http_request: Request, response: Response) -> SessionLogoutResponse:
    """Clear the session cookie and revoke refresh tokens for this user."""
    try:
        settings = get_settings()
        firebase_service = get_firebase_auth_service()

        # If a valid session exists, revoke refresh tokens for that uid
        from firebase_admin import auth as admin_auth

        cookie = http_request.cookies.get(settings.session_cookie_name)
        if cookie:
            try:
                claims = admin_auth.verify_session_cookie(
                    cookie,
                    check_revoked=True,
                    app=firebase_service._app,
                )
                uid = claims.get("uid") or claims.get("user_id")
                if uid:
                    firebase_service.revoke_refresh_tokens(uid)
            except Exception:
                # Ignore verification issues on logout; still clear cookie
                pass

        # Clear cookie
        _clear_session_cookie(
            response,
            cookie_name=settings.session_cookie_name,
            domain=settings.session_cookie_domain,
            path=settings.session_cookie_path,
            secure=bool(settings.session_cookie_secure),
            samesite=settings.session_cookie_samesite,
        )

        return SessionLogoutResponse(success=True, message="Logged out")
    except Exception as e:
        logger.error(f"Logout failed: {e}")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Logout failed")


def _set_session_cookie(
    response: Response,
    value: str,
    *,
    max_age: int,
    cookie_name: str,
    domain: Optional[str],
    path: str,
    secure: bool,
    samesite: str,
) -> None:
    samesite_val = (samesite or "lax").lower()
    if samesite_val not in {"lax", "strict", "none"}:
        samesite_val = "lax"
    response.set_cookie(
        key=cookie_name,
        value=value,
        max_age=max_age,
        path=path,
        domain=domain,
        secure=secure,
        httponly=True,
        samesite=samesite_val,  # type: ignore[arg-type]
    )


def _clear_session_cookie(
    response: Response,
    *,
    cookie_name: str,
    domain: Optional[str],
    path: str,
    secure: bool,
    samesite: str,
) -> None:
    samesite_val = (samesite or "lax").lower()
    if samesite_val not in {"lax", "strict", "none"}:
        samesite_val = "lax"
    response.delete_cookie(
        key=cookie_name,
        path=path,
        domain=domain,
        secure=secure,
        httponly=True,
        samesite=samesite_val,  # type: ignore[arg-type]
    )
