"""Session Cookie Middleware using Firebase Admin session cookies.

This middleware verifies an HTTP-only session cookie on each request.
If valid, it attaches user info to `request.state.user` for downstream handlers.
It can also signal or perform renewal when near expiry if the client provides
an ID token in `X-Firebase-Id-Token`.
"""

import datetime
import logging
from typing import Any, Dict, Literal, Optional, cast

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint

from ..services.firebase_service import get_firebase_auth_service
from ..utils.config import Settings, get_settings

logger = logging.getLogger(__name__)


def _to_user_info_from_claims(claims: Dict[str, Any]) -> Dict[str, Any]:
    return {
        "firebase_uid": claims.get("uid") or claims.get("user_id") or claims.get("sub"),
        "email": claims.get("email"),
        "phone_number": claims.get("phone_number"),
        "email_verified": claims.get("email_verified", False),
        "auth_time": claims.get("auth_time"),
        "token_exp": claims.get("exp"),
        "token_iat": claims.get("iat"),
    }


class SessionCookieMiddleware(BaseHTTPMiddleware):
    """Attach verified user info to request.state.user when session cookie is valid."""

    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        """Verify session cookie on incoming HTTP requests and renew when needed."""
        settings = get_settings()
        cookie_name = settings.session_cookie_name
        session_cookie = request.cookies.get(cookie_name)

        firebase_service = get_firebase_auth_service()
        request.state.user = getattr(request.state, "user", None)

        # If cookie exists, verify and attach user
        claims: Optional[Dict[str, Any]] = None
        if session_cookie:
            try:
                from firebase_admin import auth as admin_auth

                # verify_session_cookie raises on invalid/expired/revoked
                claims = admin_auth.verify_session_cookie(
                    session_cookie,
                    check_revoked=True,
                    app=firebase_service._app,
                )
                if claims:
                    user_info = _to_user_info_from_claims(claims)
                    # Preserve any existing request.state.user (e.g., from header auth) only if missing
                    if not request.state.user:
                        request.state.user = user_info
            except Exception as e:
                # Invalid cookie: proceed without user, let route-level auth handle 401s
                logger.info(f"Invalid/expired session cookie: {e}")

        # Call downstream and get response
        response = await call_next(request)

        # Renewal hint/flow: if near expiry, either set a header to prompt client,
        # or renew transparently if client supplied a fresh ID token.
        try:
            if claims and isinstance(claims.get("exp"), (int, float)):
                exp_ts = int(claims["exp"])
                now_ts = int(datetime.datetime.utcnow().timestamp())
                hours_left = (exp_ts - now_ts) / 3600.0
                if hours_left <= settings.session_cookie_renewal_threshold_hours:
                    id_token = request.headers.get("X-Firebase-Id-Token")
                    if id_token:
                        # Renew using provided ID token
                        new_cookie, max_age = _create_session_cookie(id_token, settings.session_cookie_max_age_days)
                        _set_session_cookie_on_response(
                            response,
                            cookie_name,
                            new_cookie,
                            max_age,
                            settings,
                        )
                        response.headers["X-Session-Renewed"] = "true"
                    else:
                        response.headers["X-Session-Renew-Soon"] = "true"
        except Exception as e:
            logger.debug(f"Session renewal check failed: {e}")

        return response


def _create_session_cookie(id_token: str, max_age_days: int) -> tuple[str, int]:
    from firebase_admin import auth as admin_auth

    expires_in = datetime.timedelta(days=max_age_days)
    cookie = admin_auth.create_session_cookie(id_token, expires_in=expires_in)
    return cookie, int(expires_in.total_seconds())


def _set_session_cookie_on_response(
    response: Response,
    cookie_name: str,
    cookie_value: str,
    max_age_seconds: int,
    settings: Settings,
) -> None:
    # Map allowed samesite values
    samesite_value = settings.session_cookie_samesite.lower()
    if samesite_value not in {"lax", "strict", "none"}:
        samesite_value = "lax"

    # Cast to the expected literal type after validation
    samesite_value = cast(Literal["lax", "strict", "none"], samesite_value)

    response.set_cookie(
        key=cookie_name,
        value=cookie_value,
        max_age=max_age_seconds,
        path=settings.session_cookie_path,
        domain=settings.session_cookie_domain,
        secure=bool(settings.session_cookie_secure),
        httponly=True,
        samesite=samesite_value,
    )
