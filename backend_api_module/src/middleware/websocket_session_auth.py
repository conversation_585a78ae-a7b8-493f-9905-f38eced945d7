"""ASGI middleware to authenticate WebSocket connections using the HTTP-only session cookie.

Attaches verified user info onto `scope["session_user"]` for downstream handlers.
Closes the WebSocket with code 4401 when authentication fails.
"""

from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from typing import Any, Awaitable, Callable, Dict

from ..services.firebase_service import get_firebase_auth_service
from ..utils.config import get_settings
from .session_middleware import _to_user_info_from_claims


class WebSocketSessionAuthMiddleware:
    """Authenticate websocket connections via session cookie and set scope data."""

    def __init__(self, app: Callable[..., Awaitable[Any]]) -> None:
        """Initialize the middleware with the downstream ASGI app."""
        self.app = app

    async def __call__(self, scope, receive, send):  # type: ignore[no-untyped-def]
        """Verify the session cookie on websocket handshake and attach user info."""
        # Only handle WebSocket connections
        if scope.get("type") != "websocket":
            return await self.app(scope, receive, send)

        try:
            # Parse cookies from raw headers
            headers = dict(scope.get("headers") or [])
            raw_cookie: bytes = headers.get(b"cookie", b"")
            cookie_value = (
                raw_cookie.decode("latin-1") if isinstance(raw_cookie, (bytes, bytearray)) else str(raw_cookie)
            )

            cookies = SimpleCookie()
            if cookie_value:
                cookies.load(cookie_value)

            settings = get_settings()
            cookie_name = settings.session_cookie_name
            cookie = cookies.get(cookie_name)
            session_cookie = cookie.value if cookie else None

            if not session_cookie:
                await send({"type": "websocket.close", "code": 4401})
                return

            # Verify session cookie via Firebase Admin
            firebase_service = get_firebase_auth_service()
            try:
                from firebase_admin import auth as admin_auth

                claims = admin_auth.verify_session_cookie(session_cookie, check_revoked=True, app=firebase_service._app)
            except Exception:
                await send({"type": "websocket.close", "code": 4401})
                return

            user_info: Dict[str, Any] = _to_user_info_from_claims(claims)
            if not user_info or not user_info.get("firebase_uid"):
                await send({"type": "websocket.close", "code": 4401})
                return

            # Attach to scope for downstream use
            scope["session_user"] = user_info

            # Proceed to the application
            return await self.app(scope, receive, send)

        except Exception:
            # On any unexpected error, close the socket as unauthorized
            await send({"type": "websocket.close", "code": 4401})
            return
