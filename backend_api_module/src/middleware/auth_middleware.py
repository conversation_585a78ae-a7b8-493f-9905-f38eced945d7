"""Authentication Middleware for Firebase Token Verification."""

import logging
import time
from functools import wraps
from typing import Any, Callable, Dict, Optional, Tuple

from fastapi import HTTPException, Request, status
from fastapi.security import HTT<PERSON><PERSON>earer

from ..datastore import check_user_exists_simple, get_user_id_by_auth_uid_simple

# Import Firebase service and database functions
from ..services.firebase_service import verify_firebase_token

# Configure logging
logger = logging.getLogger(__name__)

# Security scheme for extracting Bearer tokens
security = HTTPBearer()

# Rate limiting storage (in production, use Redis)
auth_attempts: Dict[str, Tuple[int, float]] = {}
RATE_LIMIT_WINDOW = 300  # 5 minutes
RATE_LIMIT_MAX_ATTEMPTS = 10


def rate_limit_check(firebase_uid: str) -> bool:
    """
    Check if user has exceeded authentication rate limit.

    Args:
        firebase_uid: Firebase user UID

    Returns:
        True if within rate limit, False if exceeded
    """
    current_time = time.time()

    # Clean up old entries
    expired_keys = [
        key for key, (count, timestamp) in auth_attempts.items() if current_time - timestamp > RATE_LIMIT_WINDOW
    ]
    for key in expired_keys:
        del auth_attempts[key]

    # Check current user's attempts
    if firebase_uid in auth_attempts:
        count, first_attempt = auth_attempts[firebase_uid]
        if current_time - first_attempt < RATE_LIMIT_WINDOW:
            if count >= RATE_LIMIT_MAX_ATTEMPTS:
                logger.warning(f"⚠️  Rate limit exceeded for user: {firebase_uid}")
                return False
            # Increment attempt count
            auth_attempts[firebase_uid] = (count + 1, first_attempt)
        else:
            # Reset counter for new window
            auth_attempts[firebase_uid] = (1, current_time)
    else:
        # First attempt
        auth_attempts[firebase_uid] = (1, current_time)

    return True


async def extract_firebase_token(request: Request) -> Optional[str]:
    """
    Extract Firebase token from request headers.

    Args:
        request: FastAPI request object

    Returns:
        Firebase ID token or None
    """
    try:
        # Try Authorization header first
        authorization = request.headers.get("Authorization")
        if authorization and authorization.startswith("Bearer "):
            return authorization.split(" ", 1)[1]

        # Try custom Firebase-Token header
        firebase_token = request.headers.get("Firebase-Token")
        if firebase_token:
            return firebase_token

        # Try X-Firebase-Token header
        x_firebase_token = request.headers.get("X-Firebase-Token")
        if x_firebase_token:
            return x_firebase_token

        return None

    except Exception as e:
        logger.exception(f"❌ Error extracting Firebase token: {e}")
        return None


async def verify_and_get_user_info(firebase_token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase token and get user information.

    Args:
        firebase_token: Firebase ID token

    Returns:
        User information with session mapping or None
    """
    try:
        # Verify Firebase token
        token_claims = verify_firebase_token(firebase_token)
        if not token_claims:
            logger.warning("⚠️  Invalid Firebase token")
            return None

        firebase_uid = token_claims["firebase_uid"]
        phone_number = token_claims.get("phone_number")

        if not phone_number:
            logger.warning(f"⚠️  Phone number not found in token for user: {firebase_uid}")
            return None

        # Rate limiting check
        if not rate_limit_check(firebase_uid):
            logger.warning(f"⚠️  Rate limit exceeded for Firebase UID: {firebase_uid}")
            return None

        # Check if user exists in our database
        user_exists = check_user_exists_simple(phone_number, firebase_uid)
        if not user_exists:
            logger.warning(f"⚠️  User not found in database: {firebase_uid}")
            return None

        # Get session user_id mapping
        session_user_id = get_user_id_by_auth_uid_simple(phone_number, firebase_uid)

        # Return combined user information
        user_info = {
            "firebase_uid": firebase_uid,
            "user_id": session_user_id,  # This is the session ID used in chat_history
            "email": token_claims.get("email"),
            "phone_number": token_claims.get("phone_number"),
            "email_verified": token_claims.get("email_verified", False),
            "auth_time": token_claims.get("auth_time"),
            "token_exp": token_claims.get("exp"),
            "token_iat": token_claims.get("iat"),
        }

        logger.debug(f"✅ User authenticated: {firebase_uid} -> session: {session_user_id}")
        return user_info

    except Exception as e:
        logger.exception(f"❌ Error verifying user: {e}")
        return None


def firebase_auth_required(func: Callable) -> Callable:
    """
    Require Firebase authentication for route handlers.

    Usage:
        @router.get("/protected")
        @firebase_auth_required
        async def protected_route(request: Request):
            # Access user info via request.state.user
            user_info = request.state.user
            return {"message": f"Hello {user_info['firebase_uid']}"}
    """

    @wraps(func)
    async def wrapper(*args: Any, **kwargs: Any) -> Any:
        # Find the request object in args and kwargs
        request = None

        # Check args first
        for arg in args:
            if isinstance(arg, Request):
                request = arg
                break

        # If not found in args, check kwargs
        if not request:
            for value in kwargs.values():
                if isinstance(value, Request):
                    request = value
                    break

        # If still not found, try to get from function signature
        if not request:
            import inspect

            sig = inspect.signature(func)
            for param_name, param in sig.parameters.items():
                if param.annotation == Request:
                    # Try to find the parameter value
                    if param_name in kwargs:
                        request = kwargs[param_name]
                        break
                    elif param.default == param.empty:  # Required parameter
                        # This is a required Request parameter, we need to find it
                        for arg in args:
                            if isinstance(arg, Request):
                                request = arg
                                break
                        if request:
                            break

        if not request:
            logger.exception("❌ Request object not found in decorated function")
            logger.info(f"Function: {func.__name__}")
            logger.info(f"Args: {args}")
            logger.info(f"Kwargs: {kwargs}")
            logger.info(f"Function signature: {inspect.signature(func)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Internal server error",
            )
        else:
            logger.debug(f"✅ Request object found for function: {func.__name__}")

        # If session middleware already populated user, trust it
        if getattr(request.state, "user", None):
            return await func(*args, **kwargs)

        # Otherwise, require an explicit Firebase ID token (header-based)
        # TEST CODE - TO TRIGGER FORBIDDEN ERROR
        # raise HTTPException(
        #         status_code=status.HTTP_403_FORBIDDEN,
        #         detail="Invalid or expired Firebase token",
        #     )

        # Extract and verify Firebase token
        firebase_token = await extract_firebase_token(request)
        if not firebase_token:
            logger.warning("⚠️  No Firebase token provided and no valid session cookie present")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authentication required",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Verify token and get user info
        user_info = await verify_and_get_user_info(firebase_token)
        if not user_info:
            logger.warning("⚠️  Firebase authentication failed")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Invalid or expired Firebase token",
            )

        # Store user info in request state
        request.state.user = user_info

        # Call the original function
        return await func(*args, **kwargs)

    return wrapper


async def verify_firebase_token_websocket(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase token for WebSocket connections.

    Args:
        token: Firebase ID token

    Returns:
        User information or None if invalid
    """
    try:
        user_info = await verify_and_get_user_info(token)
        if user_info:
            logger.info(f"✅ WebSocket authentication successful: {user_info['firebase_uid']}")
            return user_info

        logger.warning("⚠️  WebSocket authentication failed")
        return user_info

    except Exception as e:
        logger.exception(f"❌ WebSocket token verification error: {e}")
        return None
