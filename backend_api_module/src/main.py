"""Main FastAPI application."""

from contextlib import asynccontextmanager
from typing import AsyncIterator

import structlog
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Ensure environment variables for CouchDB are set
from src.env_config import setup_real_db_environment

from .api.auth_routes import router as auth_router
from .api.routes import router
from .middleware.session_middleware import SessionCookieMiddleware
from .middleware.websocket_session_auth import WebSocketSessionAuthMiddleware
from .utils.config import get_settings

setup_real_db_environment()

# Initialize Firebase service during startup
print("🔍 About to import firebase_service from .services.firebase_service")
try:
    from .services.firebase_service import (
        firebase_auth_service,
        verify_firebase_configuration,
    )

    print(f"✅ Import successful - firebase_auth_service: {type(firebase_auth_service)}")
    print(f"✅ firebase_auth_service._app: {firebase_auth_service._app}")
    print(f"🔥 Firebase service initialized: {firebase_auth_service._app is not None}")

    # Verify Firebase configuration
    if verify_firebase_configuration():
        print("✅ Firebase configuration verified successfully")
    else:
        print("❌ Firebase configuration verification failed")
        raise Exception("Firebase configuration is invalid")

except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback

    print(f"❌ Import traceback: {traceback.format_exc()}")
    raise

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer(),
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger(__name__)

# Get settings
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncIterator:
    """Lifespan context manager for FastAPI application."""
    # Startup
    logger.info(
        "Starting backend API module",
        version=settings.app_version,
        environment=settings.app_env,
    )

    yield

    # Shutdown
    logger.info("Shutting down backend API module")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Backend API module for Smart Agent trading platform",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan,
)

# Session cookie middleware (verifies Firebase session cookies)
app.add_middleware(SessionCookieMiddleware)
app.add_middleware(WebSocketSessionAuthMiddleware)

# Add CORS middleware with specific frontend origin
app.add_middleware(
    CORSMiddleware,
    allow_origins=(
        ["https://staging.aagman.ai", "https://app.aagman.ai", "https://aagman.ai"]
        if settings.app_env == "production"
        else [
            "http://localhost:3000",  # Vite dev server
            "http://localhost:5173",  # Vite dev server alternative
            "http://127.0.0.1:3000",
            "http://127.0.0.1:5173",
            "http://localhost:8000",  # Backend server
            "http://127.0.0.1:8000",
        ]
    ),
    allow_origin_regex=r"chrome-extension://.*",
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router)
app.include_router(auth_router)


@app.get("/health")
async def health_check() -> JSONResponse:
    """Health check endpoint."""
    return JSONResponse(
        content={
            "status": "healthy",
            "service": "backend-api-module",
            "version": "1.0.0",
        },
        status_code=200,
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
    )
