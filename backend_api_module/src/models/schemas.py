"""Pydantic schemas for API models."""

from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ConversationType(str, Enum):
    """Types of conversations supported by the system."""

    CHAT = "chat"
    ORDERS = "orders"
    MONITORING = "monitoring"


class MessageType(str, Enum):
    """Types of messages in conversations."""

    CHAT = "chat"
    ORDERS = "orders"
    MONITORING = "monitoring"


class SenderType(str, Enum):
    """Types of message senders."""

    USER = "user"
    SYSTEM = "system"


class MonitoringInstanceType(str, Enum):
    """Types of monitoring instances."""

    HOLDING_DAYS_PNL = "holding_days_pnl"
    TOTAL_PNL = "total_pnl"
    STOCK_PNL = "stock_pnl"
    STOCK_LAST_TRADE = "stock_last_trade"
    NIFTY_LEVELS = "nifty_levels"
    SENSEX_LEVELS = "sensex_levels"
    PRICE_ALERT = "PRICE_ALERT"


class PaginationParams(BaseModel):
    """Pagination parameters for API responses."""

    page: int = Field(1, ge=1, description="Page number (1-based)")
    page_size: int = Field(50, ge=1, le=100, description="Number of items per page")


class PaginatedResponse(BaseModel):
    """Generic paginated response wrapper."""

    items: List[Any] = Field(..., description="List of items")
    total: int = Field(..., description="Total number of items")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")
    total_pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")


class ChatHistoryRequest(BaseModel):
    """Request model for creating a new chat history session."""

    user_id: UUID = Field(..., description="Unique identifier for the user")
    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    conversation_type: ConversationType = Field(..., description="Type of conversation")
    broker_name: str = Field(..., description="Name of the broker")


class ChatHistoryResponse(BaseModel):
    """Response model for chat history session."""

    user_id: UUID = Field(..., description="Unique identifier for the user")
    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    conversation_type: ConversationType = Field(..., description="Type of conversation")
    broker_name: str = Field(..., description="Name of the broker")
    history: List[Dict[str, Any]] = Field(default_factory=list, description="Chat history")


class ChatMessageResponse(BaseModel):
    """Response model for individual chat messages."""

    message_id: UUID = Field(..., description="Unique identifier for the message")
    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    user_id: UUID = Field(..., description="Unique identifier for the user")
    message: str = Field(..., description="Message content")
    role: str = Field(..., description="Message role (user/system)")
    model_version: Optional[str] = Field(None, description="LLM model version")
    actions: Optional[Dict[str, Any]] = Field(None, description="Actions data")
    timestamp: str = Field(..., description="Message timestamp")


class ChatHistoryPaginatedResponse(PaginatedResponse):
    """Paginated response for chat history."""

    items: List[ChatMessageResponse] = Field(..., description="List of chat messages")
    conversation_id: UUID = Field(..., description="Conversation ID")
    conversation_type: ConversationType = Field(..., description="Type of conversation")
    broker_name: str = Field(..., description="Name of the broker")


class ConversationResponse(BaseModel):
    """Response model for conversation summary."""

    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    user_id: UUID = Field(..., description="Unique identifier for the user")
    conversation_type: ConversationType = Field(..., description="Type of conversation")
    broker_name: str = Field(..., description="Name of the broker")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    message_count: int = Field(0, description="Number of messages in conversation")
    last_message: Optional[str] = Field(None, description="Last message content")
    last_message_timestamp: Optional[str] = Field(None, description="Last message timestamp")


class ConversationsPaginatedResponse(PaginatedResponse):
    """Paginated response for user conversations."""

    items: List[ConversationResponse] = Field(..., description="List of conversations")


class ChatMessage(BaseModel):
    """Model for individual chat messages."""

    user_id: UUID = Field(..., description="Unique identifier for the user")
    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    broker_name: str = Field(..., description="Name of the broker")
    message: str = Field(..., description="Message content")
    type_of_message: MessageType = Field(..., description="Type of message")
    model_id: Optional[UUID] = Field(None, description="LLM model identifier")
    sender: SenderType = Field(..., description="Sender type")


class ChatResponse(BaseModel):
    """Response model for chat messages."""

    message: str = Field(..., description="Response message content")
    sender: SenderType = Field(..., description="Sender type")
    actions: List[Dict[str, Any]] = Field(default_factory=list, description="Actions to be performed")


class WebSocketMessage(BaseModel):
    """Model for WebSocket messages."""

    user_id: UUID = Field(..., description="Unique identifier for the user")
    conversation_id: UUID = Field(..., description="Unique identifier for the conversation")
    broker_name: str = Field(..., description="Name of the broker")
    message: str = Field(..., description="Message content")
    type_of_message: MessageType = Field(..., description="Type of message")
    model_id: Optional[UUID] = Field(None, description="LLM model identifier")
    sender: SenderType = Field(..., description="Sender type")


class OrderResponse(BaseModel):
    """Response model for order data."""

    order_id: UUID = Field(..., description="Unique identifier for the order")
    user_id: UUID = Field(..., description="Unique identifier for the user")
    broker_name: str = Field(..., description="Name of the broker")
    symbol: str = Field(..., description="Trading symbol")
    order_type: str = Field(..., description="Type of order")
    transaction_type: str = Field(..., description="BUY or SELL")
    quantity: int = Field(..., description="Number of shares/units")
    price: Optional[float] = Field(None, description="Limit price")
    status: str = Field(..., description="Order status")
    created_at: str = Field(..., description="Order creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")


class MonitoringInstance(BaseModel):
    """Model for monitoring instances."""

    instance_id: UUID = Field(..., description="Unique identifier for the monitoring instance")
    user_id: UUID = Field(..., description="Unique identifier for the user")
    broker_name: str = Field(..., description="Name of the broker")
    instance_type: MonitoringInstanceType = Field(..., description="Type of monitoring")
    status: str = Field(..., description="Instance status")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="Monitoring parameters")
    created_at: str = Field(..., description="Creation timestamp")
    updated_at: str = Field(..., description="Last update timestamp")
    started_at: Optional[str] = Field(None, description="Start timestamp")
    ended_at: Optional[str] = Field(None, description="End timestamp")


class NotificationCreate(BaseModel):
    """Request model for creating notifications."""

    user_id: UUID = Field(..., description="Unique identifier for the user")
    title: str = Field(..., description="Notification title")
    description: str = Field(..., description="Notification description")
    actions: List[Dict[str, Any]] = Field(..., description="Actions to be performed")


class NotificationResponse(BaseModel):
    """Response model for notifications."""

    notification_id: UUID = Field(..., description="Unique identifier for the notification")
    user_id: UUID = Field(..., description="Unique identifier for the user")
    title: str = Field(..., description="Notification title")
    description: str = Field(..., description="Notification description")
    actions: List[Dict[str, Any]] = Field(default_factory=list, description="Actions to be performed")
    status: str = Field(..., description="Notification status")
    created_at: str = Field(..., description="Creation timestamp")
    read_at: Optional[str] = Field(None, description="Read timestamp")


class NotificationUpdate(BaseModel):
    """Request model for updating notifications."""

    status: str = Field(..., description="New notification status")


# New schemas for updated chat history functionality
class BrokerName(str, Enum):
    """Supported broker names."""

    ZERODHA = "zerodha"
    GROWW = "groww"
    UPSTOX = "upstox"


class ChatHistoryMessageType(str, Enum):
    """Types of chat history messages."""

    ORDER_CONFIRMATION = "order_confirmation"
    ORDER_EXECUTION = "order_execution"
    MONITOR_ORDER = "monitor_order"
    ORDER_PLANNING = "order_planning"


class ChatHistoryAction(BaseModel):
    """Action model for chat history responses."""

    description: str = Field(..., description="Button label (e.g., 'View All Orders')")
    type: ConversationType = Field(..., description="Action type")
    message: str = Field(..., description="Payload/command to send back to the backend")


class ChatHistoryMessage(BaseModel):
    """Individual chat history message model."""

    textMessage: str = Field(..., description="The system's textual response to display")
    messageType: ChatHistoryMessageType = Field(..., description="Type of message")
    primitives: List[Any] = Field(default_factory=list, description="Additional data primitives")
    sender: str = Field(..., description="Message sender")
    actions: List[ChatHistoryAction] = Field(default_factory=list, description="Available actions")
    execution_request_id: Optional[str] = Field(None, description="Execution request id for correlating live updates")


class NewChatHistoryRequest(BaseModel):
    """Request model for getting chat history."""

    user_id: str = Field(..., description="User UUID string")
    conversation_id: str = Field(..., description="Conversation UUID string")
    type: ConversationType = Field(..., description="Chat type")
    brokerName: BrokerName = Field(..., description="Broker name")


class NewChatHistoryResponse(BaseModel):
    """Response model for chat history."""

    user_id: str = Field(..., description="User UUID")
    conversation_id: str = Field(..., description="Conversation UUID")
    type: ConversationType = Field(..., description="Chat type")
    brokerName: BrokerName = Field(..., description="Broker name")
    history: List[ChatHistoryMessage] = Field(default_factory=list, description="List of chat messages")


# New schemas for orders endpoint
class OrderStatus(str, Enum):
    """Order status values."""

    PENDING = "pending"
    EXECUTED = "executed"
    CANCELLED = "cancelled"


class OrdersRequest(BaseModel):
    """Request model for getting orders."""

    user_id: str = Field(..., description="User ID")
    broker: Optional[BrokerName] = Field(None, description="Broker name filter (optional)")
    status: Optional[OrderStatus] = Field(None, description="Order status filter (optional)")


class OrderItem(BaseModel):
    """Individual order item model."""

    order_id: str = Field(..., description="Unique order ID")
    broker: str = Field(..., description="Broker name")
    symbol: str = Field(..., description="Trading symbol")
    quantity: int = Field(..., description="Order quantity")
    price: float = Field(..., description="Order price")
    status: str = Field(..., description="Order status")
    timestamp: str = Field(..., description="Order timestamp")


class OrdersResponse(BaseModel):
    """Response model for orders."""

    orders: List[OrderItem] = Field(default_factory=list, description="List of orders")


# New schemas for monitoring instances endpoint
class MonitoringInstancesRequest(BaseModel):
    """Request model for getting monitoring instances."""

    user_id: str = Field(..., description="User ID")


class MonitoringInstanceItem(BaseModel):
    """Individual monitoring instance item model."""

    instance_id: str = Field(..., description="Unique instance ID")
    broker: str = Field(..., description="Broker name")
    name: str = Field(..., description="Monitoring instance name")
    target: str = Field(..., description="Monitoring target condition")
    status: str = Field(..., description="Instance status")
    created_at: str = Field(..., description="Creation timestamp")


class MonitoringInstancesResponse(BaseModel):
    """Response model for monitoring instances."""

    monitoring_instances: List[MonitoringInstanceItem] = Field(
        default_factory=list, description="List of monitoring instances"
    )


# New WebSocket Chat Schemas
class WebSocketMessageType(str, Enum):
    """Types of WebSocket messages."""

    CHAT = "chat"
    ORDERS = "orders"
    MONITORING = "monitoring"


class WebSocketResponseMessageType(str, Enum):
    """Types of WebSocket response messages."""

    ORDER_CONFIRMATION = "order_confirmation"
    ORDER_EXECUTION = "order_execution"
    MONITOR_ORDER = "monitor_order"
    ORDER_PLANNING = "order_planning"


class WebSocketAction(BaseModel):
    """Action model for WebSocket responses."""

    description: str = Field(..., description="Button label (e.g., 'View All Orders')")
    type: WebSocketMessageType = Field(..., description="Action type")
    message: str = Field(..., description="Payload/command to send back to the backend")


class WebSocketChatRequest(BaseModel):
    """WebSocket chat request model."""

    user_id: str = Field("", description="Session ID (leave empty for new chat session)")
    conversation_id: str = Field(..., description="Conversation ID")
    brokerName: BrokerName = Field(..., description="Broker name")
    message: str = Field(..., description="User's text prompt")
    typeOfMessage: WebSocketMessageType = Field(..., description="Message type")
    modelId: str = Field(..., description="Model identifier string")
    sender: str = Field(..., description="Message sender (must be 'user')")


class WebSocketChatResponse(BaseModel):
    """WebSocket chat response model."""

    textMessage: str = Field(..., description="System's textual response to display")
    messageType: WebSocketResponseMessageType = Field(..., description="Response message type")
    primitives: List[Any] = Field(default_factory=list, description="Additional data primitives")
    sender: str = Field(default="system", description="Response sender")
    actions: List[WebSocketAction] = Field(default_factory=list, description="Available actions")
    user_id: str = Field(..., description="User ID for frontend reference")
    conversation_id: str = Field(..., description="Conversation ID for frontend reference")
    # Metadata fields unpacked at root level for direct access
    typeOfMessage: Optional[str] = Field(None, description="Type of message from request context")
    # Add the missing group fields
    group_human_friendly_explanation: Optional[str] = Field(None, description="Group-level human-friendly explanation")
    group_clarification_message: Optional[str] = Field(None, description="Group-level clarification message")
    # Backend-generated execution id for correlating execution docs
    execution_request_id: Optional[str] = Field(None, description="Execution request id for correlating orders/alerts")
