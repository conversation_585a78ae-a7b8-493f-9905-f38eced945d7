"""Configuration management for the backend API module."""

import os
from typing import Optional

from pydantic import BaseModel, Field


class Settings(BaseModel):
    """Application settings."""

    # Environment settings
    app_env: str = Field(default="local", description="Application environment (local/production)")

    # Application settings
    app_name: str = Field(default="Smart Agent Backend API", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")

    # Server settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")

    # JWT settings (for local development)
    jwt_secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(default=30, description="JWT access token expiry")

    # Firebase settings (for production)
    google_application_credentials: Optional[str] = Field(
        default=None, description="Path to Google Application Credentials file"
    )

    # Session cookie settings
    session_cookie_name: str = Field(default="sa_session", description="HTTP-only session cookie name")
    session_cookie_domain: Optional[str] = Field(default=None, description="Cookie domain")
    session_cookie_path: str = Field(default="/", description="Cookie path")
    session_cookie_secure: bool = Field(default=False, description="Send cookie with Secure flag")
    session_cookie_samesite: str = Field(default="none", description="SameSite policy: lax|strict|none")
    session_cookie_max_age_days: int = Field(default=14, description="Session cookie lifetime in days")
    session_cookie_renewal_threshold_hours: int = Field(
        default=24, description="Threshold to signal/perform renewal when within this many hours of expiry"
    )

    # WebSocket settings
    websocket_ping_interval: int = Field(default=20, description="WebSocket ping interval")
    websocket_ping_timeout: int = Field(default=20, description="WebSocket ping timeout")

    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format")

    class Config:
        """Pydantic configuration."""

        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings(
    app_env=os.getenv("APP_ENV", "local"),
    jwt_secret_key=os.getenv("JWT_SECRET_KEY", "your-secret-key-change-in-production"),
    google_application_credentials=os.getenv("GOOGLE_APPLICATION_CREDENTIALS"),
    session_cookie_name=os.getenv("SESSION_COOKIE_NAME", "sa_session"),
    session_cookie_domain=os.getenv("SESSION_COOKIE_DOMAIN") or None,
    session_cookie_path=os.getenv("SESSION_COOKIE_PATH", "/"),
    session_cookie_secure=os.getenv("SESSION_COOKIE_SECURE", "false").lower() == "true",
    session_cookie_samesite=os.getenv("SESSION_COOKIE_SAMESITE", "none").lower(),
    session_cookie_max_age_days=int(os.getenv("SESSION_COOKIE_MAX_AGE_DAYS", "14")),
    session_cookie_renewal_threshold_hours=int(os.getenv("SESSION_COOKIE_RENEWAL_THRESHOLD_HOURS", "24")),
)


def get_settings() -> Settings:
    """Get application settings."""
    return settings
