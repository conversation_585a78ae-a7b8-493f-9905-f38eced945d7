# mypy: disable-error-code="unreachable"
"""Firebase Authentication Service for Backend."""

import json
import logging
import os
from typing import Any, Dict, Optional

import firebase_admin
from firebase_admin import auth, credentials
from firebase_admin.exceptions import FirebaseError

# Configure logging
logger = logging.getLogger(__name__)

# Module import debug
logger.info("🔥 Firebase service module imported!")


class FirebaseAuthService:
    """Firebase authentication service for backend token verification."""

    def __init__(self) -> None:
        """Initialize Firebase Admin SDK."""
        logger.info("🚀 FirebaseAuthService constructor called!")
        self._app = None

        # Debug environment variables before initialization
        logger.info(f"🔍 FIREBASE_SERVICE_ACCOUNT_PATH: {os.getenv('FIREBASE_SERVICE_ACCOUNT_PATH', 'NOT_SET')}")
        logger.info(f"🔍 GOOGLE_CLOUD_PROJECT: {os.getenv('GOOGLE_CLOUD_PROJECT', 'NOT_SET')}")
        logger.info(f"🔍 GOOGLE_APPLICATION_CREDENTIALS: {os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'NOT_SET')}")

        logger.info("🔧 About to call _initialize_firebase...")
        self._initialize_firebase()
        logger.info("✅ _initialize_firebase completed!")
        logger.info(f"🔍 Final _app value: {self._app}")

        # Verify initialization
        if self._app:
            logger.info("✅ Firebase app initialized successfully")
            logger.info(f"🔍 App name: {getattr(self._app, 'name', 'NOT_FOUND')}")
            logger.info(f"🔍 App project_id: {getattr(self._app, 'project_id', 'NOT_FOUND')}")
            logger.info(f"🔍 App credential type: {type(self._app.credential)}")
        else:
            logger.exception("❌ Firebase app initialization failed")

    def _initialize_firebase(self) -> None:
        """Initialize Firebase Admin SDK with service account credentials."""
        try:
            logger.info("🔧 Starting Firebase initialization...")
            logger.info(
                f"🔍 Current firebase_admin._apps: "
                f"{list(firebase_admin._apps.keys()) if firebase_admin._apps else 'Empty'}"
            )

            # Check if Firebase is already initialized
            if firebase_admin._apps:
                self._app = firebase_admin.get_app()
                logger.info("✅ Firebase Admin SDK already initialized")
                logger.info(f"🔍 Existing app: {self._app}")
                logger.info(f"🔍 App project_id: {getattr(self._app, 'project_id', 'NOT_FOUND')}")
                # Ensure we have the app instance AND proper credentials
                if self._app and hasattr(self._app, "project_id") and self._app.project_id:
                    logger.info(f"✅ Using existing Firebase app with project_id: {self._app.project_id}")
                    return

                logger.warning("🔄 Existing Firebase app lacks proper project_id, reinitializing...")
                # Delete existing apps to reinitialize properly
                firebase_admin.delete_app(self._app)
                self._app = None
                logger.info("🗑️ Deleted existing Firebase app")

            # Try to get service account from environment variable
            service_account_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_PATH")
            service_account_json = os.getenv("FIREBASE_SERVICE_ACCOUNT_JSON")

            if service_account_path and os.path.exists(service_account_path):
                # Initialize from file
                with open(service_account_path, "r") as f:
                    service_account_info = json.load(f)

                # Get project ID from service account
                project_id = service_account_info.get("project_id")
                if not project_id:
                    raise ValueError("Service account JSON missing project_id")

                # Explicitly set environment variables
                os.environ["GOOGLE_CLOUD_PROJECT"] = project_id
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_path
                logger.info(f"✅ Set GOOGLE_CLOUD_PROJECT to: {project_id}")
                logger.info(f"✅ Set GOOGLE_APPLICATION_CREDENTIALS to: {service_account_path}")

                # Create credentials and initialize app with explicit project ID
                cred = credentials.Certificate(service_account_info)
                options = {"projectId": project_id, "credential": cred}

                # Delete any existing apps first
                for app_name in list(firebase_admin._apps.keys()):
                    try:
                        firebase_admin.delete_app(firebase_admin.get_app(app_name))
                        logger.info(f"🗑️ Deleted existing Firebase app: {app_name}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not delete app {app_name}: {e}")

                self._app = firebase_admin.initialize_app(cred, options)
                logger.info(f"✅ Firebase initialized from service account file: {service_account_path}")
                logger.info(f"✅ Firebase project ID: {project_id}")
                logger.info(f"🔍 Firebase app object: {self._app}")
                logger.info(f"🔍 Firebase app project_id attribute: {getattr(self._app, 'project_id', 'NOT_FOUND')}")
                logger.info(f"🔍 Firebase app name: {getattr(self._app, 'name', 'NOT_FOUND')}")

            elif service_account_json:
                # Initialize from JSON string
                service_account_info = json.loads(service_account_json)
                project_id = service_account_info.get("project_id")
                if not project_id:
                    raise ValueError("Service account JSON missing project_id")

                # Set environment variables
                os.environ["GOOGLE_CLOUD_PROJECT"] = project_id
                logger.info(f"✅ Set GOOGLE_CLOUD_PROJECT to: {project_id}")

                cred = credentials.Certificate(service_account_info)
                options = {"projectId": project_id, "credential": cred}

                # Delete any existing apps first
                for app_name in list(firebase_admin._apps.keys()):
                    try:
                        firebase_admin.delete_app(firebase_admin.get_app(app_name))
                        logger.info(f"🗑️ Deleted existing Firebase app: {app_name}")
                    except Exception as e:
                        logger.warning(f"⚠️ Could not delete app {app_name}: {e}")

                self._app = firebase_admin.initialize_app(cred, options)
                logger.info("✅ Firebase initialized from service account JSON")
                logger.info(f"✅ Firebase project ID: {project_id}")
                logger.info(f"🔍 Firebase app object: {self._app}")
                logger.info(f"🔍 Firebase app project_id attribute: {getattr(self._app, 'project_id', 'NOT_FOUND')}")

            else:
                # Try to initialize with default credentials (for Google Cloud environments)
                try:
                    self._app = firebase_admin.initialize_app()
                    logger.info("✅ Firebase initialized with default credentials")
                    logger.info(f"🔍 Firebase app object: {self._app}")
                    logger.info(f"🔍 Firebase app project_id attribute: {getattr(self._app, 'project_id', 'NOT_FOUND')}")
                except Exception as e:
                    logger.exception(f"❌ Failed to initialize Firebase with default credentials: {e}")
                    raise

        except Exception as e:
            logger.exception(f"❌ Firebase initialization failed: {e}")
            self._app = None
            raise

    def verify_token(self, id_token: str) -> Optional[Dict[str, Any]]:
        """
        Verify Firebase ID token and return decoded claims.

        Args:
            id_token: Firebase ID token from client

        Returns:
            Decoded token claims or None if invalid
        """
        print(f"🚨 DEBUG: verify_token method called with token: {id_token[:50]}...")
        print(f"🚨 DEBUG: self._app = {self._app}")
        print(f"🚨 DEBUG: self._app.name = {getattr(self._app, 'name', 'NONE')}")
        print(f"🚨 DEBUG: self._app.project_id = {getattr(self._app, 'project_id', 'NONE')}")
        try:
            # Ensure Firebase is initialized
            if not self._app:
                logger.exception("❌ Firebase Admin SDK not initialized")
                return None

            # Debug: Log Firebase app status
            logger.info(f"🔍 Firebase app initialized: {self._app is not None}")
            logger.info(f"🔍 Firebase app name: {getattr(self._app, 'name', 'None')}")
            logger.info(f"🔍 Firebase project ID: {getattr(self._app, 'project_id', 'None')}")

            # Verify the ID token
            print(f"🚨 DEBUG: About to call auth.verify_id_token with app={self._app.name}...")

            decoded_token = None
            try:
                # Debug: Check app credential type and project ID
                print(f"🚨 DEBUG: App credential type: {type(self._app.credential)}")
                print(f"🚨 DEBUG: App credential: {self._app.credential}")
                print(f"🚨 DEBUG: App project_id: {getattr(self._app, 'project_id', 'NOT_FOUND')}")
                print(f"🚨 DEBUG: GOOGLE_CLOUD_PROJECT env: {os.getenv('GOOGLE_CLOUD_PROJECT', 'NOT_SET')}")

                # Use the auth module with explicit app context
                decoded_token = auth.verify_id_token(id_token, app=self._app)
                print("🚨 DEBUG: auth.verify_id_token succeeded!")
            except Exception as verify_error:
                print(f"🚨 DEBUG: auth.verify_id_token failed with error: {verify_error}")
                print(f"🚨 DEBUG: Error type: {type(verify_error)}")

                # Try alternative approach - ensure environment variables are set
                print("🚨 DEBUG: Trying alternative approach with environment variable...")
                service_account_path = os.getenv("FIREBASE_SERVICE_ACCOUNT_PATH")
                if service_account_path and os.path.exists(service_account_path):
                    # Re-read service account and set environment variables
                    with open(service_account_path, "r") as f:
                        service_account_info = json.load(f)
                        project_id = service_account_info.get("project_id")
                        if project_id:
                            os.environ["GOOGLE_CLOUD_PROJECT"] = project_id
                            os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = service_account_path
                            print(f"🚨 DEBUG: Set GOOGLE_CLOUD_PROJECT to: {project_id}")
                            print(f"🚨 DEBUG: Set GOOGLE_APPLICATION_CREDENTIALS to: {service_account_path}")

                    try:
                        decoded_token = auth.verify_id_token(id_token)
                        print("🚨 DEBUG: Alternative approach succeeded!")
                    except Exception as alt_error:
                        print(f"🚨 DEBUG: Alternative approach also failed: {alt_error}")
                        raise verify_error
                else:
                    raise verify_error

            if decoded_token is None:
                logger.exception("❌ Failed to verify token")
                return None

            # Extract relevant information
            user_info = {
                "firebase_uid": decoded_token["uid"],
                "email": decoded_token.get("email"),
                "phone_number": decoded_token.get("phone_number"),
                "email_verified": decoded_token.get("email_verified", False),
                "auth_time": decoded_token.get("auth_time"),
                "exp": decoded_token.get("exp"),
                "iat": decoded_token.get("iat"),
                "iss": decoded_token.get("iss"),
                "aud": decoded_token.get("aud"),
            }

            logger.debug(f"✅ Token verified for user: {user_info['firebase_uid']}")
            return user_info

        except auth.ExpiredIdTokenError as e:
            logger.warning(f"⚠️  Expired ID token: {e}")
            return None
        except auth.InvalidIdTokenError as e:
            logger.warning(f"⚠️  Invalid ID token: {e}")
            return None
        except FirebaseError as e:
            logger.exception(f"❌ Firebase error verifying token: {e}")
            return None
        except Exception as e:
            logger.exception(f"❌ Unexpected error verifying token: {e}")
            return None

    def get_user_info(self, firebase_uid: str) -> Optional[Dict[str, Any]]:
        """
        Get user information from Firebase Auth by UID.

        Args:
            firebase_uid: Firebase user UID

        Returns:
            User information or None if not found
        """
        try:
            # Ensure Firebase is initialized
            if not self._app:
                logger.exception("❌ Firebase Admin SDK not initialized")
                return None

            user_record = auth.get_user(firebase_uid)

            user_info = {
                "firebase_uid": user_record.uid,
                "email": user_record.email,
                "phone_number": user_record.phone_number,
                "email_verified": user_record.email_verified,
                "disabled": user_record.disabled,
                "creation_timestamp": user_record.user_metadata.creation_timestamp,
                "last_sign_in_timestamp": user_record.user_metadata.last_sign_in_timestamp,
                "provider_data": [
                    {
                        "uid": provider.uid,
                        "email": provider.email,
                        "phone_number": provider.phone_number,
                        "provider_id": provider.provider_id,
                    }
                    for provider in user_record.provider_data
                ],
            }

            logger.debug(f"✅ Retrieved user info for: {firebase_uid}")
            return user_info

        except auth.UserNotFoundError:
            logger.warning(f"⚠️  User not found: {firebase_uid}")
            return None
        except FirebaseError as e:
            logger.exception(f"❌ Firebase error getting user info: {e}")
            return None
        except Exception as e:
            logger.exception(f"❌ Unexpected error getting user info: {e}")
            return None

    def revoke_refresh_tokens(self, firebase_uid: str) -> bool:
        """
        Revoke all refresh tokens for a user (force logout).

        Args:
            firebase_uid: Firebase user UID

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure Firebase is initialized
            if not self._app:
                logger.exception("❌ Firebase Admin SDK not initialized")
                return False

            auth.revoke_refresh_tokens(firebase_uid)
            logger.info(f"✅ Revoked refresh tokens for user: {firebase_uid}")
            return True

        except FirebaseError as e:
            logger.exception(f"❌ Firebase error revoking tokens: {e}")
            return False
        except Exception as e:
            logger.exception(f"❌ Unexpected error revoking tokens: {e}")
            return False

    def set_custom_claims(self, firebase_uid: str, custom_claims: Dict[str, Any]) -> bool:
        """
        Set custom claims for a user (for role-based access control).

        Args:
            firebase_uid: Firebase user UID
            custom_claims: Custom claims dictionary

        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure Firebase is initialized
            if not self._app:
                logger.exception("❌ Firebase Admin SDK not initialized")
                return False

            auth.set_custom_user_claims(firebase_uid, custom_claims)
            logger.info(f"✅ Set custom claims for user: {firebase_uid}")
            return True

        except FirebaseError as e:
            logger.exception(f"❌ Firebase error setting custom claims: {e}")
            return False
        except Exception as e:
            logger.exception(f"❌ Unexpected error setting custom claims: {e}")
            return False

    def is_token_valid(self, id_token: str) -> bool:
        """
        Quick check if token is valid (without full verification).

        Args:
            id_token: Firebase ID token

        Returns:
            True if valid, False otherwise
        """
        return self.verify_token(id_token) is not None

    def verify_configuration(self) -> bool:
        """
        Verify that Firebase is properly configured and can access the auth service.

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            if not self._app:
                logger.exception("❌ Firebase app not initialized")
                return False

            # Check if we can access the auth service
            project_id = getattr(self._app, "project_id", None)
            if not project_id:
                logger.exception("❌ Firebase app missing project_id")
                return False

            logger.info(f"✅ Firebase configuration verified - project_id: {project_id}")
            return True

        except Exception as e:
            logger.exception(f"❌ Firebase configuration verification failed: {e}")
            return False


# Global instance - lazy initialization
_firebase_auth_service_instance: Optional[FirebaseAuthService] = None


def get_firebase_auth_service() -> "FirebaseAuthService":
    """Get or create the Firebase auth service singleton."""
    global _firebase_auth_service_instance
    if _firebase_auth_service_instance is None:
        logger.info("📦 Creating FirebaseAuthService singleton...")
        try:
            _firebase_auth_service_instance = FirebaseAuthService()
            logger.info("✅ FirebaseAuthService singleton created successfully!")
            logger.info(f"🔍 Singleton _app status: {_firebase_auth_service_instance._app is not None}")
        except Exception as e:
            logger.exception(f"❌ Failed to create FirebaseAuthService singleton: {e}")
            import traceback

            logger.exception(f"❌ Singleton creation traceback: {traceback.format_exc()}")
            raise
    return _firebase_auth_service_instance


# For backward compatibility, create a property that uses lazy initialization
class FirebaseAuthServiceProxy:
    """Proxy lazy initialization of Firebase auth service."""

    def __getattr__(self, name: str) -> Any:
        """Delegate all attribute access to the actual service instance."""
        service = get_firebase_auth_service()
        return getattr(service, name)


# Create the proxy instance
firebase_auth_service = FirebaseAuthServiceProxy()


# Convenience functions
def verify_firebase_token(id_token: str) -> Optional[Dict[str, Any]]:
    """
    Verify Firebase token.

    Args:
        id_token: Firebase ID token

    Returns:
        Decoded token claims or None
    """
    print("🚨 DEBUG: verify_firebase_token ENTRY POINT!")
    service = get_firebase_auth_service()
    print(f"🚨 DEBUG: firebase_auth_service type: {type(service)}")
    print(f"🚨 DEBUG: firebase_auth_service._app: {service._app}")
    print("🚨 DEBUG: About to call firebase_auth_service.verify_token()...")

    try:
        result = service.verify_token(id_token)
        print(f"🚨 DEBUG: firebase_auth_service.verify_token returned: {result}")
        return result
    except Exception as e:
        print(f"🚨 DEBUG: Exception in verify_firebase_token: {e}")
        print(f"🚨 DEBUG: Exception type: {type(e)}")
        import traceback

        print(f"🚨 DEBUG: Traceback: {traceback.format_exc()}")
        raise


def get_firebase_user_info(firebase_uid: str) -> Optional[Dict[str, Any]]:
    """
    Get Firebase user info.

    Args:
        firebase_uid: Firebase user UID

    Returns:
        User information or None
    """
    service = get_firebase_auth_service()
    return service.get_user_info(firebase_uid)


def is_firebase_token_valid(id_token: str) -> bool:
    """
    Check token validity.

    Args:
        id_token: Firebase ID token

    Returns:
        True if valid, False otherwise
    """
    service = get_firebase_auth_service()
    return service.is_token_valid(id_token)


def verify_firebase_configuration() -> bool:
    """
    Verify Firebase configuration.

    Returns:
        True if configuration is valid, False otherwise
    """
    service = get_firebase_auth_service()
    return service.verify_configuration()
