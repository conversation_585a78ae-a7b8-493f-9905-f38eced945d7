#!/usr/bin/env python3
"""
Configure Environment Variables.

This module sets up environment variables for connecting to the real CouchDB
database by loading from the data_layer_v3 .env file.
"""

import os

from dotenv import load_dotenv


def setup_real_db_environment() -> None:
    """Set up environment variables for real database connections."""
    # Load .env from backend_api_module directory only
    backend_env_path = os.path.join(os.path.dirname(__file__), "..", ".env")

    if os.path.exists(backend_env_path):
        load_dotenv(backend_env_path)
        print(f"✓ Loaded environment variables from {backend_env_path}")
    else:
        print(f"⚠️  Environment file not found at {backend_env_path}")

    # Setup Firebase service account path if file exists
    backend_dir = os.path.join(os.path.dirname(__file__), "..")
    firebase_service_account_path = os.path.join(backend_dir, "firebase-service-account.json")

    if os.path.exists(firebase_service_account_path):
        # Convert to absolute path
        abs_firebase_path = os.path.abspath(firebase_service_account_path)
        os.environ["FIREBASE_SERVICE_ACCOUNT_PATH"] = abs_firebase_path
        print(f"✓ Set FIREBASE_SERVICE_ACCOUNT_PATH to {abs_firebase_path}")

        # Read project ID from service account file and set environment variables
        try:
            import json

            with open(abs_firebase_path, "r") as f:
                service_account_info = json.load(f)
                project_id = service_account_info.get("project_id")
                if project_id:
                    os.environ["GOOGLE_CLOUD_PROJECT"] = project_id
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = abs_firebase_path
                    print(f"✓ Set GOOGLE_CLOUD_PROJECT to {project_id}")
                    print(f"✓ Set GOOGLE_APPLICATION_CREDENTIALS to {abs_firebase_path}")
                else:
                    print("⚠️  Service account file missing project_id")
        except Exception as e:
            print(f"⚠️  Error reading service account file: {e}")
    else:
        print(f"⚠️  Firebase file not found at {firebase_service_account_path}")

    print("Environment variables loaded successfully.")

    # CouchDB Configuration - use environment variables or defaults
    # COUCHDB_PRIVATE_USERNAME and COUCHDB_PRIVATE_PASSWORD should be set in .env file
    if not os.getenv("COUCHDB_PRIVATE_HOST"):
        os.environ["COUCHDB_PRIVATE_HOST"] = "localhost"
    if not os.getenv("COUCHDB_PRIVATE_PORT"):
        os.environ["COUCHDB_PRIVATE_PORT"] = "5984"
    if not os.getenv("COUCHDB_PRIVATE_USE_SSL"):
        os.environ["COUCHDB_PRIVATE_USE_SSL"] = "false"
    if not os.getenv("COUCHDB_PUBLIC_HOST"):
        os.environ["COUCHDB_PUBLIC_HOST"] = "localhost"
    if not os.getenv("COUCHDB_PUBLIC_PORT"):
        os.environ["COUCHDB_PUBLIC_PORT"] = "5984"
    if not os.getenv("COUCHDB_PUBLIC_USE_SSL"):
        os.environ["COUCHDB_PUBLIC_USE_SSL"] = "false"

    # PouchDB Configuration
    os.environ["POUCHDB_NAME"] = "aagmanai_local"
    os.environ["POUCHDB_ADAPTER"] = "idb"

    # Testing Configuration

    if not os.getenv("MOCK_LLM_RESPONSES"):
        os.environ["MOCK_LLM_RESPONSES"] = "false"
    if not os.getenv("MOCK_LLM_RESPONSE_TYPE"):
        os.environ["MOCK_LLM_RESPONSE_TYPE"] = "orders"

    # Connection Settings
    os.environ["DB_TIMEOUT"] = "30"
    os.environ["DB_MAX_RETRIES"] = "3"
    os.environ["DB_RETRY_DELAY"] = "1.0"
    os.environ["DB_POOL_SIZE"] = "10"

    # Sync Settings
    os.environ["SYNC_ENABLED"] = "true"
    os.environ["SYNC_INTERVAL"] = "5000"
    os.environ["SYNC_LIVE"] = "true"
    os.environ["SYNC_RETRY"] = "true"

    # Schema Settings
    os.environ["SCHEMA_VALIDATION_ENABLED"] = "true"
    os.environ["AUTO_CREATE_DESIGN_DOCS"] = "true"


if __name__ == "__main__":
    setup_real_db_environment()
    print("✓ Real database environment configured")
    print(f"  CouchDB: {os.environ['COUCHDB_PRIVATE_HOST']}:{os.environ['COUCHDB_PRIVATE_PORT']}")
    print(f"  CouchDB Username: {os.environ.get('COUCHDB_PRIVATE_USERNAME', 'NOT SET')}")
    print(f"  Mock LLM Responses: {os.environ.get('MOCK_LLM_RESPONSES', 'false')}")
    if os.environ.get("MOCK_LLM_RESPONSES", "false") == "true":
        print(f"  Mock LLM Response Type: " f"{os.environ.get('MOCK_LLM_RESPONSE_TYPE', 'orders')}")
    print("  CouchDB-only configuration complete")
