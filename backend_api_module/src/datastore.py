"""CouchDB-compatible functions for Backend API Module.

This module provides database operations and management functions for the Backend API Module,
including user database creation, document operations, indexing, and CouchDB connection management.
It handles user-specific databases, authentication, and various data operations like chat history,
summaries, orders, and monitoring data.

This module uses only the couchdb3 library with clear separation between admin and user connections.
"""

import hashlib
import os
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union

import couchdb3
from couchdb3 import Database, Server


def get_admin_server() -> Server:
    """Get CouchDB server connection with admin credentials using private (localhost) connection."""
    use_ssl = os.getenv("COUCHDB_PRIVATE_USE_SSL", "false").lower() == "true"
    scheme = "https" if use_ssl else "http"
    host = os.getenv("COUCHDB_PRIVATE_HOST", "localhost")
    port = os.getenv("COUCHDB_PRIVATE_PORT", "5984")
    username = os.getenv("COUCHDB_PRIVATE_USERNAME")
    password = os.getenv("COUCHDB_PRIVATE_PASSWORD")

    if not username:
        raise ValueError("COUCHDB_PRIVATE_USERNAME must be set in environment variables")
    if not password:
        raise ValueError("COUCHDB_PRIVATE_PASSWORD must be set in environment variables")

    base_url = f"{scheme}://{host}:{port}/"
    return Server(base_url, user=username, password=password)


def get_user_server(phone_number: str) -> Server:
    """Get CouchDB server connection with user-specific credentials using private (localhost) connection."""
    # Get or create user database first to ensure it exists
    db_details = get_user_database(phone_number)

    use_ssl = os.getenv("COUCHDB_PRIVATE_USE_SSL", "false").lower() == "true"
    scheme = "https" if use_ssl else "http"
    host = os.getenv("COUCHDB_PRIVATE_HOST", "localhost")
    port = os.getenv("COUCHDB_PRIVATE_PORT", "5984")
    username = db_details["username"]
    password = db_details["password"]

    base_url = f"{scheme}://{host}:{port}/"
    return Server(base_url, user=username, password=password)


def get_user_database(phone_number: str) -> Dict[str, str]:
    """
    Get or create user-specific database and return connection details.

    Args:
        phone_number: User's phone number (used to generate database name)

    Returns:
        Dictionary containing database connection details
    """
    # Clean phone number for database naming
    clean_phone = phone_number.replace("+", "").replace("-", "").replace(" ", "")
    db_name = f"db_{clean_phone}"
    username = f"user_{clean_phone}"

    # Generate password using hashed phone number with salt
    salt = os.getenv("COUCHDB_USER_CRED_SALT", "")
    password_string = f"{clean_phone}{salt}"
    password = hashlib.sha256(password_string.encode()).hexdigest()[:16]

    # Get admin server connection
    admin_server = get_admin_server()

    # Check if database exists, create if it doesn't
    try:
        admin_server.get(db_name, check=True)
        print(f"Database {db_name} already exists")
    except couchdb3.exceptions.NotFoundError:
        # Database doesn't exist, create it
        print(f"Creating database: {db_name}")
        admin_server.create(db_name)
        # Generate indices after database creation
        generate_indices(phone_number)

    # Check if user exists, create if it doesn't
    try:
        # Try to get user from _users database
        users_db = admin_server["_users"]
        user_doc_id = f"org.couchdb.user:{username}"
        if user_doc_id in users_db:
            print(f"User {username} already exists")
        else:
            # User doesn't exist, create it
            print(f"Creating user: {username}")
            admin_server.save_user(username, password=password, roles=[])
    except Exception as e:
        print(f"Warning: Failed to check/create user: {e}")

    # Ensure user has access to the database by setting database security
    try:
        db = admin_server.get(db_name)
        security_doc = {
            "admins": {"names": [os.getenv("COUCHDB_PRIVATE_USERNAME")], "roles": []},
            "members": {"names": [username], "roles": []},
        }
        db.update_security(security_doc["admins"], security_doc["members"])
        print(f"Updated security for database {db_name}")
    except Exception as e:
        print(f"Warning: Failed to update database security: {e}")

    return {
        "database_name": db_name,
        "username": username,
        "password": password,
    }


def get_admin_database(phone_number: str) -> Database:
    """Get CouchDB database connection with admin credentials."""
    db_details = get_user_database(phone_number)
    db_name = db_details["database_name"]
    admin_server = get_admin_server()
    return admin_server.get(db_name, check=True)


def get_user_database_connection(phone_number: str) -> Database:
    """Get CouchDB database connection with user-specific credentials."""
    db_details = get_user_database(phone_number)
    db_name = db_details["database_name"]
    user_server = get_user_server(phone_number)
    return user_server.get(db_name, check=True)


def get_couchdb_url(phone_number: str) -> str:
    """
    Get CouchDB connection URL for client connections using public (FQDN) connection.

    Args:
        phone_number: User's phone number for user-specific credentials (if not admin)

    Returns:
        CouchDB connection URL for clients
    """
    use_ssl = os.getenv("COUCHDB_PUBLIC_USE_SSL", "false").lower() == "true"
    scheme = "https" if use_ssl else "http"

    # Use public (FQDN) host and port for client connections
    host = os.getenv("COUCHDB_PUBLIC_HOST", "localhost")
    port = os.getenv("COUCHDB_PUBLIC_PORT", "5984")

    # Return user-specific connection URL
    if not phone_number:
        raise ValueError("phone_number is required for user-specific credentials")

    db_details = get_user_database(phone_number)
    username = db_details["username"]
    password = db_details["password"]

    # URL encode username and password to handle special characters
    import urllib.parse

    encoded_username = urllib.parse.quote(username, safe="")
    encoded_password = urllib.parse.quote(password, safe="")

    # Build URL with proper encoding
    return f"{scheme}://{encoded_username}:{encoded_password}@{host}:{port}/"


def generate_indices(phone_number: str) -> None:
    """Generate indices for a specific user's database using admin connection."""
    db = get_admin_database(phone_number)
    create_index_if_not_exists(
        db,
        {"fields": ["user_id", "type", "conversation_id", "timestamp"]},
        "user_conversation_type_ts_idx",
    )
    create_index_if_not_exists(
        db,
        {"fields": ["user_id", "type", "broker_id", "status", "created_at"]},
        "order_result_idx",
    )
    create_index_if_not_exists(
        db,
        {"fields": ["user_id", "type", "status", "execution_time"]},
        "monitoring_alert_idx",
    )


def create_index_if_not_exists(db: Database, index_definition: Dict, name: str) -> None:
    """
    Create a CouchDB index if it doesn't already exist using the couchdb3 library.

    Args:
        db: The couchdb3 database object.
        index_definition: A dictionary defining the index.
        name: Name of the index.
    """
    try:
        # Get a list of existing indexes
        existing_indexes = db.indexes()

        # Check if an index with the same name already exists
        if "indexes" in existing_indexes:
            for index in existing_indexes["indexes"]:
                if index["name"] == name:
                    print(f"Index '{name}' already exists. 🤝")
                    return
    except couchdb3.exceptions.NotFoundError:
        # The database may be new and have no indexes yet
        pass

    # If the index doesn't exist, create it using put_index
    db.save_index(index_definition, name=name)
    print(f"Index '{name}' created successfully. ✨")


def save_chat_message(
    phone_number: str,
    user_id: str,
    conversation_id: str,
    role: str,
    message: str,
    llm_model_version: Optional[str] = None,
    meta_json: Dict[str, Any] = {},
    order_id: Optional[str] = None,
    type: str = "message",
    timestamp: Optional[datetime] = None,
) -> str:
    """
    Save a chat message to the conversation history.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        role: The role of the message sender (e.g., 'user', 'assistant', 'system')
        message: The message content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
        order_id: Message order in the conversation (optional)
        type: Message type (default: 'message')
        timestamp: Message timestamp (default: current time)

    Returns:
        str: The chat_id of the inserted message
    """
    timestamp_str = timestamp.isoformat() if isinstance(timestamp, datetime) else datetime.now(timezone.utc).isoformat()

    db = get_user_database_connection(phone_number)

    doc = {
        # Use UUIDv1 to ensure monotonically increasing IDs
        "_id": f"chat_{str(uuid.uuid1())}",
        "type": "chat_history",
        "user_id": user_id,
        "conversation_id": conversation_id,
        "timestamp": timestamp_str,
        "role": role,
        "message": message,
        "llm_model_version": llm_model_version,
        "meta_json": meta_json,
        "order_id": order_id,
        "message_type": type,
    }

    doc_id, status, _rev = db.save(doc)

    if not status:
        raise ValueError("Failed to save the chat message")

    return str(doc_id)


def get_chat_messages(phone_number: str, user_id: str, conversation_id: str) -> List[Dict[str, Any]]:
    """
    Extract chat history for a specific user and conversation.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier

    Returns:
        List[Dict[str, Any]]: List of chat history records as dictionaries

        Example: [
            {
                'chat_id': 'chat_005fe020-1942-4432-a620-7a6d1edf1607',
                'type': 'chat_history',
                'user_id': 'user-123',
                'conversation_id': 'conv-001',
                'timestamp': '2025-07-16T12:24:00.860668',
                'role': 'user',
                'message': 'Hello, how can I help you today?',
                'llm_model_version': None,
                'meta_json': {'tokens_used': 350, 'response_time': 3.2},
                'order_id': '69a6c75a-08dc-43f9-9d6a-27ba12fa7557',
                'message_type': 'monitoring'
            }
        ]
    """
    db = get_user_database_connection(phone_number)

    result = db.find(
        selector={"user_id": user_id, "conversation_id": conversation_id, "type": "chat_history"},
        limit=100,
        skip=0,
        sort=[{"timestamp": "desc"}],
        use_index="user_conversation_type_ts_idx",
    )
    # TODO: reverse the list
    result["docs"].reverse()
    if result["warning"]:
        print(f"Got warning: {result['warning']}")

    return [
        {
            **dict(doc),
            "chat_id": doc.get("_id"),
        }
        for doc in result["docs"]
    ]


def save_summary(
    phone_number: str,
    user_id: str,
    conversation_id: str,
    summary: str,
    llm_model_version: Optional[str] = None,
    meta_json: Dict[str, Any] = {},
    timestamp: Optional[datetime] = None,
) -> str:
    """
    Save a summary to the summary table.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier
        summary: The summary text content
        llm_model_version: The LLM model version used (optional)
        meta_json: Additional metadata as a dictionary (optional)
        timestamp: Summary timestamp (default: current time)

    Returns:
        str: The summary_id of the inserted summary, or None if failed
    """
    timestamp_str = timestamp.isoformat() if isinstance(timestamp, datetime) else datetime.now(timezone.utc).isoformat()

    db = get_user_database_connection(phone_number)

    doc = {
        # Use UUIDv1 to ensure monotonically increasing IDs
        "_id": f"summary_{str(uuid.uuid1())}",
        "type": "summary",
        "user_id": user_id,
        "conversation_id": conversation_id,
        "timestamp": timestamp_str,
        "summary": summary,
        "llm_model_version": llm_model_version,
        "meta_json": meta_json,
    }

    doc_id, status, _rev = db.save(doc)

    if not status:
        raise ValueError("Failed to save the summary")

    return str(doc_id)


def get_summaries(phone_number: str, user_id: str, conversation_id: str) -> List[Dict[str, Any]]:
    """
    Fetch summary records from the summary table by user_id and conversation_id.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        conversation_id: The conversation's unique identifier

    Returns:
        List[Dict[str, Any]]: List of summary records as dictionaries

        Example: [
            {
                '_id': 'summary_0d586242-e66c-4ec7-838f-c8826cd146c4',
                'type': 'summary',
                'user_id': 'user-123',
                'conversation_id': 'conv-001',
                'timestamp': '2025-07-18T13:51:45.937990',
                'summary': 'This is a test summary created by CouchDB test',
                'llm_model_version': 'gpt-4-test',
                'meta_json': {'test': True, 'created_by': 'couchdb_test'}
            }
        ]
    """
    db = get_user_database_connection(phone_number)

    result = db.find(
        selector={"user_id": user_id, "conversation_id": conversation_id, "type": "summary"},
        limit=100,
        skip=0,
        sort=[{"timestamp": "desc"}],
        use_index="user_conversation_type_ts_idx",
    )
    if result["warning"]:
        print(f"Got warning: {result['warning']}")

    return [
        {
            **doc,
        }
        for doc in result["docs"]
    ]


def fetch_orders_by_user(
    phone_number: str,
    user_id: str,
    broker_id: Optional[str] = None,
    status: Optional[str] = None,
) -> List[Dict[str, Any]]:
    """
    Fetch all orders for a specific user with optional filtering by broker_id and status.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        broker_id: Optional broker ID to filter by (if None, returns orders from all brokers)
        status: Optional order status to filter by (if None, returns orders with all statuses)

    Returns:
        List[Dict[str, Any]]: List of order records as dictionaries
    """
    db = get_user_database_connection(phone_number)

    selector = {"user_id": user_id, "type": "order_result"}
    if broker_id:
        selector["broker_id"] = broker_id
    if status:
        selector["status"] = status

    result = db.find(
        selector=selector,
        limit=100,
        skip=0,
        sort=[{"created_at": "desc"}],
        use_index="order_result_idx",
    )
    if result["warning"]:
        print(f"Got warning: {result['warning']}")

    return [{**doc} for doc in result["docs"]]


def fetch_monitoring_by_user(
    phone_number: str,
    user_id: str,
    status: Optional[str] = None,
    time_from: Optional[datetime] = None,
) -> List[Dict[str, Any]]:
    """
    Fetch all monitoring records for a specific user with optional filtering by status and time.

    Args:
        phone_number: User's phone number for database connection
        user_id: The user's unique identifier
        status: Optional status to filter by (if None, returns records with all statuses)
        time_from: Optional timestamp to filter from (if None, returns all records)

    Returns:
        List[Dict[str, Any]]: List of monitoring records as dictionaries
    """
    db = get_user_database_connection(phone_number)

    selector: Dict[str, Union[str, Dict]] = {"user_id": user_id, "type": "monitoring_alert"}
    if status:
        selector["status"] = status
    if time_from:
        selector["execution_time"] = {
            "$gte": time_from.isoformat() if isinstance(time_from, datetime) else str(time_from)
        }

    result = db.find(
        selector=selector,
        limit=100,
        skip=0,
        sort=[{"execution_time": "desc"}],
        use_index="monitoring_alert_idx",
    )
    if result["warning"]:
        print(f"Got warning: {result['warning']}")

    return [{**doc} for doc in result["docs"]]


def save_user_profile_simple(phone_number: str, firebase_uid: str, name: str, phone: Optional[str] = None) -> str:
    """
    Save user profile to CouchDB.

    Args:
        phone_number: User's phone number for database connection
        firebase_uid: Firebase user UID
        name: User's full name
        phone: User's phone number (optional)

    Returns:
        Document ID of saved user profile
    """
    db = get_user_database_connection(phone_number)

    # Create user profile document
    doc_id = f"user_profile_{firebase_uid}"
    user_doc = {
        "_id": doc_id,
        "type": "user_profile",
        "firebase_uid": firebase_uid,
        "name": name,
        "phone": phone,
        "created_at": datetime.now(timezone.utc).isoformat(),
        "updated_at": datetime.now(timezone.utc).isoformat(),
    }

    # Check if document already exists
    if doc_id in db:
        existing_doc = db[doc_id]
        user_doc["_rev"] = existing_doc["_rev"]
        user_doc["created_at"] = existing_doc.get("created_at", user_doc["created_at"])

    # Save document
    doc_id, _status, _rev = db.save(user_doc)

    if not _status:
        raise ValueError("Failed to save the user profile")

    return doc_id


def get_user_by_auth_uid_simple(phone_number: str, firebase_uid: str) -> Optional[Dict[str, Any]]:
    """
    Get user profile by Firebase UID.

    Args:
        phone_number: User's phone number for database connection
        firebase_uid: Firebase user UID

    Returns:
        User profile dictionary or None if not found
    """
    db = get_user_database_connection(phone_number)
    doc_id = f"user_profile_{firebase_uid}"

    if doc_id in db:
        return {**db[doc_id]}
    return None


def check_user_exists_simple(phone_number: str, firebase_uid: str) -> bool:
    """
    Check if user exists in the database.

    Args:
        phone_number: User's phone number for database connection
        firebase_uid: Firebase user UID

    Returns:
        True if user exists, False otherwise
    """
    db = get_user_database_connection(phone_number)
    doc_id = f"user_profile_{firebase_uid}"
    return doc_id in db


def get_user_id_by_auth_uid_simple(phone_number: str, firebase_uid: str) -> Optional[str]:
    """
    Get session user ID by Firebase UID.

    Args:
        phone_number: User's phone number for database connection
        firebase_uid: Firebase user UID

    Returns:
        Session user ID or None if not found
    """
    db = get_user_database_connection(phone_number)
    session_doc_id = f"user_session_{firebase_uid}"

    if session_doc_id in db:
        doc = db[session_doc_id]
        session_user_id = doc.get("session_user_id")
        return str(session_user_id) if session_user_id else None
    return None


def create_user_session_mapping_simple(phone_number: str, firebase_uid: str) -> str:
    """
    Create user session mapping for Firebase UID.

    Args:
        phone_number: User's phone number for database connection
        firebase_uid: Firebase user UID

    Returns:
        Session user ID
    """
    try:
        db = get_user_database_connection(phone_number)

        # Generate a session user ID
        session_user_id = str(uuid.uuid4())

        # Create session mapping document
        session_doc_id = f"user_session_{firebase_uid}"
        session_doc = {
            "_id": session_doc_id,
            "type": "user_session_mapping",
            "firebase_uid": firebase_uid,
            "session_user_id": session_user_id,
            "created_at": datetime.now(timezone.utc).isoformat(),
        }

        # Check if document already exists
        if session_doc_id in db:
            existing_doc = db[session_doc_id]
            existing_session_user_id = existing_doc.get("session_user_id")
            return str(existing_session_user_id) if existing_session_user_id else session_user_id

        # Save new session mapping
        _doc_id, _status, _rev = db.save(session_doc)

        if not _status:
            raise ValueError("Failed to create user session mapping")

        return session_user_id

    except Exception as e:
        print(f"Error creating user session mapping: {e}")
        raise


def create_chat_session_mapping(phone_number: str, session_user_id: str, firebase_uid: str) -> None:
    """Create a chat session document mapping session_user_id to firebase_uid.

    If the session doc already exists, this is a no-op.
    """
    try:
        db = get_user_database_connection(phone_number)
        doc_id = f"chat_session_{session_user_id}"
        if doc_id in db:
            return
        db.save(
            {
                "_id": doc_id,
                "type": "chat_session",
                "session_user_id": session_user_id,
                "firebase_uid": firebase_uid,
                "created_at": datetime.now(timezone.utc).isoformat(),
            }
        )
    except Exception as e:
        print(f"Error creating chat session mapping: {e}")
        raise


def check_session_belongs_to_uid(phone_number: str, session_user_id: str, firebase_uid: str) -> bool:
    """Check if a given session_user_id belongs to the specified firebase_uid."""
    try:
        db = get_user_database_connection(phone_number)
        doc_id = f"chat_session_{session_user_id}"
        if doc_id not in db:
            return False
        return True if db[doc_id].get("firebase_uid") == firebase_uid else False
    except Exception as e:
        print(f"Error checking chat session ownership: {e}")
        return False
