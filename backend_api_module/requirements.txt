# Backend API Module Dependencies - Pinned Versions for Faster Resolution
fastapi==0.116.1
uvicorn[standard]==0.35.0
websockets==12.0
pydantic==2.11.7
pydantic-settings==2.10.1
python-jose[cryptography]==3.5.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
httpx==0.27.2
requests==2.32.4

# Logging and Monitoring
structlog==23.2.0
firebase-admin==6.8.0

# DB Dependencies
couchdb3==2.0.2
packaging==24.2

# Text Processing and Fuzzy Matching
rapidfuzz==3.10.0

# Langchain and Langfuse Dependencies
python-dotenv==1.1.0
langfuse==3.2.1
PyYAML==6.0.1
langchain-community==0.3.27
langchain-openai==0.3.28
langchain-google-genai==2.1.9

# Web Search Dependencies (for MarketInsight functionality)
# Using OpenAI's native search capabilities instead of external tools

# Development Dependencies
black==23.12.1
isort==5.13.2
mypy==1.8.0
flake8==7.0.0
flake8-docstrings==1.7.0
pre-commit==3.6.0
pytest-timeout==2.2.0
psutil==5.9.8
types-requests==2.32.4.20250809
