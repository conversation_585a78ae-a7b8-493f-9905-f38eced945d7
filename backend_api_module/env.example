# Backend API Module Environment Configuration
# Copy this file to .env and update with your actual values

# Application Environment
APP_ENV=local  # Options: local, production

# Server Configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false
LOG_LEVEL=INFO

# JWT Configuration (for local development)
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# Firebase Configuration (for production)
# GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json

# LLM Configuration
# OpenAI API Key (required for OpenAI models like gpt-4, gpt-3.5-turbo)
OPENAI_API_KEY=your_openai_api_key_here

# Gemini API Key (optional, for Google Gemini models)
# GEMINI_API_KEY=your_gemini_api_key_here

# WebSocket Configuration
WEBSOCKET_PING_INTERVAL=20
WEBSOCKET_PING_TIMEOUT=20

# CouchDB Configuration
# Private (localhost) connection for server-side operations
COUCHDB_PRIVATE_HOST=localhost
COUCHDB_PRIVATE_PORT=5984
COUCHDB_PRIVATE_USE_SSL=false
COUCHDB_PRIVATE_USERNAME=your_couchdb_admin_username
COUCHDB_PRIVATE_PASSWORD=your_couchdb_admin_password

# Public (FQDN) connection for client-side operations
COUCHDB_PUBLIC_HOST=couchdb.yourdomain.com
COUCHDB_PUBLIC_PORT=5984
COUCHDB_PUBLIC_USE_SSL=true

# User CouchDB Credential Salt
COUCHDB_USER_CRED_SALT=smart_agent_couchdb_salt_2024

# Langfuse Configuration (for observability)
# Get these from https://cloud.langfuse.com
LANGFUSE_PUBLIC_KEY=your_langfuse_public_key_here
LANGFUSE_SECRET_KEY=your_langfuse_secret_key_here
LANGFUSE_HOST=https://cloud.langfuse.com

# Web Search Configuration (for MarketInsight functionality)
# Set to true to enable web search for MarketInsight primitives
ENABLE_WEB_SEARCH=false

# Session Cookie (Firebase Admin session cookies)
# Name of the HTTP-only cookie storing the Firebase session
# SESSION_COOKIE_NAME=sa_session
# Domain for the cookie (omit for default host-only cookie)
# SESSION_COOKIE_DOMAIN=localhost
# Cookie path
# SESSION_COOKIE_PATH=/
# Secure flag: set true in production and when SameSite=None
# SESSION_COOKIE_SECURE=false
# SameSite policy: lax|strict|none (use none when embedding cross-site)
# SESSION_COOKIE_SAMESITE=lax
# Cookie lifetime in days (Firebase max 14)
# SESSION_COOKIE_MAX_AGE_DAYS=14
# When to trigger renewal signal (hours before expiry)
# SESSION_COOKIE_RENEWAL_THRESHOLD_HOURS=24
