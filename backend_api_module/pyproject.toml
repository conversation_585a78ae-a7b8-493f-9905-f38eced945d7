[tool.poetry]
name = "backend-api-module"
version = "1.0.1"
description = "Backend API module for smart agent"
authors = ["Kotilabs Tech <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.12"
fastapi = "0.116.1"
uvicorn = {extras = ["standard"], version = "0.35.0"}
websockets = "12.0"
pydantic = "2.11.7"
pydantic-settings = "2.10.1"
python-jose = {extras = ["cryptography"], version = "3.5.0"}
passlib = {extras = ["bcrypt"], version = "1.7.4"}
python-multipart = "0.0.6"
httpx = "0.27.2"
requests = "2.32.4"
structlog = "23.2.0"
python-dotenv = "1.1.0"
PyYAML = "6.0.1"
packaging = ">=23.2,<25.0"

[tool.poetry.group.optional.dependencies]
firebase = ["firebase-admin==6.8.0"]
database = ["couchdb3==2.0.2"]
ai = [
    "langfuse==3.2.1",
    "langchain-core>=0.1.21,<0.2.0",
    "langchain-community>=0.0.20,<0.1.0",
    "langchain-openai>=0.0.5,<0.1.0",
    "langchain-google-genai>=0.0.9,<0.1.0"
]

[tool.poetry.group.dev.dependencies]
black = "23.12.1"
isort = "5.13.2"
mypy = "1.8.0"
pytest-timeout = "2.2.0"
psutil = "5.9.8"
flake8 = "7.0.0"
pre-commit = "3.6.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[project]
name = "aagmanai-backend"
version = "1.0.0"

# Black configuration
[tool.black]
line-length = 120
target-version = ['py39']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort configuration
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["fastapi", "uvicorn", "websockets", "pydantic", "structlog"]
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]

# mypy configuration
[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "fastapi.*",
    "uvicorn.*",
    "websockets.*",
    "pydantic.*",
    "structlog.*",
    "firebase_admin.*",
    "couchdb3.*",
    "langchain.*",
    "langfuse.*"
]
ignore_missing_imports = true

# Flake8 configuration
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503", "D202"]
exclude = [
    ".git",
    "__pycache__",
    ".venv",
    "build",
    "dist",
    ".mypy_cache",
    ".pytest_cache"
]
