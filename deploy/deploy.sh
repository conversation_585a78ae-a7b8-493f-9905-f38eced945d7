#!/bin/bash

# Aagman Smart Agent Deployment Script
# This script deploys the latest version of the application on EC2 instances

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENVIRONMENT=${1:-production}
TAG=${2:-latest}
CLI_TAG="$TAG"

print_status "Starting deployment for environment: $ENVIRONMENT"
print_status "Using tag: $TAG"

# Load environment variables
if [ -f "$PROJECT_ROOT/.env.$ENVIRONMENT" ]; then
    print_status "Loading environment variables from .env.$ENVIRONMENT"
    export $(cat "$PROJECT_ROOT/.env.$ENVIRONMENT" | grep -v '^#' | xargs)
else
    print_warning "Environment file .env.$ENVIRONMENT not found"
fi

# Ensure CLI-provided TAG is not overridden by .env file
if [ -n "$CLI_TAG" ]; then
    TAG="$CLI_TAG"
    export TAG
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available (try both modern and legacy syntax)
if command -v docker > /dev/null 2>&1 && docker compose version > /dev/null 2>&1; then
    print_success "Docker Compose (modern) is available"
    DOCKER_COMPOSE_CMD="docker compose"
elif command -v docker-compose > /dev/null 2>&1; then
    print_success "Docker Compose (legacy) is available"
    DOCKER_COMPOSE_CMD="docker-compose"
else
    print_error "Docker Compose is not available. Please install it and try again."
    exit 1
fi

# Navigate to project root
cd "$PROJECT_ROOT"

# Set environment variables for docker compose
export TAG=$TAG
export APP_ENV=$ENVIRONMENT
export GITHUB_REPOSITORY=${GITHUB_REPOSITORY:-your-username/smart-agent}

# Select the appropriate Docker Compose file based on environment
case "$ENVIRONMENT" in
    staging)
        DOCKER_COMPOSE_FILE="docker-compose.staging.yml"
        ;;
    production|prod)
        DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
        ;;
    development|dev)
        DOCKER_COMPOSE_FILE="docker-compose.yml"
        ;;
    *)
        print_warning "Unknown environment '$ENVIRONMENT'. Falling back to production compose file."
        DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
        ;;
esac

print_status "Using GitHub Container Registry: ghcr.io/$GITHUB_REPOSITORY"
print_status "TAG variable: '$TAG'"
print_status "APP_ENV variable: '$APP_ENV'"
print_status "GITHUB_REPOSITORY variable: '$GITHUB_REPOSITORY'"
print_status "Docker Compose file selected: $DOCKER_COMPOSE_FILE"

# Resolve Firebase service account host path (used by docker-compose variable substitution)
FIREBASE_CREDENTIALS_HOST_PATH=${FIREBASE_CREDENTIALS_HOST_PATH:-"$PROJECT_ROOT/backend_api_module/firebase-service-account.json"}
export FIREBASE_CREDENTIALS_HOST_PATH
print_status "Firebase credentials host path: $FIREBASE_CREDENTIALS_HOST_PATH"

# Validate Firebase credentials path
if [ -d "$FIREBASE_CREDENTIALS_HOST_PATH" ]; then
    print_error "Firebase credentials path points to a directory, expected a file: $FIREBASE_CREDENTIALS_HOST_PATH"
    print_error "Please set FIREBASE_CREDENTIALS_HOST_PATH to the full path of your firebase-service-account.json file."
    exit 1
fi
if [ ! -f "$FIREBASE_CREDENTIALS_HOST_PATH" ]; then
    print_warning "Firebase credentials file not found at: $FIREBASE_CREDENTIALS_HOST_PATH"
    print_warning "Backend will fail to start without valid credentials."
fi

# Validate TAG variable
if [ -z "$TAG" ]; then
    print_error "TAG variable is empty or not set. Please provide a valid tag."
    print_error "Usage: $0 <environment> <tag>"
    exit 1
fi

# Login to GitHub Container Registry (if credentials are provided)
if [ -n "$GITHUB_TOKEN" ]; then
    print_status "Logging in to GitHub Container Registry..."
    echo "$GITHUB_TOKEN" | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin
    print_success "Logged in to GitHub Container Registry"
else
    print_warning "GITHUB_TOKEN not set - attempting to pull public images"
fi

# Create frontend distribution directory
print_status "Creating frontend distribution directory..."
mkdir -p frontend-dist
sudo chown -R $USER:$USER frontend-dist
sudo chmod -R 0777 frontend-dist

print_status "Pulling latest Docker images..."
print_status "Docker Compose command: $DOCKER_COMPOSE_CMD"
print_status "Docker Compose file: $DOCKER_COMPOSE_FILE"
print_status "Current working directory: $(pwd)"

# Debug Docker Compose configuration
print_status "Docker Compose configuration:"
$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" config | grep -A 5 -B 5 "image:"

$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" pull

print_status "Stopping existing backend container..."
$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" down backend

print_status "Starting backend service with new image..."
$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" up -d backend

print_status "Extracting frontend static files from image..."

# Create a temporary container from the frontend image and copy built assets
FRONTEND_IMAGE="ghcr.io/$GITHUB_REPOSITORY/aagman-frontend:${TAG}"
TMP_CONTAINER_NAME="aagman-frontend-extract-$(date +%s)"

print_status "Using image: $FRONTEND_IMAGE"
docker create --name "$TMP_CONTAINER_NAME" "$FRONTEND_IMAGE" >/dev/null
docker cp "$TMP_CONTAINER_NAME":/usr/share/nginx/html/. frontend-dist/ || true
docker rm -f "$TMP_CONTAINER_NAME" >/dev/null

print_status "Copying frontend files to nginx directory..."
if [ "$ENVIRONMENT" = "staging" ]; then
    NGINX_FRONTEND_DIR="/home/<USER>/staging/smart-agent/frontend/dist"
else
    NGINX_FRONTEND_DIR="/home/<USER>/production/smart-agent/frontend/dist"
fi

# Create nginx frontend directory if it doesn't exist
sudo mkdir -p "$NGINX_FRONTEND_DIR"

# Check if frontend files were built successfully
if [ -z "$(ls -A frontend-dist 2>/dev/null)" ]; then
    print_error "Frontend files were not built successfully. frontend-dist directory is empty."
    print_error "Checked image extraction. Verify the frontend image contains built assets at /usr/share/nginx/html."
    exit 1
fi

# Copy frontend files to nginx directory (avoid glob expansion issues)
print_status "Copying $(ls -A frontend-dist | wc -l) items to nginx directory..."
sudo cp -r frontend-dist/. "$NGINX_FRONTEND_DIR/"
sudo chown -R ubuntu:ubuntu "$NGINX_FRONTEND_DIR"

print_status "Frontend files copied to: $NGINX_FRONTEND_DIR"

# Wait for services to be healthy
print_status "Waiting for backend service to be healthy..."
sleep 10

# Check service health
print_status "Checking service health..."

# Check backend health
if $DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" exec -T backend curl -f http://localhost:8000/health > /dev/null 2>&1; then
    print_success "Backend service is healthy"
else
    print_warning "Backend service health check failed"
fi

# Check nginx health (using host nginx)
if curl -f http://localhost/health > /dev/null 2>&1; then
    print_success "Nginx service is healthy"
else
    print_warning "Nginx health check failed"
fi

# Show running containers
print_status "Current running containers:"
$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" ps

# Show logs for the last few lines
print_status "Recent backend logs:"
$DOCKER_COMPOSE_CMD -f "$DOCKER_COMPOSE_FILE" logs --tail=20 backend

print_success "Deployment completed successfully!"
print_status "Environment: $ENVIRONMENT"
print_status "Tag: $TAG"
print_status "Backend service is running and healthy"
print_status "Frontend files updated in nginx directory: $NGINX_FRONTEND_DIR"

# Optional: Clean up old images
if [ "$3" = "--cleanup" ]; then
    print_status "Cleaning up old Docker images..."
    docker image prune -f
    print_success "Cleanup completed"
fi

# No frontend-builder container used; assets copied via docker cp
print_status "Frontend asset extraction complete"
