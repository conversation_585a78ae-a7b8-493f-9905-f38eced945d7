/**
 * ===============================================================================
 * LOGIN BRIDGE CONTENT SCRIPT
 * ===============================================================================
 *
 * This content script acts as a communication bridge between:
 * - Web Login Page (DOM events) ↔ Content Script ↔ Extension (Chrome Runtime)
 *
 * SECURITY: Only handles login communication for localhost and authorized domains
 * FLOW: Web Page → DOM Event → Content Script → Chrome Runtime → Extension
 */

console.log("[LoginBridge] Content script loaded");

// Check if this is a login page by looking for the ?mode=extension parameter
const urlParams = new URLSearchParams(window.location.search);
const isLoginMode = urlParams.get("mode") === "extension";

if (!isLoginMode) {
  console.log("[LoginBridge] Not a login page, bridge inactive");
} else {
  console.log(
    "[LoginBridge] Login mode detected, setting up communication bridge"
  );

  // Listen for messages from the web page (DOM events)
  window.addEventListener("message", (event) => {
    // Security: Only accept messages from same origin
    if (event.origin !== window.location.origin) {
      console.warn(
        "[LoginBridge] Rejected message from different origin:",
        event.origin
      );
      return;
    }

    // Only handle extension login messages FROM the web page (not echoed back)
    if (
      !event.data ||
      !event.data.type ||
      !event.data.type.startsWith("EXTENSION_") ||
      event.data.fromBridge // Ignore messages we sent back to avoid echo
    ) {
      return;
    }

    console.log("[LoginBridge] Forwarding message to extension:", event.data);

    // Forward message to extension via Chrome Runtime
    chrome.runtime.sendMessage(event.data, (response) => {
      if (chrome.runtime.lastError) {
        console.error(
          "[LoginBridge] Failed to send to extension:",
          chrome.runtime.lastError
        );
        // Send error back to web page
        window.postMessage(
          {
            type: "EXTENSION_ERROR",
            error: chrome.runtime.lastError.message,
            fromBridge: true, // Mark as from bridge to avoid echo
          },
          window.location.origin
        );
      } else {
        console.log("[LoginBridge] Response from extension:", response);
        // Don't forward generic response - extension sends specific messages via chrome.tabs.sendMessage
      }
    });
  });

  // Listen for messages from extension (Chrome Runtime)
  chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log("[LoginBridge] Message from extension:", message);

    // Forward extension messages to web page via DOM events (mark as from bridge)
    window.postMessage(
      {
        ...message,
        fromBridge: true, // Mark as from bridge to avoid echo
      },
      window.location.origin
    );

    // Acknowledge receipt
    sendResponse({ received: true });
    return true;
  });

  // Notify extension that bridge is ready
  setTimeout(() => {
    chrome.runtime.sendMessage({
      type: "EXTENSION_BRIDGE_READY",
      url: window.location.href,
      tabId: "unknown", // Extension will fill this in
    });
  }, 100);

  console.log("[LoginBridge] Communication bridge established");
}
