import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  mode: "production",
  entry: {
    'json-rule-engine-bundle': './lib/bundle-entry.js'
  },
  output: {
    path: path.resolve(__dirname, "lib"),
    filename: "[name].bundle.js",
    // Produce a classic script suitable for importScripts in MV3 service worker
    library: {
      type: "var",
      name: "JsonRuleEngineBundle"
    },
  },
  // Build for service worker/global scope
  target: ['webworker', 'es5'],
  resolve: {
    extensions: [".js", ".json"],
  },
  module: {
    rules: [
      {
        test: /\.js$/,
        exclude: /node_modules/,
        use: {
          loader: "babel-loader",
          options: {
            presets: ["@babel/preset-env"],
          },
        },
      },
    ],
  },
};
