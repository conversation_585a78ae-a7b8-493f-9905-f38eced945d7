<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Agent Executor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🚀 Smart Agent Executor</h1>
            <p>Execute automation workflows with Action Array Format</p>
        </header>

        <main>
            <!-- Action Array Input Section -->
            <section class="input-section">
                <h3>Action Array</h3>
                <div class="input-group">
                    <label for="actions-json">Enter your action array:</label>
                    <textarea
                        id="actions-json"
                        placeholder='[
  {
    "id": "buy1",
    "action": "PlaceBuyLimitOrder",
    "arguments": {
      "symbol": "TCS",
      "exchange": "NSE",
      "quantity": 5,
      "price": 3800,
      "productType": "CNC"
    },
    "depends_on": null
  },
  {
    "id": "stopLoss1",
    "action": "PlaceSellStopLossMarketOrder",
    "depends_on": "buy1",
    "arguments": {
      "symbol": "TCS",
      "exchange": "NSE",
      "quantity": 5,
      "triggerPrice": 3700,
      "productType": "CNC"
    }
  }
]'
                        rows="12"
                    ></textarea>
                </div>

                <div class="button-group">
                    <button id="run-actions" class="btn btn-primary">
                        ▶️ Run Actions
                    </button>
                    <button id="test-current-tab" class="btn btn-secondary">
                        🧪 Test Current Tab
                    </button>
                    <button id="reload-extension" class="btn btn-secondary">
                        🔄 Reload Extension
                    </button>
                    <button id="clear" class="btn btn-secondary">
                        🗑️ Clear
                    </button>
                </div>
            </section>

            <!-- Status Section -->
            <section class="status-section">
                <h3>Execution Status</h3>
                <div id="status-message" class="status-message"></div>
                <div id="execution-log" class="execution-log"></div>
            </section>

            <!-- Help Section -->
            <section class="help-section">
                <details>
                    <summary id="help-toggle">📖 Action Array Format Help</summary>
                    <div id="help-content" class="help-content">
                        <h4>Action Array Structure:</h4>
                        <pre><code>[
  {
    "id": "unique_action_id",
    "action": "ActionType",
    "arguments": {
      "param1": "value1",
      "param2": "value2"
    },
    "depends_on": "previous_action_id" // or null for first action
  }
]</code></pre>

                        <h4>Supported Actions:</h4>
                        <ul>
                            <li><strong>PlaceBuyLimitOrder</strong> - Place a buy limit order</li>
                            <li><strong>PlaceSellLimitOrder</strong> - Place a sell limit order</li>
                            <li><strong>PlaceBuyStopLossMarketOrder</strong> - Place a buy stop loss market order</li>
                            <li><strong>PlaceSellStopLossMarketOrder</strong> - Place a sell stop loss market order</li>
                            <li><strong>MONITORPROFIT</strong> - Monitor price for profit target</li>
                        </ul>

                        <h4>Example:</h4>
                        <pre><code>[
  {
    "id": "buy1",
    "action": "PlaceBuyLimitOrder",
    "arguments": {
      "symbol": "TCS",
      "exchange": "NSE",
      "quantity": 5,
      "price": 3800,
      "productType": "CNC"
    },
    "depends_on": null
  },
  {
    "id": "stopLoss1",
    "action": "PlaceSellStopLossMarketOrder",
    "depends_on": "buy1",
    "arguments": {
      "symbol": "TCS",
      "exchange": "NSE",
      "quantity": 5,
      "triggerPrice": 3700,
      "productType": "CNC"
    }
  }
]</code></pre>
                    </div>
                </details>
            </section>
        </main>
    </div>

    <script type="module" src="popup.js"></script>
</body>
</html>
