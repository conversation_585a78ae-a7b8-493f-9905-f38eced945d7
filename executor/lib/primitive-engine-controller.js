// Primitive Engine Controller - Combined Rule Engine and Controller
import { Engine } from 'json-rules-engine';
import { getSupportedSites, getActionArguments } from './config.js';
import { ACTION_TYPES } from './constants.js';
import { ExecutionService } from './execution-service.js';

/**
 * PrimitiveEngineController - A unified class that combines:
 * 1. JSON Rule Engine functionality (from rule-engine.js)
 * 2. Engine orchestration and control (from engine-controller.js)
 */
export class PrimitiveEngineController {
  constructor() {
    // Rule engine properties (from JSONRuleEngine)
    this.ruleEngine = new Engine();
    this.facts = new Map();

    // Controller properties
    this.initialized = false;
    this.graphs = new Map();
    this.nodeStatuses = new Map();
    this.parentStatusListeners = new Map();
    this.activeTabs = 0;

    // Enhanced execution service for real browser automation
    this.executionService = new ExecutionService();
  }

  // ==================== INITIALIZATION ====================

  async initialize() {
    if (!this.initialized) {
      // Initialize execution service
      await this.executionService.loadTimeoutSettings();
      // Defer starting login monitoring until a graph actually executes
      this.initialized = true;
      console.log('[PrimitiveEngineController] Initialized with enhanced execution service');
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initialize();
    }
  }

  // ==================== RULE ENGINE METHODS (from rule-engine.js) ====================

  /**
   * Add a rule to the engine
   * @param {Object} rule - Rule definition
   */
  addRule(rule) {
    if (!rule.conditions || !rule.event) {
      throw new Error('Rule must have conditions and event properties');
    }

    try {
      this.ruleEngine.addRule(rule);
    } catch (error) {
      console.error('Failed to add rule:', error);
      throw error;
    }
  }

  /**
   * Add a fact to the engine
   * @param {string} id - Fact identifier
   * @param {*} value - Fact value (can be a function or static value)
   */
  addFact(id, value) {
    this.facts.set(id, value);

    if (typeof value === 'function') {
      this.ruleEngine.addFact(id, value);
    } else {
      this.ruleEngine.addFact(id, () => value);
    }
  }

  /**
   * Remove a rule from the engine
   * @param {Object|string} rule - Rule object or rule name
   */
  removeRule(rule) {
    return this.ruleEngine.removeRule(rule);
  }

  /**
   * Remove a fact from the engine
   * @param {string} id - Fact identifier
   */
  removeFact(id) {
    this.facts.delete(id);
    return this.ruleEngine.removeFact(id);
  }

  /**
   * Run the rule engine with provided data
   * @param {Object} data - Data to evaluate against rules
   * @returns {Promise} Promise resolving to engine results
   */
  async run(data = {}) {
    try {
      // Add runtime facts from data
      Object.keys(data).forEach(key => {
        this.addFact(key, data[key]);
      });

      const results = await this.ruleEngine.run();

      return {
        events: results.events || [],
        results: results.results || [],
        failureEvents: results.failureEvents || [],
        failureResults: results.failureResults || []
      };
    } catch (error) {
      console.error('Rule engine execution failed:', error);
      throw error;
    }
  }

  /**
   * Clear all rules and facts from the engine
   */
  clearRules() {
    this.ruleEngine = new Engine();
    this.facts.clear();
  }

  /**
   * Get all facts
   * @returns {Map} Map of all facts
   */
  getFacts() {
    return new Map(this.facts);
  }

  /**
   * Check if a fact exists
   * @param {string} id - Fact identifier
   * @returns {boolean}
   */
  hasFact(id) {
    return this.facts.has(id);
  }

  /**
   * Get rule count
   * @returns {number} Number of rules in the engine
   */
  getRuleCount() {
    return this.ruleEngine.rules ? this.ruleEngine.rules.length : 0;
  }

  // ==================== CONTROLLER METHODS (from engine-controller.js) ====================

  /**
   * Add a graph with actions
   * @param {string} graphId - Graph identifier
   * @param {Array} actions - Array of actions
   */
  addGraph(graphId, actions) {
    try {
      this.graphs.set(graphId, {
        actions: actions || [],
        nodeStatuses: new Map(),
        created: new Date()
      });
      return { isValid: true, message: `Graph '${graphId}' added with ${actions.length} actions` };
    } catch (error) {
      return { isValid: false, message: error.message };
    }
  }

  /**
   * Execute an array of actions for a graph using node-by-node execution
   * @param {string} graphId - Graph identifier
   * @param {Array} actions - Actions to execute
   * @param {Object} options - Execution options
   */

  async executeActionArray(graphId, actions, options = {}, updateStatus = () => { }, queryStatus = null) {
    await this.ensureInitialized();

    // Start login monitoring only when a graph execution begins
    this.executionService.startLoginMonitor();

    try {
      console.log(`[PrimitiveEngineController] Executing action array for graph ${graphId} using node-by-node execution`);

      // Use node-by-node execution (no level blocking, missing deps = independent)
      return await this.executionService.executeGraphNodeByNode(graphId, actions, {
        ...options,
        reuseTab: options.reuseTab !== false,
        stopOnError: options.stopOnError !== false,
        failDependentsOnError: options.failDependentsOnError !== false
      }, updateStatus, queryStatus);
    } catch (error) {
      console.error(`[PrimitiveEngineController] Failed to execute action array for graph ${graphId}:`, error);
      return {
        success: false,
        error: error.message,
        message: `Failed to execute actions for graph ${graphId}: ${error.message}`,
        graphId
      };
    }
  }



  /**
   * Trigger a graph starting from a specific action
   * @param {string} graphId - Graph identifier
   * @param {string} startActionId - Starting action ID
   */
  async triggerGraph(graphId, startActionId) {
    await this.ensureInitialized();

    const graph = this.graphs.get(graphId);
    if (!graph) {
      throw new Error(`Graph '${graphId}' not found`);
    }

    try {
      const startAction = graph.actions.find(action => action.id === startActionId);
      if (!startAction) {
        throw new Error(`Start action '${startActionId}' not found in graph '${graphId}'`);
      }

      this.setNodeStatus(graphId, startActionId, 'running');

      // Execute the starting action and its dependencies
      const result = await this.executeActionArray(graphId, [startAction]);

      this.setNodeStatus(graphId, startActionId, 'completed');

      return result;
    } catch (error) {
      this.setNodeStatus(graphId, startActionId, 'failed');
      throw error;
    }
  }

  /**
   * Trigger graph with parent tracking
   * @param {string} graphId - Graph identifier
   * @param {string} startActionId - Starting action ID
   * @param {Object} options - Additional options
   */
  async triggerGraphWithParentTracking(graphId, startActionId, options = {}) {
    await this.ensureInitialized();

    const { parentActionId, trackingEnabled = true } = options;

    if (trackingEnabled && parentActionId) {
      this.addParentStatusListener(graphId, parentActionId, (status) => {
        console.log(`Parent action ${parentActionId} status changed to: ${status}`);
      });
    }

    return this.triggerGraph(graphId, startActionId);
  }

  /**
   * Get parent status before subtree execution
   * @param {string} graphId - Graph identifier
   * @param {string} parentActionId - Parent action ID
   */
  async getParentStatusBeforeSubtree(graphId, parentActionId) {
    await this.ensureInitialized();
    return this.getNodeStatus(graphId, parentActionId);
  }

  // ==================== NODE STATUS MANAGEMENT ====================

  /**
   * Set node status
   * @param {string} graphId - Graph identifier
   * @param {string} actionId - Action identifier
   * @param {string} status - Status value
   */
  setNodeStatus(graphId, actionId, status) {
    const graphKey = `${graphId}:${actionId}`;
    this.nodeStatuses.set(graphKey, {
      status,
      timestamp: new Date(),
      graphId,
      actionId
    });

    // Notify listeners
    this.notifyParentStatusListeners(graphId, actionId, status);
  }

  /**
   * Get node status
   * @param {string} graphId - Graph identifier
   * @param {string} actionId - Action identifier
   */
  getNodeStatus(graphId, actionId) {
    const graphKey = `${graphId}:${actionId}`;
    const statusData = this.nodeStatuses.get(graphKey);
    return statusData ? statusData.status : null;
  }

  /**
   * Get all node statuses for a graph
   * @param {string} graphId - Graph identifier
   */
  getNodeStatuses(graphId) {
    const statuses = [];
    for (const [key, value] of this.nodeStatuses.entries()) {
      if (value.graphId === graphId) {
        statuses.push(value);
      }
    }
    return statuses;
  }

  // ==================== PARENT STATUS LISTENERS ====================

  /**
   * Add parent status listener
   * @param {string} graphId - Graph identifier
   * @param {string} actionId - Action identifier
   * @param {Function} listener - Listener function
   */
  addParentStatusListener(graphId, actionId, listener) {
    const key = `${graphId}:${actionId}`;
    if (!this.parentStatusListeners.has(key)) {
      this.parentStatusListeners.set(key, new Set());
    }
    this.parentStatusListeners.get(key).add(listener);
  }

  /**
   * Remove parent status listener
   * @param {string} graphId - Graph identifier
   * @param {string} actionId - Action identifier
   * @param {Function} listener - Listener function
   */
  removeParentStatusListener(graphId, actionId, listener) {
    const key = `${graphId}:${actionId}`;
    const listeners = this.parentStatusListeners.get(key);
    if (listeners) {
      listeners.delete(listener);
      if (listeners.size === 0) {
        this.parentStatusListeners.delete(key);
      }
    }
  }

  /**
   * Notify parent status listeners
   * @param {string} graphId - Graph identifier
   * @param {string} actionId - Action identifier
   * @param {string} status - New status
   */
  notifyParentStatusListeners(graphId, actionId, status) {
    const key = `${graphId}:${actionId}`;
    const listeners = this.parentStatusListeners.get(key);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(status);
        } catch (error) {
          console.error('Error in parent status listener:', error);
        }
      });
    }
  }

  // ==================== STATUS AND CONFIGURATION ====================

  /**
   * Get engine status including execution service status
   */
  getStatus() {
    const executionStatus = this.executionService ? this.executionService.getAllExecutionStatus() : {};

    return {
      initialized: this.initialized,
      activeTabs: this.activeTabs,
      graphs: this.graphs.size,
      rules: this.getRuleCount(),
      facts: this.facts.size,
      nodeStatuses: this.nodeStatuses.size,
      // Enhanced execution status
      executionService: {
        activeGraphs: executionStatus.activeGraphs || 0,
        activeTabs: executionStatus.activeTabs || 0,
        activeNodes: executionStatus.activeNodes || 0,
        login: executionStatus.login || { required: null, lastChecked: null, monitorTabId: null, pausedByLogin: [] }
      }
    };
  }

  /**
   * Get detailed status for a specific graph
   * @param {string} graphId - Graph identifier
   */
  getGraphExecutionStatus(graphId) {
    if (!this.executionService) {
      return { found: false, error: 'Execution service not initialized' };
    }

    return this.executionService.getGraphStatus(graphId);
  }

  /**
   * Get all active executions status
   */
  getAllExecutionsStatus() {
    if (!this.executionService) {
      return { error: 'Execution service not initialized' };
    }

    return this.executionService.getAllExecutionStatus();
  }

  /**
   * Cancel a running graph execution
   * @param {string} graphId - Graph identifier
   */
  async cancelGraphExecution(graphId) {
    if (!this.executionService) {
      return { success: false, message: 'Execution service not initialized' };
    }

    try {
      // Cleanup tabs and tracking for this graph
      await this.executionService.cleanupGraphTabs(graphId);

      // Update graph status
      if (this.executionService.activeGraphs.has(graphId)) {
        const graph = this.executionService.activeGraphs.get(graphId);
        graph.status = 'cancelled';
        graph.endTime = new Date();
      }

      console.log(`[PrimitiveEngineController] Cancelled graph execution: ${graphId}`);

      return { success: true, message: `Graph execution cancelled: ${graphId}` };
    } catch (error) {
      console.error(`[PrimitiveEngineController] Error cancelling graph execution: ${graphId}`, error);
      return { success: false, message: `Failed to cancel graph execution: ${error.message}` };
    }
  }

  /**
   * Pause a running graph execution
   * @param {string} graphId - Graph identifier
   */
  pauseGraphExecution(graphId) {
    if (!this.executionService) {
      return { success: false, message: 'Execution service not initialized' };
    }

    try {
      console.log(`[PrimitiveEngineController] Pausing graph execution: ${graphId}`);
      const result = this.executionService.pauseGraph(graphId);

      if (result.success) {
        console.log(`[PrimitiveEngineController] Successfully paused graph: ${graphId}`);
      }

      return result;
    } catch (error) {
      console.error(`[PrimitiveEngineController] Error pausing graph execution: ${graphId}`, error);
      return { success: false, message: `Failed to pause graph execution: ${error.message}` };
    }
  }

  /**
   * Resume a paused graph execution
   * @param {string} graphId - Graph identifier
   */
  resumeGraphExecution(graphId) {
    if (!this.executionService) {
      return { success: false, message: 'Execution service not initialized' };
    }

    try {
      console.log(`[PrimitiveEngineController] Resuming graph execution: ${graphId}`);
      const result = this.executionService.resumeGraph(graphId);

      if (result.success) {
        console.log(`[PrimitiveEngineController] Successfully resumed graph: ${graphId}`);
      }

      return result;
    } catch (error) {
      console.error(`[PrimitiveEngineController] Error resuming graph execution: ${graphId}`, error);
      return { success: false, message: `Failed to resume graph execution: ${error.message}` };
    }
  }

  /**
   * Get configuration
   */
  async getConfiguration() {
    try {
      return {
        supportedSites: await getSupportedSites(),
        actionArguments: await getActionArguments(),
        constants: ACTION_TYPES
      };
    } catch (error) {
      return { supportedSites: {}, actionArguments: {}, constants: {} };
    }
  }

  // ==================== CLEANUP ====================

  /**
   * Cleanup resources
   */
  async cleanup() {
    this.clearRules();
    this.graphs.clear();
    this.nodeStatuses.clear();
    this.parentStatusListeners.clear();
    this.initialized = false;
    this.activeTabs = 0;
  }
}
