// Configuration management for the executor extension
class ConfigManager {
  constructor() {
    this.sharedConfig = null;
    this.configUrl = "lib/shared-config.json";
  }

  async loadSharedConfig() {
    if (this.sharedConfig) {
      return this.sharedConfig;
    }

    try {
      console.log("🔍 Loading shared config from:", this.configUrl);

      const response = await fetch(chrome.runtime.getURL(this.configUrl));

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      this.sharedConfig = await response.json();
      console.log("✅ Shared config loaded successfully");
      return this.sharedConfig;
    } catch (error) {
      console.error("❌ Failed to load shared config:", error);
      console.log("🔄 Using fallback config");
      this.sharedConfig = this.getFallbackConfig();
      return this.sharedConfig;
    }
  }

  getFallbackConfig() {
    return {
      SUPPORTED_SITES: {
        kiteByZerodha: {
          name: "Kite by Zerodha",
          url: "https://kite.zerodha.com/",
          contentScript: "content-scripts/zerodha.js",
        },
      },
      ACTION_ARGUMENTS: {
        BUY: ["SYMBOL", "QUANTITY", "EXCHANGE", "PRODUCT_TYPE", "PRODUCTTYPE"],
        SELL: ["SYMBOL", "QUANTITY", "EXCHANGE", "PRODUCT_TYPE", "PRODUCTTYPE"],
        SellAll: ["SYMBOL", "EXCHANGE", "PRODUCT_TYPE"],
        MONITORPROFIT: [
          "TARGET_PROFIT_AMOUNT",
          "TARGET_PROFIT_PERCENTAGE",
          "MONITOR_INTERVAL_SECONDS",
        ],
        Monitor: ["condition"],
        MonitorConditionThenAct: ["condition", "on_trigger"],
      },
      MESSAGE_TYPES: {
        EXECUTE_ACTIONS: "EXECUTE_ACTIONS",
        PERFORM_SITE_ACTIONS: "PERFORM_SITE_ACTIONS",
        ACTION_STATUS_UPDATE: "ACTION_STATUS_UPDATE",
        EXECUTION_COMPLETE: "EXECUTION_COMPLETE",
      },
      ENVIRONMENT: {
        slow_execute: false,
        close_tabs_after_execution: true,
      },
    };
  }

  async getSupportedSites() {
    const config = await this.loadSharedConfig();
    return config.SUPPORTED_SITES;
  }

  async getActionArguments() {
    const config = await this.loadSharedConfig();
    return config.ACTION_ARGUMENTS;
  }

  async getMessageTypes() {
    const config = await this.loadSharedConfig();
    return config.MESSAGE_TYPES;
  }

  async getEnvironment() {
    const config = await this.loadSharedConfig();
    return config.ENVIRONMENT;
  }

  async getFullConfig() {
    return await this.loadSharedConfig();
  }
}

// Create singleton instance
const configManager = new ConfigManager();

// Export async functions
export async function getSupportedSites() {
  return await configManager.getSupportedSites();
}

export async function getActionArguments() {
  return await configManager.getActionArguments();
}

export async function getMessageTypes() {
  return await configManager.getMessageTypes();
}

export async function getEnvironment() {
  return await configManager.getEnvironment();
}

export async function getFullConfig() {
  return await configManager.getFullConfig();
}

// Export the manager instance for advanced usage
export { configManager };

// For backward compatibility, export the fallback values directly
export const SUPPORTED_SITES = {
  kiteByZerodha: {
    name: "Kite by Zerodha",
    url: "https://kite.zerodha.com/",
    contentScript: "content-scripts/zerodha.js",
  },
};

export const ACTION_ARGUMENTS = {
  BUY: [
    "SYMBOL",
    "QUANTITY",
    "PRICE",
    "EXCHANGE",
    "PRODUCT_TYPE",
    "PRODUCTTYPE",
  ],
  SELL: [
    "SYMBOL",
    "QUANTITY",
    "PRICE",
    "EXCHANGE",
    "PRODUCT_TYPE",
    "PRODUCTTYPE",
  ],
  SellAll: ["SYMBOL", "EXCHANGE", "PRODUCT_TYPE"],
  MONITORPROFIT: [
    "TARGET_PROFIT_AMOUNT",
    "TARGET_PROFIT_PERCENTAGE",
    "MONITOR_INTERVAL_SECONDS",
  ],
  Monitor: ["condition"],
  MonitorConditionThenAct: ["condition", "on_trigger"],
};
