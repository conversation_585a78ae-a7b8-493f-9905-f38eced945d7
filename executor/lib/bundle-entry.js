// Bundle entry point to expose everything background.js needs
import { Engine } from 'json-rules-engine';
import { PrimitiveEngineController } from './primitive-engine-controller.js';
import { ExecutionService } from './execution-service.js';
import {
  AUTOMATION_MODES,
  MESSAGE_TYPES,
  ACTION_TYPES,
  ARGUMENT_TYPES,
  ARGUMENT_CONFIG,
  ALL_ARGUMENT_NAMES,
  UI_MESSAGE_TYPES,
  CONTENT_SCRIPT_IDENTIFIER,
  INDEX_KEYWORDS,
  INDEX_EXCHANGE_MAPPING,
  DELAYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES
} from './constants.js';
import {
  getSupportedSites,
  getActionArguments,
  getMessageTypes,
  getEnvironment,
  getFullConfig,
  configManager,
  SUPPORTED_SITES,
  ACTION_ARGUMENTS
} from './config.js';

// Attach to global scope for service worker (importScripts) compatibility
(() => {
  const g = (typeof globalThis !== 'undefined') ? globalThis
    : (typeof self !== 'undefined') ? self
      : (typeof window !== 'undefined') ? window
        : (typeof global !== 'undefined') ? global
          : {};

  // Classes
  g.ExecutionService = ExecutionService;
  // Backward-compatibility: background expects EngineController
  g.EngineController = PrimitiveEngineController;
  g.PrimitiveEngineController = PrimitiveEngineController;
  g.Engine = Engine;

  // Constants
  g.AUTOMATION_MODES = AUTOMATION_MODES;
  g.MESSAGE_TYPES = MESSAGE_TYPES;
  g.ACTION_TYPES = ACTION_TYPES;
  g.ARGUMENT_TYPES = ARGUMENT_TYPES;
  g.ARGUMENT_CONFIG = ARGUMENT_CONFIG;
  g.ALL_ARGUMENT_NAMES = ALL_ARGUMENT_NAMES;
  g.UI_MESSAGE_TYPES = UI_MESSAGE_TYPES;
  g.CONTENT_SCRIPT_IDENTIFIER = CONTENT_SCRIPT_IDENTIFIER;
  g.INDEX_KEYWORDS = INDEX_KEYWORDS;
  g.INDEX_EXCHANGE_MAPPING = INDEX_EXCHANGE_MAPPING;
  g.DELAYS = DELAYS;
  g.ERROR_MESSAGES = ERROR_MESSAGES;
  g.SUCCESS_MESSAGES = SUCCESS_MESSAGES;

  // Config accessors
  g.getSupportedSites = getSupportedSites;
  g.getActionArguments = getActionArguments;
  g.getMessageTypes = getMessageTypes;
  g.getEnvironment = getEnvironment;
  g.getFullConfig = getFullConfig;
  g.configManager = configManager;

  // Optional fallbacks (match previous globals in importscripts version)
  g.SUPPORTED_SITES = SUPPORTED_SITES;
  g.ACTION_ARGUMENTS = ACTION_ARGUMENTS;

  try {
    console.log('✅ [BUNDLE] Exposed EngineController, ExecutionService, constants, and config globally');
  } catch { }
})();
