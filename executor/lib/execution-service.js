// Enhanced execution service for handling automation with parallel execution and graph support
import { validateActions } from "./validation.js";
import {
  AUTOMATION_MODES,
  CONTENT_SCRIPT_IDENTIFIER,
  DELAYS,
  ERROR_MESSAGES,
  MESSAGE_TYPES,
} from "./constants.js";
import {
  getActionArguments,
  getSupportedSites,
  getEnvironment,
  getFullConfig,
} from "./config.js";

export class ExecutionService {
  constructor() {
    this.messageTypes = null;
    this.timeoutSettings = {
      execution_service_general_timeout_seconds: 30,
      execution_service_monitoring_timeout_seconds: 43200,
      // Defaults for dependency long-polling in node-by-node scheduler
      // When max wait is null, poll indefinitely
      scheduler_long_poll_interval_ms: 5000,
      scheduler_max_wait_minutes: null,
      // Force a periodic full scan of completed orders for recovery
      completed_orders_full_poll_interval_ms: 10000,
    };

    // Enhanced properties for parallel execution and graph support
    this.activeTabs = new Map(); // tabId -> {tab, site, status, actions}
    this.activeGraphs = new Map(); // graphId -> {status, nodes, progress}
    this.nodeExecutions = new Map(); // nodeId -> {status, result, tabId, startTime, endTime}
    this.maxConcurrentTabs = 6; // Configurable limit
    this.tabPool = new Set(); // Pool of available tabs for reuse

    // Fix for race conditions and memory leaks
    this.cancelledActions = new Set(); // Track cancelled actions
    this.tabAcquisitionLocks = new Map(); // Prevent race conditions in tab acquisition
    this.defaultActionTimeout = 300000; // 5 minutes default timeout for actions
    // Nodes flagged by a recent cascade so they must not start in this tick
    this.nodesBlockedFromStartByCascade = new Map(); // graphId -> Set(nodeId)

    // Pause/Resume functionality
    this.pausedGraphs = new Set(); // Track paused graphs
    this.pauseResolvers = new Map(); // graphId -> resolve function to resume execution

    // Login monitoring system
    this.loginMonitor = {
      tabId: null,
      intervalId: null,
      checking: false,
      required: null,
      lastChecked: null,
      pausedGraphsSet: new Set(),
      started: false,
      isDedicated: false,
      creationPromise: null,
      onRemovedHandler: null,
      // Track data fetches on successful login to avoid repeating every tick
      fetchedThisSession: false,
      lastFetchAt: null,
      // Lightweight order summary for early-exit decisions
      orderSummary: {
        open: { count: null, firstKey: null, lastKey: null },
        completed: { count: null, firstKey: null, lastKey: null },
      },
      // Stabilization to tolerate brief DOM jank when counts same but order rows reshuffle transiently
      stabilize: { mismatchStreak: 1, maxStreak: 1 },
      // Last time we executed a full completed-orders poll
      completedFullPollLastAt: null,
    };

    // Dedicated observer tab to host all long-lived MutationObservers/monitors
    this.observerMonitor = {
      tabId: null,
      isDedicated: false,
      creationPromise: null,
      onRemovedHandler: null,
    };

    // Track recent tab movement/attach/detach to avoid Chrome tab edit conflicts
    this.tabEventInfo = new Map();
    const markMoved = (tabId) => {
      try {
        this.tabEventInfo.set(tabId, { lastMovedAt: Date.now() });
      } catch (_) {}
    };
    try {
      if (chrome?.tabs?.onMoved?.addListener) {
        chrome.tabs.onMoved.addListener((tabId) => markMoved(tabId));
      }
    } catch (_) {}
    try {
      if (chrome?.tabs?.onAttached?.addListener) {
        chrome.tabs.onAttached.addListener((tabId) => markMoved(tabId));
      }
    } catch (_) {}
    try {
      if (chrome?.tabs?.onDetached?.addListener) {
        chrome.tabs.onDetached.addListener((tabId) => markMoved(tabId));
      }
    } catch (_) {}
    this.isTabInCooldown = (tabId, ms = 800) => {
      try {
        const info = this.tabEventInfo.get(tabId);
        return !!(info && Date.now() - info.lastMovedAt < ms);
      } catch (_) {
        return false;
      }
    };
  }

  async loadTimeoutSettings() {
    try {
      const response = await fetch(
        chrome.runtime.getURL("lib/shared-config.json")
      );
      const config = await response.json();
      if (config.TIMEOUT_SETTINGS) {
        this.timeoutSettings = {
          ...this.timeoutSettings,
          ...config.TIMEOUT_SETTINGS,
        };
        console.log(
          "[ExecutionService] Loaded timeout settings:",
          this.timeoutSettings
        );
      }
      if (
        config.EXECUTION_SERVICE &&
        Number.isFinite(config.EXECUTION_SERVICE.max_concurrent_tabs)
      ) {
        this.maxConcurrentTabs = config.EXECUTION_SERVICE.max_concurrent_tabs;
        console.log(
          "[ExecutionService] Loaded max concurrent tabs from config:",
          this.maxConcurrentTabs
        );
      }
      if (
        config.TIMEOUT_SETTINGS &&
        Number.isFinite(
          config.TIMEOUT_SETTINGS.action_execution_timeout_seconds
        )
      ) {
        // Convert seconds to ms for internal defaultActionTimeout
        this.defaultActionTimeout =
          config.TIMEOUT_SETTINGS.action_execution_timeout_seconds * 1000;
        console.log(
          "[ExecutionService] Loaded default action timeout (ms):",
          this.defaultActionTimeout
        );
      }
    } catch (error) {
      console.warn(
        "[ExecutionService] Failed to load timeout config, using defaults:",
        error
      );
    }
  }

  initialize(messageTypes) {
    this.messageTypes = messageTypes;
  }

  /**
   * Generate common variations of an argument name for case-insensitive matching
   * @param {string} argName - Original argument name
   * @returns {Array} Array of possible variations
   */
  generateArgumentVariations(argName) {
    const variations = [argName];

    // Add common variations
    variations.push(argName.toLowerCase());
    variations.push(argName.toUpperCase());

    // Convert snake_case to camelCase
    if (argName.includes("_")) {
      const camelCase = argName
        .toLowerCase()
        .split("_")
        .map((word, index) =>
          index === 0 ? word : word.charAt(0).toUpperCase() + word.slice(1)
        )
        .join("");
      variations.push(camelCase);

      // Also add version without underscores
      variations.push(argName.replace(/_/g, ""));
      variations.push(argName.replace(/_/g, "").toLowerCase());
      variations.push(argName.replace(/_/g, "").toUpperCase());
    }

    // Convert camelCase to snake_case
    if (/[A-Z]/.test(argName) && !argName.includes("_")) {
      const snakeCase = argName
        .replace(/([A-Z])/g, "_$1")
        .toLowerCase()
        .replace(/^_/, "");
      variations.push(snakeCase.toUpperCase());
      variations.push(snakeCase);
    }

    return [...new Set(variations)]; // Remove duplicates
  }

  /**
   * Normalizes actions and arguments to be completely case-insensitive
   * @param {Array} actions - Actions to normalize
   * @param {Object} actionArgumentsConfig - Configuration for action arguments
   * @returns {Array} Normalized actions
   */
  normalizeActions(actions, actionArgumentsConfig) {
    return actions.map((actionObj) => {
      const { action, arguments: args } = actionObj;

      // Find the correct action key (case-insensitive)
      const correctActionKey = Object.keys(actionArgumentsConfig).find(
        (key) => key.toUpperCase() === action.toUpperCase()
      );

      if (!correctActionKey) {
        return actionObj; // Return original if not found
      }

      // Normalize arguments using comprehensive case-insensitive matching
      const normalizedArgs = {};
      const requiredArgs = actionArgumentsConfig[correctActionKey];

      // Map each required argument to its provided value using case-insensitive matching
      for (const requiredArg of requiredArgs) {
        let foundValue = null;

        // First try direct case-insensitive match
        const directMatch = Object.keys(args).find(
          (key) => key.toUpperCase() === requiredArg.toUpperCase()
        );

        if (directMatch) {
          foundValue = args[directMatch];
        } else {
          // Try variations of the argument name
          const variations = this.generateArgumentVariations(requiredArg);
          for (const variation of variations) {
            const match = Object.keys(args).find(
              (key) => key.toUpperCase() === variation.toUpperCase()
            );
            if (match) {
              foundValue = args[match];
              break;
            }
          }
        }

        if (foundValue !== null) {
          normalizedArgs[requiredArg] = foundValue;
        }
      }

      // Add any additional arguments that weren't matched
      for (const [key, value] of Object.entries(args)) {
        const isAlreadyMapped = requiredArgs.some((reqArg) => {
          const variations = this.generateArgumentVariations(reqArg);
          return variations.some(
            (variation) => variation.toUpperCase() === key.toUpperCase()
          );
        });

        if (!isAlreadyMapped) {
          normalizedArgs[key] = value;
        }
      }

      return {
        action: correctActionKey,
        arguments: normalizedArgs,
      };
    });
  }

  async executeActions(params) {
    const { actions, site, automationMode, tabId, actionArgumentsConfig } =
      params;

    const validation = await validateActions(actions, actionArgumentsConfig);
    if (!validation.isValid) {
      return { success: false, message: validation.message };
    }

    const normalizedActions = this.normalizeActions(
      actions,
      actionArgumentsConfig
    );

    if (automationMode === AUTOMATION_MODES.CURRENT_TAB) {
      return this.executeInCurrentTab(tabId, site, normalizedActions);
    } else if (automationMode === AUTOMATION_MODES.BACKGROUND) {
      return this.executeInBackground(site, normalizedActions);
    } else {
      return {
        success: false,
        message: `Unknown automation mode: ${automationMode}`,
      };
    }
  }

  /**
   * Execute graph with node-by-node execution using a single ready-when-deps-done scheduler.
   * - Actions start as soon as their dependencies are satisfied
   * - Missing dependencies are treated as independent actions
   * - Concurrency is limited by maxParallel
   * @param {string} graphId - Graph identifier
   * @param {Array} actions - Actions to execute
   * @param {Object} options - Execution options (maxParallel, reuseTab, stopOnError, failDependentsOnError, actionTimeout)
   * @param {Function} updateStatus - Callback to update node status in PouchDB
   * @param {Function} queryStatus - Callback to query node status from PouchDB
   */
  async executeGraphNodeByNode(
    graphId,
    actions,
    options = {},
    updateStatus = () => {},
    queryStatus = null
  ) {
    try {
      console.log(
        `[ExecutionService] Starting node-by-node graph execution: ${graphId} with ${actions.length} actions`
      );

      // TEMP SPLIT: Convert any MonitorConditionThenAct into two nodes: Monitor + ACT(site primitive)
      // - Monitor node keeps original id (if provided) or uses a generated explicit id
      // - ACT node depends_on the Monitor node and gets an explicit id `${baseId}_ACT`
      // - This enables routing ACT through the normal site primitive path while Monitor uses observer tab
      try {
        const expandedActions = [];
        (actions || []).forEach((a, idx) => {
          const upper = (a?.action || "").toString().toUpperCase();
          if (upper === "MONITORCONDITIONTHENACT") {
            const condition = a?.arguments?.condition ?? a?.condition;
            const onTrigger = a?.arguments?.on_trigger ?? a?.on_trigger;
            if (!onTrigger || !onTrigger.action) {
              // If malformed, keep as-is to avoid breaking execution
              expandedActions.push(a);
              return;
            }

            const baseId = a?.id ? String(a.id) : `MCTA_${idx + 1}`;

            const monitorAction = {
              ...a,
              id: baseId,
              action: "Monitor",
              arguments: { ...(a?.arguments || {}), condition },
            };
            // Remove on_trigger from monitor arguments if present
            if (monitorAction?.arguments?.on_trigger) {
              try {
                delete monitorAction.arguments.on_trigger;
              } catch (_) {}
            }
            // Also drop any top-level on_trigger field to avoid confusion downstream
            if (
              Object.prototype.hasOwnProperty.call(monitorAction, "on_trigger")
            ) {
              try {
                delete monitorAction.on_trigger;
              } catch (_) {}
            }

            const actAction = {
              id: `${baseId}_ACT`,
              action: onTrigger.action,
              arguments: onTrigger.arguments || {},
              depends_on: [baseId],
            };

            expandedActions.push(monitorAction);
            expandedActions.push(actAction);
          } else {
            expandedActions.push(a);
          }
        });
        actions = expandedActions;
        console.log(
          `[ExecutionService] Preprocessed actions for MonitorConditionThenAct split. New count: ${actions.length}`
        );
      } catch (preprocessErr) {
        console.warn(
          "[ExecutionService] Failed to preprocess MonitorConditionThenAct split:",
          preprocessErr
        );
      }

      // Normalize actions to ensure unique node IDs when 'id' is missing
      // Also validate that explicit IDs (when provided) are not duplicated
      const explicitIdSet = new Set();
      const duplicateExplicitIds = new Set();
      for (const a of actions) {
        if (a && a.id) {
          if (explicitIdSet.has(a.id)) duplicateExplicitIds.add(a.id);
          explicitIdSet.add(a.id);
        }
      }
      if (duplicateExplicitIds.size > 0) {
        throw new Error(
          `Duplicate action id(s) detected: ${Array.from(
            duplicateExplicitIds
          ).join(", ")}. Please provide unique 'id' for each action.`
        );
      }

      // Normalize actions to have stable, unique IDs and build a candidate map for dependency resolution
      const candidateToId = new Map(); // case-insensitive key (explicit id or action name) -> normalized id
      const registerCandidate = (key, id) => {
        if (!key) return;
        const k = String(key).toUpperCase();
        if (!candidateToId.has(k)) candidateToId.set(k, id);
      };

      const normalizedActions = actions.map((a, idx) => {
        const actionName = a?.action || `ACTION_${idx + 1}`;
        const explicitId = a?.id ? String(a.id) : null;
        const generatedId = explicitId || `${actionName}_${idx + 1}`;

        // Register candidates for resolving depends_on later (by explicit id or by action name)
        registerCandidate(explicitId, generatedId);
        registerCandidate(actionName, generatedId);

        return {
          ...a,
          id: generatedId,
          __actionName: actionName,
        };
      });

      const totalActionsCount = normalizedActions.length;

      // Perform a synchronous login check before starting
      console.log(
        `[ExecutionService] Performing pre-start login check for graph: ${graphId}`
      );
      try {
        await this.ensureLoginMonitorTab();
        await this.checkLoginAndTogglePause();
      } catch (e) {
        console.warn("[ExecutionService] Pre-start login check failed:", e);
      }

      // If login is required (from last check), start graph in paused state and auto-resume after login
      if (this.loginMonitor.required === true) {
        console.warn(
          `[ExecutionService] Login required - starting graph ${graphId} in paused state and waiting`
        );
        this.activeGraphs.set(graphId, {
          status: "running",
          nodes: new Map(),
          progress: { completed: 0, failed: 0, total: totalActionsCount },
          startTime: new Date(),
          options,
        });
        this.pauseGraph(graphId);
        this.loginMonitor.pausedGraphsSet.add(graphId);
        // Wait until login clears, then resume
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(1000);
        }
        this.resumeGraph(graphId);
      }

      // Initialize graph tracking
      this.activeGraphs.set(graphId, {
        status: "running",
        nodes: new Map(),
        progress: { completed: 0, failed: 0, total: totalActionsCount },
        startTime: new Date(),
        options,
      });

      // Build dependency graph (missing dependencies are filtered out automatically)
      const dependencyMap = this.buildDependencyMap(
        normalizedActions,
        candidateToId
      );
      const { dependencies, dependents } = dependencyMap;

      // Concurrency options
      const maxParallel = Number.isFinite(options.maxParallel)
        ? options.maxParallel
        : this.maxConcurrentTabs;

      // Track completion status and results
      const completedActions = new Set();
      const failedActions = new Set();
      const results = [];
      const activePromises = new Map(); // actionId -> Promise
      const actionMap = new Map(normalizedActions.map((a) => [a.id, a]));

      console.log(`[ExecutionService] Node-by-node execution initialized`);
      console.log(
        `[ExecutionService] Dependency analysis complete - actions with dependencies:`,
        Array.from(dependencies.entries()).filter(
          ([id, deps]) => deps.length > 0
        )
      );

      // Function to check if an action can be executed (PouchDB-based dependency checking)
      const canExecuteAction = async (actionId) => {
        if (
          completedActions.has(actionId) ||
          failedActions.has(actionId) ||
          activePromises.has(actionId)
        ) {
          return false;
        }

        // Only check dependencies that actually exist in the action list
        const deps = dependencies.get(actionId) || [];

        // If no dependencies, can execute immediately
        if (deps.length === 0) {
          return true;
        }

        // Check PouchDB status for each dependency
        if (queryStatus) {
          for (const dep of deps) {
            try {
              const qStart = Date.now();
              try {
                console.log(
                  "[Cancel Order P][ExecutionService] queryStatus start",
                  { graphId, actionId, dep }
                );
              } catch (_) {}
              const depStatus = await queryStatus(graphId, dep);
              const qMs = Date.now() - qStart;
              console.log(
                `[Cancel Order P][ExecutionService] Dependency ${dep} status: ${depStatus} for action ${actionId} (query ${qMs}ms)`
              );

              // Parent must be in a final state. For Monitor nodes we map:
              // - running -> inProgress (non-final)
              // - completed -> triggered (final, success) or stopped (final, failure)
              // Also consider order_executed (monitor doc updated by ACT) as final.
              const finalStates = [
                "completed",
                "executed",
                "failed",
                "cancelled",
                "stopped",
                "triggered",
                "order_executed",
              ];
              if (!finalStates.includes(depStatus)) {
                console.log(
                  `[Cancel Order P][ExecutionService] Dependency ${dep} not in final state (${depStatus}), cannot execute ${actionId}`
                );
                return false;
              }

              // If parent failed/cancelled/stopped, mark this action as failed and cascade doc updates
              const failureStates = ["failed", "cancelled", "stopped"];
              if (failureStates.includes(depStatus)) {
                console.log(
                  `[Cancel Order P][ExecutionService] Dependency ${dep} failed/cancelled (${depStatus}), marking ${actionId} as failed and cascading`
                );
                console.log(
                  "[monitor fix][deps] cascading failure due to parent final failure",
                  { dep, depStatus, actionId }
                );
                failedActions.add(actionId);

                // Trigger cascading cancellation/stopping of dependent docs (treat both failed and cancelled as dependency failure)
                try {
                  console.log(
                    "[Cancel Order P][ExecutionService] Cascade due to dependency final failure",
                    { parent: dep, affected: actionId, parentStatus: depStatus }
                  );
                } catch (_) {}
                this.cancelAllChildNodesAndUpdateDocs(
                  graphId,
                  dep,
                  dependents,
                  normalizedActions
                ).catch((error) =>
                  console.error(
                    `[ExecutionService] Failed to cascade cancellation from ${dep}:`,
                    error
                  )
                );

                // Update local status for this action to failed with metadata
                this.updateNodeStatus(graphId, actionId, "failed", {
                  reason: "dependency_failed",
                  failedDependency: dep,
                  parentStatus: depStatus,
                  timestamp: new Date(),
                });
                return false;
              }
            } catch (error) {
              console.warn(
                `[ExecutionService] Failed to query status for dependency ${dep}:`,
                error
              );
              // Fall back to local tracking if PouchDB query fails
              if (!completedActions.has(dep)) {
                return false;
              }
            }
          }
          try {
            console.log(
              "[Cancel Order P][ExecutionService] All deps final for action",
              { actionId, deps }
            );
          } catch (_) {}
          return true;
        } else {
          // Fall back to original local tracking if no queryStatus callback
          console.warn(
            `[ExecutionService] No queryStatus callback provided, falling back to local tracking`
          );
          return deps.every((dep) => completedActions.has(dep));
        }
      };

      // Function to start an action with timeout protection
      const startAction = async (actionId) => {
        // Secondary guard to avoid duplicate starts
        if (
          activePromises.has(actionId) ||
          completedActions.has(actionId) ||
          failedActions.has(actionId)
        ) {
          return activePromises.get(actionId);
        }
        const action = actionMap.get(actionId);
        if (!action) return;

        console.log(`[ExecutionService] Starting action: ${actionId}`);

        // Create timeout with cancel support and race with execution
        const actionTimeout =
          options.actionTimeout || this.defaultActionTimeout;
        let timeoutId = null;
        const timeoutPromise = new Promise((_, reject) => {
          timeoutId = setTimeout(() => {
            reject(
              new Error(
                `Action timeout: ${actionId} exceeded ${actionTimeout / 1000}s`
              )
            );
          }, actionTimeout);
        });

        const execPromise = this.executeNodeWithTab(
          graphId,
          action,
          options,
          updateStatus
        );

        const promise = Promise.race([execPromise, timeoutPromise])
          .then((result) => {
            // Remove from active promises first
            activePromises.delete(actionId);
            if (timeoutId) clearTimeout(timeoutId);

            if (result.success) {
              completedActions.add(actionId);
              results.push(result);
              this.updateNodeStatus(graphId, actionId, "completed", result);
              console.log(`[ExecutionService] Action completed: ${actionId}`);

              // Log potential dependents that might now be ready
              const potentialDependents = dependents.get(actionId) || [];
              if (potentialDependents.length > 0) {
                console.log(
                  `[ExecutionService] Action ${actionId} completion may unblock dependents: [${potentialDependents.join(
                    ", "
                  )}]`
                );
              }
            } else {
              failedActions.add(actionId);
              results.push({
                success: false,
                nodeId: actionId,
                action: action.action,
                error: result.error || result.message,
              });
              this.updateNodeStatus(
                graphId,
                actionId,
                "failed",
                result.error || result
              );
              console.log(`[ExecutionService] Action failed: ${actionId}`);

              // If this action failed, fail its dependents too (if configured)
              if (options.failDependentsOnError !== false) {
                this.failDependentActions(graphId, actionId, dependents);
                const deps = dependents.get(actionId) || [];
                deps.forEach((dep) => {
                  failedActions.add(dep);
                  // Cancel if currently running
                  if (activePromises.has(dep)) {
                    this.cancelledActions.add(dep);
                  }
                });
              }
            }

            return result;
          })
          .catch((error) => {
            // Always remove from active promises
            activePromises.delete(actionId);
            if (timeoutId) clearTimeout(timeoutId);
            failedActions.add(actionId);
            const errorResult = {
              success: false,
              action: action.action,
              error: error.message,
            };
            results.push(errorResult);
            this.updateNodeStatus(graphId, actionId, "failed", error);
            console.error(
              `[ExecutionService] Action errored: ${actionId}`,
              error
            );

            // Fail dependents on error
            if (options.failDependentsOnError !== false) {
              this.failDependentActions(graphId, actionId, dependents);
              const deps = dependents.get(actionId) || [];
              deps.forEach((dep) => {
                failedActions.add(dep);
                // Cancel if currently running
                if (activePromises.has(dep)) {
                  this.cancelledActions.add(dep);
                }
              });
            }

            return errorResult;
          });

        activePromises.set(actionId, promise);
        return promise;
      };

      // Function to check and start ready actions (respecting maxParallel)
      const checkAndStartReadyActions = async () => {
        console.log(
          `[ExecutionService] Checking for ready actions... Active: ${activePromises.size}, Completed: ${completedActions.size}, Failed: ${failedActions.size}`
        );
        const readyActions = [];
        const seen = new Set();
        for (const action of normalizedActions) {
          const actionId = action.id;
          if (seen.has(actionId)) continue;
          // If this action is blocked by a just-triggered cascade, skip it this iteration
          const blockedSet = this.nodesBlockedFromStartByCascade.get(graphId);
          if (blockedSet && blockedSet.has(actionId)) {
            console.log(
              `[ExecutionService] Skipping ${actionId} due to cascade block in current tick`
            );
            continue;
          }
          if (await canExecuteAction(actionId)) {
            seen.add(actionId);
            readyActions.push(actionId);
            console.log(
              `[ExecutionService] Action ${actionId} is ready to execute`
            );
          }
        }

        // Respect concurrency limit
        const capacity = Math.max(0, maxParallel - activePromises.size);
        const toStart = readyActions.slice(0, capacity);
        if (toStart.length > 0) {
          console.log(
            `[ExecutionService] Starting ${toStart.length} ready actions (capacity ${capacity}/${maxParallel}):`,
            toStart
          );
        } else if (readyActions.length > 0) {
          console.log(
            `[order over order][ExecutionService] Found ${readyActions.length} ready actions but no capacity (${activePromises.size}/${maxParallel} active)`
          );
        } else {
          console.log(
            `[order over order][ExecutionService] No ready actions found this iteration`
          );
        }
        toStart.forEach((actionId) => startAction(actionId));
        return toStart.length;
      };

      // Main execution loop with dependency long-polling support
      let iteration = 0;
      const longPollDeps = options?.longPollDeps === false ? false : true;
      // If long-polling, don't cap iterations unless a max-wait is configured
      const maxIterations = longPollDeps
        ? Number.MAX_SAFE_INTEGER
        : totalActionsCount * 3; // legacy safety limit
      const maxWaitMinutes = this?.timeoutSettings?.scheduler_max_wait_minutes;
      const longPollIntervalMs =
        Number(this?.timeoutSettings?.scheduler_long_poll_interval_ms) || 5000;
      const startedAtMs = Date.now();
      let consecutiveNoProgressIterations = 0;

      while (
        completedActions.size + failedActions.size < totalActionsCount &&
        iteration < maxIterations
      ) {
        iteration++;

        // Check for pause before proceeding
        await this.checkForPause(graphId);

        // Check and start ready actions up to capacity
        const startedCount = await checkAndStartReadyActions();

        if (startedCount === 0 && activePromises.size === 0) {
          // No actions started and none running
          const remaining = normalizedActions.filter((a) => {
            const actionId = a.id || a.action;
            return (
              !completedActions.has(actionId) && !failedActions.has(actionId)
            );
          });

          if (remaining.length > 0) {
            // If long-polling is enabled, wait and re-check dependencies
            if (longPollDeps) {
              // Optional absolute max-wait cutoff
              if (
                Number.isFinite(maxWaitMinutes) &&
                Date.now() - startedAtMs > maxWaitMinutes * 60_000
              ) {
                console.warn(
                  `[ExecutionService] Max wait minutes exceeded (${maxWaitMinutes}). Stopping long-poll.`
                );
                break;
              }

              try {
                console.log(
                  `[ExecutionService] Waiting for dependencies. Remaining:`,
                  remaining.map((a) => {
                    const actionId = a.id || a.action;
                    const deps = dependencies.get(actionId) || [];
                    const uncompleted = deps.filter(
                      (dep) => !completedActions.has(dep)
                    );
                    return `${actionId} (waiting for: ${uncompleted.join(
                      ", "
                    )})`;
                  })
                );
              } catch (_) {}

              await this.delay(longPollIntervalMs);
              continue; // re-check loop
            }

            // Legacy behavior: fail remaining to prevent infinite loop
            console.error(
              `[ExecutionService] Deadlock detected at iteration ${iteration}. Remaining actions:`,
              remaining.map((a) => {
                const actionId = a.id || a.action;
                const deps = dependencies.get(actionId) || [];
                const uncompletedDeps = deps.filter(
                  (dep) => !completedActions.has(dep)
                );
                return `${actionId} (waiting for: ${uncompletedDeps.join(
                  ", "
                )})`;
              })
            );
            remaining.forEach((action) => {
              const actionId = action.id || action.action;
              failedActions.add(actionId);
              this.updateNodeStatus(graphId, actionId, "failed", {
                reason: "deadlock_detected",
                iteration,
                remainingDependencies: (
                  dependencies.get(actionId) || []
                ).filter((dep) => !completedActions.has(dep)),
              });
            });
          }
          break;
        }

        // Track progress to detect infinite loops
        if (startedCount === 0) {
          consecutiveNoProgressIterations++;
          if (consecutiveNoProgressIterations > 10) {
            console.warn(
              `[ExecutionService] No progress for ${consecutiveNoProgressIterations} iterations. Active promises: ${activePromises.size}`
            );
          }
        } else {
          consecutiveNoProgressIterations = 0;
        }

        // Wait for at least one active promise to complete before checking again
        if (activePromises.size > 0) {
          try {
            // Create a stable snapshot of promises to avoid race conditions
            const promiseArray = Array.from(activePromises.values());
            await Promise.race(promiseArray);
          } catch (error) {
            console.error(`[ExecutionService] Error in Promise.race:`, error);
            // Continue execution even if one promise fails
          }
        }

        // Clear cascade block set for next iteration so status-gated checks take over
        if (this.nodesBlockedFromStartByCascade.has(graphId)) {
          this.nodesBlockedFromStartByCascade.delete(graphId);
        }

        // Stop on error if configured and we have failures
        if (options.stopOnError && failedActions.size > 0) {
          console.log(
            `[ExecutionService] Stopping execution due to error at iteration ${iteration}`
          );
          break;
        }
      }

      // Handle max iterations exceeded
      if (iteration >= maxIterations) {
        console.error(
          `[ExecutionService] Max iterations exceeded (${maxIterations}). Forcing completion.`
        );

        // Force fail remaining actions
        const remaining = normalizedActions.filter((a) => {
          const actionId = a.id || a.action;
          return (
            !completedActions.has(actionId) && !failedActions.has(actionId)
          );
        });

        remaining.forEach((action) => {
          const actionId = action.id || action.action;
          failedActions.add(actionId);
          this.updateNodeStatus(graphId, actionId, "failed", {
            reason: "max_iterations_exceeded",
            iteration: maxIterations,
          });
        });

        // Cancel any remaining active promises
        for (const actionId of activePromises.keys()) {
          this.cancelledActions.add(actionId);
        }
      }

      // Wait for any remaining active promises to complete
      if (activePromises.size > 0) {
        console.log(
          `[ExecutionService] Waiting for ${activePromises.size} remaining actions to complete`
        );
        await Promise.allSettled(Array.from(activePromises.values()));
      }

      // Update graph status
      const graphData = this.activeGraphs.get(graphId);
      const overallSuccess = failedActions.size === 0;
      graphData.status = overallSuccess ? "completed" : "failed";
      graphData.endTime = new Date();
      graphData.duration = graphData.endTime - graphData.startTime;

      // Cleanup tabs if configured
      await this.cleanupGraphTabs(graphId);

      // If nothing else is executing, keep the monitor running
      if (this.activeGraphs.size === 0 && this.nodeExecutions.size === 0) {
        console.log(
          "[ExecutionService] All graphs completed. Keeping login monitor running."
        );
      }

      console.log(
        `[ExecutionService] Node-by-node graph execution completed: ${graphId}, success: ${overallSuccess}`
      );
      console.log(
        `[ExecutionService] Final stats - Completed: ${completedActions.size}, Failed: ${failedActions.size}, Total: ${totalActionsCount}`
      );

      return {
        success: overallSuccess,
        graphId,
        results,
        summary: {
          total: totalActionsCount,
          completed: completedActions.size,
          failed: failedActions.size,
          duration: graphData.duration,
        },
      };
    } catch (error) {
      console.error(
        `[ExecutionService] Node-by-node graph execution failed: ${graphId}`,
        error
      );

      // Update graph status
      if (this.activeGraphs.has(graphId)) {
        const graphData = this.activeGraphs.get(graphId);
        graphData.status = "failed";
        graphData.error = error.message;
        graphData.endTime = new Date();
      }

      return {
        success: false,
        graphId,
        error: error.message,
        message: `Node-by-node graph execution failed: ${error.message}`,
      };
    }
  }

  async getSupportedActions() {
    try {
      const actionArgumentsConfig = await getActionArguments();
      return Object.keys(actionArgumentsConfig);
    } catch (error) {
      return [
        "BUY",
        "SELL",
        "PlaceBuyLimitOrder",
        "PlaceSellLimitOrder",
        "PlaceBuyStopLossMarketOrder",
        "PlaceSellStopLossMarketOrder",
        "MONITORPROFIT",
        "ExitAllPositions",
        "SellAll",
        "NavigateToProfile",
        "GetProfileInfo",
      ];
    }
  }

  async executeAction(actionType, arguments_, tabId) {
    try {
      const action = { action: actionType, arguments: arguments_ };
      const actionArgumentsConfig = await getActionArguments();

      const validation = await validateActions([action], actionArgumentsConfig);
      if (!validation.isValid) {
        return { success: false, message: validation.message };
      }

      const normalizedAction = this.normalizeActions(
        [action],
        actionArgumentsConfig
      )[0];
      const site = await this.findSiteForTab(tabId);

      return this.executeInTab(tabId, site, [normalizedAction]);
    } catch (error) {
      return {
        success: false,
        message: `Action execution failed: ${error.message}`,
      };
    }
  }

  async findSiteForTab(tabId) {
    const sites = await getSupportedSites();

    if (tabId) {
      try {
        const tab = await chrome.tabs.get(tabId);
        for (const [siteId, siteConfig] of Object.entries(sites)) {
          const siteUrl = siteConfig.url || siteConfig.urlPrefix;
          if (siteUrl && tab.url && tab.url.includes(siteUrl)) {
            return { ...siteConfig, siteId, url: siteUrl };
          }
        }
      } catch (error) {
        // Tab might not exist, use default
      }
    }

    // Use default site
    const defaultSite = Object.values(sites)[0];
    const siteUrl = defaultSite.url || defaultSite.urlPrefix;
    return { ...defaultSite, url: siteUrl };
  }

  async executeInCurrentTab(tabId, site, actions) {
    try {
      await this.ensureContentScript(tabId, site.contentScript);
      return this.sendMessageToTab(tabId, {
        type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
        actions,
      });
    } catch (error) {
      return { success: false, message: `Execution error: ${error.message}` };
    }
  }

  async executeInBackground(site, actions) {
    let newTab = null;

    try {
      newTab = await chrome.tabs.create({
        url: site.urlPrefix || site.url,
        active: false,
      });
      try {
        this.addTabToSmartAgentGroup(newTab.id, { collapse: true });
      } catch (_) {}

      await this.waitForTabLoad(newTab.id);
      await this.delay(DELAYS.PAGE_INITIALIZATION);
      await this.ensureContentScript(newTab.id, site.contentScript);

      // Send message and wait for response BEFORE closing tab
      const result = await this.sendMessageToTab(newTab.id, {
        type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
        actions,
      });

      // Close tab only AFTER getting the response
      const environment = await getEnvironment();
      const shouldCloseTabs = environment.close_tabs_after_execution !== false;

      if (shouldCloseTabs && newTab) {
        try {
          console.log(
            `[ExecutionService] Closing background tab ${newTab.id} after successful execution`
          );
          await chrome.tabs.remove(newTab.id);
          newTab = null; // Prevent double-closing in finally block
        } catch (error) {
          console.warn(
            `[ExecutionService] Failed to close tab ${newTab.id}:`,
            error.message
          );
        }
      }

      return result;
    } catch (error) {
      return {
        success: false,
        message: `Background execution error: ${error.message}`,
      };
    } finally {
      // Only close tab if it wasn't already closed and there was an error
      if (newTab) {
        const environment = await getEnvironment();
        const shouldCloseTabs =
          environment.close_tabs_after_execution !== false;

        if (shouldCloseTabs) {
          try {
            console.log(
              `[ExecutionService] Cleaning up tab ${newTab.id} after error`
            );
            await chrome.tabs.remove(newTab.id);
          } catch (error) {
            // Ignore close errors during cleanup
          }
        }
      }
    }
  }

  async executeInTab(tabId, site, actions) {
    const maxRetries = 2;
    let lastError = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        await this.waitForTabLoad(tabId);

        if (site.contentScript) {
          await this.ensureContentScript(tabId, site.contentScript);
        }

        return this.sendMessageToTab(tabId, {
          type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
          actions,
        });
      } catch (error) {
        lastError = error;

        // Check if tab still exists
        try {
          await chrome.tabs.get(tabId);
        } catch (tabError) {
          return {
            success: false,
            message: `Tab execution failed: Tab no longer exists (${error.message})`,
            error: "TAB_CLOSED",
          };
        }

        // If this is the last attempt, return the error
        if (attempt === maxRetries) {
          // Check if it's a message channel error
          if (
            error.message.includes("message channel closed") ||
            error.message.includes("asynchronous response") ||
            error.message.includes("Could not establish connection")
          ) {
            return {
              success: false,
              message: `Tab execution failed after ${
                maxRetries + 1
              } attempts: ${
                error.message
              }. The content script may not be ready or the tab was navigated.`,
              error: "MESSAGE_CHANNEL_ERROR",
            };
          }

          return {
            success: false,
            message: `Tab execution failed after ${maxRetries + 1} attempts: ${
              error.message
            }`,
          };
        }

        // Wait before retrying
        console.log(
          `[ExecutionService] Execution attempt ${
            attempt + 1
          } failed for tab ${tabId}, retrying in 1 second...`,
          error.message
        );
        await this.delay(1000);
      }
    }
  }

  // Consolidated content script injection
  async ensureContentScript(tabId, contentScript) {
    try {
      // First check if tab is accessible
      await chrome.tabs.get(tabId);

      const [checkResult] = await chrome.scripting.executeScript({
        target: { tabId },
        func: (identifier) => window[identifier] === true,
        args: [CONTENT_SCRIPT_IDENTIFIER],
        world: "ISOLATED",
      });

      if (!checkResult.result) {
        await chrome.scripting.executeScript({
          target: { tabId },
          files: [contentScript],
          world: "ISOLATED",
        });

        // Wait a bit for the content script to initialize
        await this.delay(500);
      }
    } catch (error) {
      const msg = error?.message || "";
      if (
        msg.includes("cannot be edited right now") ||
        msg.includes("user may be dragging") ||
        msg.includes("Tabs cannot be edited")
      ) {
        throw new Error(
          "EXECUTION_CANCELLED: Tab operation cancelled due to user interaction (cannot edit tab)"
        );
      }
      if (error.message.includes("Cannot access contents of url")) {
        throw new Error(ERROR_MESSAGES.TAB_NOT_ACCESSIBLE);
      }
      throw new Error(`Failed to inject content script: ${error.message}`);
    }
  }

  // Safely remove a problematic tab from tracking and try to close it
  async flushTabAndForget(tabId) {
    if (!tabId) return;
    try {
      await chrome.tabs.remove(tabId);
    } catch (e) {
      // Ignore removal errors (tab may already be gone/inaccessible)
    }
    this.activeTabs.delete(tabId);
    console.warn(`[ExecutionService] Flushed tab ${tabId} from activeTabs`);
  }

  waitForTabLoad(tabId) {
    return new Promise(async (resolve) => {
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.status === "complete") {
          resolve();
          return;
        }

        const listener = (updatedTabId, changeInfo) => {
          if (updatedTabId === tabId && changeInfo.status === "complete") {
            chrome.tabs.onUpdated.removeListener(listener);
            resolve();
          }
        };

        chrome.tabs.onUpdated.addListener(listener);
      } catch (error) {
        resolve(); // Continue even if tab check fails
      }
    });
  }

  sendMessageToTab(tabId, message) {
    return new Promise(async (resolve, reject) => {
      // Ensure timeout settings are loaded
      if (!this.timeoutSettings.execution_service_monitoring_timeout_seconds) {
        await this.loadTimeoutSettings();

        // Load configuration for parallel execution
        try {
          const response = await fetch(
            chrome.runtime.getURL("lib/shared-config.json")
          );
          const config = await response.json();
          if (config.EXECUTION_SERVICE) {
            this.maxConcurrentTabs =
              config.EXECUTION_SERVICE.max_concurrent_tabs || 5;
          }
        } catch (error) {
          console.warn(
            "[ExecutionService] Failed to load execution service config, using defaults:",
            error
          );
        }
      }

      console.log(
        `[ExecutionService] Sending message to tab ${tabId}:`,
        message
      );

      // Check if this is a monitoring operation that needs extended timeout
      const hasMonitoringAction =
        message.actions &&
        message.actions.some((action) =>
          [
            "MonitorConditionThenAct",
            "MONITORPROFIT",
            "MonitorSymbolFromWatchlist",
            "Monitor",
          ].includes(action.action)
        );

      // Use different timeout for monitoring vs regular actions from config
      const timeoutDuration = hasMonitoringAction
        ? this.timeoutSettings.execution_service_monitoring_timeout_seconds *
          1000
        : this.timeoutSettings.execution_service_general_timeout_seconds * 1000;

      console.log(
        `[ExecutionService] Using ${timeoutDuration / 1000}s timeout for ${
          hasMonitoringAction ? "monitoring" : "regular"
        } action`
      );

      // Add timeout to prevent hanging
      const timeout = setTimeout(() => {
        console.error(
          `[ExecutionService] Message timeout for tab ${tabId} after ${
            timeoutDuration / 1000
          }s`
        );
        reject(
          new Error(
            `Message timeout: No response received within ${
              timeoutDuration / 1000
            } seconds`
          )
        );
      }, timeoutDuration);

      chrome.tabs.sendMessage(tabId, message, (response) => {
        clearTimeout(timeout);

        if (chrome.runtime.lastError) {
          const errorMessage = chrome.runtime.lastError.message;
          console.error(
            `[ExecutionService] Chrome runtime error for tab ${tabId}:`,
            errorMessage
          );

          // Handle specific message channel closed error
          if (
            errorMessage.includes("message channel closed") ||
            errorMessage.includes("asynchronous response") ||
            errorMessage.includes("Could not establish connection")
          ) {
            console.error(
              `[ExecutionService] Message channel closed error detected`
            );
            reject(new Error(ERROR_MESSAGES.MESSAGE_CHANNEL_CLOSED));
          } else {
            reject(new Error(`Failed to send message: ${errorMessage}`));
          }
          return;
        }

        if (!response) {
          console.error(
            `[ExecutionService] No response received from tab ${tabId}`
          );
          reject(new Error(ERROR_MESSAGES.NO_RESPONSE));
          return;
        }

        console.log(
          `[ExecutionService] Received response from tab ${tabId}:`,
          response
        );
        resolve(response);
      });
    });
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * Build dependency map from actions, filtering out missing dependencies
   * @param {Array} actions - Actions with dependency information
   */
  buildDependencyMap(actions, candidateToId) {
    const dependencyMap = new Map(); // actionId -> [dependencies]
    const dependents = new Map(); // actionId -> [dependents]

    const resolveCandidate = (value) => {
      if (!value) return null;
      const k = String(value).toUpperCase();
      return candidateToId?.get(k) || null;
    };

    actions.forEach((action) => {
      const actionId = action.id;
      dependencyMap.set(actionId, []);
      dependents.set(actionId, []);

      if (!action.depends_on) {
        console.log(
          `[order over order][ExecutionService] Action '${actionId}' has no dependencies`
        );
        return;
      }

      // Normalize depends_on into an array of resolved IDs
      const rawDepends = Array.isArray(action.depends_on)
        ? action.depends_on
        : [action.depends_on];
      const resolved = [];
      console.log(
        `[order over order][ExecutionService] Processing dependencies for '${actionId}': [${rawDepends.join(
          ", "
        )}]`
      );

      for (const dep of rawDepends) {
        const resolvedId = resolveCandidate(dep);
        if (resolvedId) {
          resolved.push(resolvedId);
          console.log(
            `[order over order][ExecutionService] Dependency '${dep}' resolved to '${resolvedId}' for action '${actionId}'`
          );
        } else {
          console.log(
            `[ExecutionService] Missing dependency '${dep}' for action '${actionId}' - treating as independent`
          );
        }
      }

      dependencyMap.set(actionId, resolved);
      // Build reverse mapping
      resolved.forEach((depId) => {
        if (!dependents.has(depId)) dependents.set(depId, []);
        dependents.get(depId).push(actionId);
      });
    });

    console.log(
      `[order over order][ExecutionService] Final dependency mapping:`
    );
    for (const [actionId, deps] of dependencyMap.entries()) {
      if (deps.length > 0) {
        console.log(
          `[order over order][ExecutionService]   ${actionId} depends on: [${deps.join(
            ", "
          )}]`
        );
      }
    }
    console.log(
      `[order over order][ExecutionService] Final dependent mapping:`
    );
    for (const [parentId, deps] of dependents.entries()) {
      if (deps.length > 0) {
        console.log(
          `[order over order][ExecutionService]   ${parentId} has dependents: [${deps.join(
            ", "
          )}]`
        );
      }
    }

    return { dependencies: dependencyMap, dependents };
  }

  /**
   * Check if a monitor action has a non-price condition that should use a new tab
   * @param {Object} action - The monitor action to check
   * @returns {boolean} True if this is a non-price monitor condition
   */
  isNonPriceMonitorCondition(action) {
    try {
      // Extract condition from action arguments
      const condition = action?.arguments?.condition;
      if (!condition) {
        return false;
      }

      // Check if the condition observes something other than price
      const observationType = condition.observe;
      const isNonPrice = observationType && observationType !== "price";

      console.log(
        `[ExecutionService] Monitor condition check: observe=${observationType}, isNonPrice=${isNonPrice}`
      );
      return isNonPrice;
    } catch (error) {
      console.warn(
        `[ExecutionService] Error checking monitor condition type:`,
        error
      );
      return false;
    }
  }

  /**
   * Execute a single node with tab management and proper tab release
   * @param {string} graphId - Graph identifier
   * @param {Object} action - Action to execute
   * @param {Object} options - Execution options
   */

  async executeNodeWithTab(
    graphId,
    action,
    options = {},
    updateStatus = () => {}
  ) {
    const nodeId = action.id;
    let tabId = null;
    let tabInfo = null;

    console.log(`[ExecutionService] Starting node execution: ${nodeId}`);

    // Initialize node tracking
    this.nodeExecutions.set(nodeId, {
      status: "running",
      graphId,
      action,
      startTime: new Date(),
      tabId: null,
    });

    updateStatus(graphId, nodeId, "running", { action, graphId, nodeId });

    // Route monitoring actions to the dedicated observer tab (case-insensitive)
    const monitoringActions = new Set([
      "MONITORCONDITIONTHENACT",
      "MONITORPROFIT",
      "MONITORSYMBOLFROMWATCHLIST",
      "MONITOR",
    ]);
    const isMonitoringAction = monitoringActions.has(
      (action?.action || "").toString().toUpperCase()
    );
    console.log("isMonitoringAction =>", isMonitoringAction);

    // Check if this is a non-price monitor condition that should use a new tab
    const shouldUseNewTabForMonitor =
      isMonitoringAction && this.isNonPriceMonitorCondition(action);
    console.log("shouldUseNewTabForMonitor =>", shouldUseNewTabForMonitor);

    try {
      // Check if action was cancelled before starting
      if (this.cancelledActions.has(nodeId)) {
        throw new Error(`Action ${nodeId} was cancelled before execution`);
      }
      let siteForExecution = null;

      if (isMonitoringAction && !shouldUseNewTabForMonitor) {
        // Use dedicated observer tab for price-based monitors
        await this.ensureObserverMonitorTab();
        tabId = this.observerMonitor.tabId;
        const sites = await getSupportedSites();
        siteForExecution = Object.values(sites)[0];
      } else {
        // Get or create tab for this action (includes non-price monitors and regular actions)
        const tabResult = await this.getOrCreateTab(action, {
          ...options,
          reuseTab: shouldUseNewTabForMonitor ? false : options.reuseTab,
        });
        tabId = tabResult.tabId;
        tabInfo = this.activeTabs.get(tabId);
        siteForExecution = tabResult.site;
      }

      // Update node tracking with tab info
      const nodeExecution = this.nodeExecutions.get(nodeId);
      nodeExecution.tabId = tabId;

      const attemptExecuteOnTab = async (tid, site) => {
        // Cancel if tab is in cooldown due to a recent move/attach/detach
        if (this.isTabInCooldown && this.isTabInCooldown(tid)) {
          throw new Error("EXECUTION_CANCELLED: TAB_CONFLICT_RECENT_MOVE");
        }
        // Wait for the tab to fully load and settle before injecting/sending
        await this.waitForTabLoad(tid);
        await this.delay(DELAYS.PAGE_INITIALIZATION);

        // Ensure content script is injected
        await this.ensureContentScript(tid, site.contentScript);

        // Check again if action was cancelled during setup
        if (this.cancelledActions.has(nodeId)) {
          throw new Error(`Action ${nodeId} was cancelled during setup`);
        }

        // CRITICAL: Check login status right before execution
        await this.checkLoginBeforeExecution(tid, nodeId, graphId);

        // Execute the action
        return this.sendMessageToTab(tid, {
          type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
          actions: [action],
        });
      };

      let result;
      try {
        result = await attemptExecuteOnTab(tabId, siteForExecution);
        console.log("result from tab during execution =>", result);
      } catch (err) {
        const msg = err?.message || "";
        const isInaccessible =
          msg === ERROR_MESSAGES.TAB_NOT_ACCESSIBLE ||
          msg === ERROR_MESSAGES.MESSAGE_CHANNEL_CLOSED ||
          msg === ERROR_MESSAGES.NO_RESPONSE;

        if (isInaccessible) {
          console.warn(
            `[ExecutionService] Tab ${tabId} inaccessible (${msg}). Not retrying to avoid duplicate executions.`
          );
          throw err;
        } else {
          throw err;
        }
      }

      // Update node tracking with result
      nodeExecution.status = result.success ? "completed" : "failed";
      nodeExecution.result = result;
      nodeExecution.endTime = new Date();
      nodeExecution.duration = nodeExecution.endTime - nodeExecution.startTime;

      console.log(
        `[ExecutionService] Node execution completed: ${nodeId}, success: ${result.success}`
      );

      updateStatus(graphId, nodeId, result.success ? "completed" : "failed", {
        action,
        tabId,
        duration: nodeExecution.duration,
        resultDetail: result.results ? result.results[0] : result,
      });
      return {
        success: result.success,
        nodeId,
        action: action.action,
        result: result.results ? result.results[0] : result,
        tabId,
        duration: nodeExecution.duration,
      };
    } catch (error) {
      console.error(
        `[ExecutionService] Node execution failed: ${nodeId}`,
        error
      );

      // Update node tracking with error
      const nodeExecution = this.nodeExecutions.get(nodeId);
      const isCancellation = (error?.message || "").includes(
        "EXECUTION_CANCELLED"
      );
      const finalStatus = isCancellation ? "cancelled" : "failed";
      if (nodeExecution) {
        nodeExecution.status = finalStatus;
        nodeExecution.error = error.message;
        nodeExecution.endTime = new Date();
      }

      try {
        updateStatus(graphId, nodeId, finalStatus, {
          action,
          tabId: tabId,
          error: error.message,
          reason: isCancellation
            ? "Tab conflict / user interaction"
            : "Execution error",
        });
      } catch (_) {}

      return {
        success: false,
        nodeId,
        action: action.action,
        error: error.message,
        status: finalStatus,
        tabId: tabId,
      };
    } finally {
      // CRITICAL: Release tab for reuse (not for observer-monitor actions)
      if (
        !isMonitoringAction &&
        tabId &&
        tabInfo &&
        options.reuseTab !== false
      ) {
        if (tabInfo.status === "busy" && tabInfo.acquiredBy === nodeId) {
          tabInfo.status = "available";
          tabInfo.lastUsed = new Date();
          tabInfo.acquiredBy = null;
          console.log(`[ExecutionService] Released tab ${tabId} for reuse`);
        }
      }

      // Optionally close the tab after this node completes
      try {
        const environment = await getEnvironment();
        const shouldCloseTabs =
          environment.close_tabs_after_execution !== false;
        const wantsPerNodeClose =
          options.closeAfterNode === true || options.reuseTab === false;
        if (
          tabId &&
          shouldCloseTabs &&
          wantsPerNodeClose &&
          !(isMonitoringAction && !shouldUseNewTabForMonitor)
        ) {
          // Do not close the dedicated login monitor/observer tabs
          if (
            !(
              (this.loginMonitor &&
                this.loginMonitor.isDedicated &&
                this.loginMonitor.tabId === tabId) ||
              (this.observerMonitor &&
                this.observerMonitor.isDedicated &&
                this.observerMonitor.tabId === tabId)
            )
          ) {
            try {
              await chrome.tabs.remove(tabId);
            } catch (_) {}
            this.activeTabs.delete(tabId);
            console.log(
              `[ExecutionService] Closed tab ${tabId} after node ${nodeId}`
            );
          }
        }
      } catch (_) {
        /* ignore close errors */
      }

      // Cleanup cancelled action tracking
      this.cancelledActions.delete(nodeId);
    }
  }

  /**
   * Get or create tab for action execution with atomic reuse protection
   * @param {Object} action - Action to execute
   * @param {Object} options - Execution options
   */
  async getOrCreateTab(action, options = {}) {
    const sites = await getSupportedSites();
    let site = null;

    // Determine site for this action
    if (options.siteId) {
      site = sites[options.siteId];
    } else {
      // Use default site (Zerodha Kite)
      site = Object.values(sites)[0];
    }

    if (!site) {
      throw new Error("No supported site found for action execution");
    }

    const siteKey = site.url;

    // Ensure a lock exists for this site (required for both reuse and create paths)
    if (!this.tabAcquisitionLocks.has(siteKey)) {
      this.tabAcquisitionLocks.set(siteKey, Promise.resolve());
    }

    // Atomic tab acquisition with locks
    const reuseTab = options.reuseTab !== false;
    if (reuseTab) {
      // Wait for any existing acquisition to complete, then try to acquire
      const result = await this.tabAcquisitionLocks
        .get(siteKey)
        .then(async () => {
          // Atomic check-and-acquire
          for (const [tabId, tabInfo] of this.activeTabs) {
            if (
              tabInfo.site.url === site.url &&
              tabInfo.status === "available"
            ) {
              // Verify the tab still exists and is accessible
              try {
                await chrome.tabs.get(tabId);
              } catch (e) {
                // Stale/broken tab – flush it and continue
                await this.flushTabAndForget(tabId);
                continue;
              }

              // Double-check and atomically set to busy
              if (tabInfo.status === "available") {
                tabInfo.status = "busy";
                tabInfo.acquiredBy = action.id || action.action;
                tabInfo.lastAcquired = new Date();
                console.log(
                  `[ExecutionService] Reusing existing tab: ${tabId} for action: ${
                    action.id || action.action
                  }`
                );
                try {
                  this.addTabToSmartAgentGroup(tabId, { collapse: false });
                } catch (_) {}
                return { tabId, site, reused: true };
              }
            }
          }
          return null; // No available tab found
        });

      if (result) {
        return result;
      }
    }

    // Check concurrent tab limit
    if (this.activeTabs.size >= this.maxConcurrentTabs) {
      throw new Error(
        `Maximum concurrent tabs (${this.maxConcurrentTabs}) reached. Consider increasing limit or enabling tab reuse.`
      );
    }

    // Create new tab with lock protection
    const createTabPromise = this.tabAcquisitionLocks
      .get(siteKey)
      .then(async () => {
        console.log(
          `[ExecutionService] Creating new tab for site: ${site.url}`
        );
        const newTab = await chrome.tabs.create({
          url: site.url,
          active: false,
        });

        // Track the new tab
        this.activeTabs.set(newTab.id, {
          tab: newTab,
          site,
          status: "busy",
          created: new Date(),
          acquiredBy: action.id || action.action,
          actions: [],
        });
        try {
          this.addTabToSmartAgentGroup(newTab.id, { collapse: false });
        } catch (_) {}

        // Wait for tab to load
        await this.waitForTabLoad(newTab.id);
        await this.delay(DELAYS.PAGE_INITIALIZATION);

        console.log(
          `[ExecutionService] New tab created and loaded: ${newTab.id}`
        );

        return { tabId: newTab.id, site, reused: false };
      });

    // Update the lock for this site
    this.tabAcquisitionLocks.set(siteKey, createTabPromise);

    return await createTabPromise;
  }

  /**
   * Update node status in graph
   * @param {string} graphId - Graph identifier
   * @param {string} nodeId - Node identifier
   * @param {string} status - New status
   * @param {Object} data - Additional data
   */
  updateNodeStatus(graphId, nodeId, status, data = {}) {
    if (this.activeGraphs.has(graphId)) {
      const graph = this.activeGraphs.get(graphId);
      graph.nodes.set(nodeId, {
        status,
        data,
        timestamp: new Date(),
      });

      // Update progress counters
      if (status === "completed") {
        graph.progress.completed++;
      } else if (status === "failed") {
        graph.progress.failed++;
      }
    }
  }

  /**
   * Fail dependent actions when a dependency fails
   * @param {string} graphId - Graph identifier
   * @param {string} failedNodeId - Node that failed
   * @param {Map} dependents - Dependents map
   */
  failDependentActions(graphId, failedNodeId, dependents) {
    const toFail = dependents.get(failedNodeId) || [];
    const processed = new Set();

    const failRecursively = (nodeId) => {
      if (processed.has(nodeId)) return;
      processed.add(nodeId);

      // Cancel if currently running
      this.cancelledActions.add(nodeId);
      console.log(
        `[ExecutionService] Cancelling running action due to dependency failure: ${nodeId}`
      );

      this.updateNodeStatus(graphId, nodeId, "failed", {
        reason: "dependency_failed",
        failedDependency: failedNodeId,
        timestamp: new Date(),
      });

      // Fail this node's dependents too
      const nodeDependents = dependents.get(nodeId) || [];
      nodeDependents.forEach(failRecursively);
    };

    console.log(
      `[ExecutionService] Failing ${toFail.length} dependent actions for failed node: ${failedNodeId}`
    );
    toFail.forEach(failRecursively);
  }

  /**
   * Cancel all child nodes when parent is cancelled and update PouchDB documents
   * @param {string} graphId - Graph identifier
   * @param {string} parentNodeId - Parent node that was cancelled
   * @param {Map} dependents - Dependents map
   * @param {Array} actions - Original actions array for context
   */
  async cancelAllChildNodesAndUpdateDocs(
    graphId,
    parentNodeId,
    dependents,
    actions = []
  ) {
    console.log("[Cancel Order P] cancelAllChildNodesAndUpdateDocs start", {
      graphId,
      parentNodeId,
      dependentsSize: dependents ? dependents.size : 0,
      actionsCount: Array.isArray(actions) ? actions.length : 0,
    });
    const toBulkCancel = [];
    const processed = new Set();

    // Helper function to determine if an action is an ACT type
    const isACTAction = (nodeId) => {
      const action = actions.find((a) => (a.id || a.action) === nodeId);
      const actionType = (action?.action || "").toString().toUpperCase();
      const actActions = [
        "MONITORCONDITIONTHENACT",
        "MONITORPROFIT",
        "MONITORSYMBOLFROMWATCHLIST",
        "MONITOR",
      ];
      return actActions.includes(actionType);
    };

    // Ensure parent doc is also updated as cancelled/stopped
    try {
      const parentIsACT = isACTAction(parentNodeId);
      toBulkCancel.push({
        nodeId: parentNodeId,
        isACT: parentIsACT,
        docPrefix: parentIsACT ? "monitor_" : "order_",
        newStatus: parentIsACT ? "stopped" : "cancelled",
      });
      console.log(
        "[Cancel Order P] [ExecutionService] Prepared parent for bulk cancel",
        {
          nodeId: parentNodeId,
          isACT: parentIsACT,
          docPrefix: parentIsACT ? "monitor_" : "order_",
          newStatus: parentIsACT ? "stopped" : "cancelled",
        }
      );
    } catch (_) {}

    const cancelRecursively = (nodeId, reason = "parent_cancelled") => {
      if (processed.has(nodeId)) return;
      processed.add(nodeId);

      // Cancel if currently running
      this.cancelledActions.add(nodeId);
      console.log(
        `[ExecutionService] Cancelling child node due to parent cancellation: ${nodeId}`
      );

      // Update local status
      this.updateNodeStatus(graphId, nodeId, "cancelled", {
        reason: reason,
        cancelledParent: parentNodeId,
        timestamp: new Date(),
      });

      // Prepare for bulk PouchDB update
      const isACT = isACTAction(nodeId);
      console.log("[Cancel Order P] [ExecutionService] Child ACT detection", {
        nodeId,
        isACT,
      });
      toBulkCancel.push({
        nodeId,
        isACT,
        docPrefix: isACT ? "monitor_" : "order_",
        newStatus: isACT ? "stopped" : "cancelled",
      });

      // Cancel this node's dependents too
      const nodeDependents = dependents.get(nodeId) || [];
      nodeDependents.forEach((childNodeId) =>
        cancelRecursively(childNodeId, "ancestor_cancelled")
      );
    };

    // Start cancellation cascade
    const toCancel = dependents.get(parentNodeId) || [];
    console.log(
      `[ExecutionService] Cancelling ${toCancel.length} child nodes for cancelled parent: ${parentNodeId}`
    );
    try {
      console.log("[Cancel Order P] [ExecutionService] Dependents list", {
        parentNodeId,
        dependents: toCancel,
      });
    } catch (_) {}
    toCancel.forEach((nodeId) => cancelRecursively(nodeId));

    // Bulk update PouchDB documents if there are any to cancel
    if (toBulkCancel.length > 0) {
      try {
        try {
          console.log(
            "[Cancel Order P][ExecutionService] Bulk cancel payload preview",
            { count: toBulkCancel.length, nodes: toBulkCancel }
          );
        } catch (_) {}
        // Pre-flag all nodes in this cascade to prevent them from starting this tick
        if (!this.nodesBlockedFromStartByCascade.has(graphId)) {
          this.nodesBlockedFromStartByCascade.set(graphId, new Set());
        }
        const blockedSet = this.nodesBlockedFromStartByCascade.get(graphId);
        toBulkCancel.forEach((n) => blockedSet.add(n.nodeId));

        await this.sendBulkCancelMessage(graphId, toBulkCancel);
        console.log(
          `[ExecutionService] Sent bulk cancel message for ${toBulkCancel.length} nodes`
        );
      } catch (error) {
        console.error(
          `[ExecutionService] Failed to send bulk cancel message:`,
          error
        );
      }
    }

    return toBulkCancel;
  }

  /**
   * Send bulk cancel message to background script for PouchDB updates
   * @param {string} graphId - Graph identifier
   * @param {Array} nodesToCancel - Array of {nodeId, isACT, docPrefix, newStatus}
   */
  async sendBulkCancelMessage(graphId, nodesToCancel) {
    try {
      console.log("[Cancel Order P][ExecutionService] sendBulkCancelMessage", {
        graphId,
        nodesCount: Array.isArray(nodesToCancel) ? nodesToCancel.length : 0,
      });
    } catch (_) {}
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(
        {
          type: "POUCHDB_BULK_CANCEL",
          graphId,
          nodes: nodesToCancel,
        },
        (response) => {
          try {
            console.log(
              "[Cancel Order P][ExecutionService] sendBulkCancelMessage response",
              response
            );
          } catch (_) {}
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
          } else if (response && response.success) {
            resolve(response);
          } else {
            reject(new Error(response?.error || "Bulk cancel failed"));
          }
        }
      );
    });
  }

  /**
   * Cancel a specific node and all its dependents
   * @param {string} graphId - Graph identifier
   * @param {string} nodeId - Node to cancel
   * @param {Array} actions - Actions array for context
   * @param {string} reason - Reason for cancellation
   */
  async cancelNodeAndDependents(
    graphId,
    nodeId,
    actions = [],
    reason = "user_cancelled"
  ) {
    console.log(
      `[ExecutionService] Cancelling node ${nodeId} and its dependents. Reason: ${reason}`
    );

    // Build dependents map from actions
    const dependents = new Map();
    actions.forEach((action) => {
      const actionId = action.id || action.action;
      const deps = action.dependencies || [];
      deps.forEach((dep) => {
        if (!dependents.has(dep)) {
          dependents.set(dep, []);
        }
        dependents.get(dep).push(actionId);
      });
    });

    try {
      console.log(
        "[Cancel Order P][ExecutionService] Built dependents map for cancellation",
        {
          nodeId,
          dependentsSize: dependents.size,
          directDependents: dependents.get(nodeId) || [],
        }
      );
    } catch (_) {}

    // Cancel the node itself
    this.cancelledActions.add(nodeId);
    this.updateNodeStatus(graphId, nodeId, "cancelled", {
      reason: reason,
      timestamp: new Date(),
    });

    // Cancel all dependent nodes and update their documents
    const cancelledNodes = await this.cancelAllChildNodesAndUpdateDocs(
      graphId,
      nodeId,
      dependents,
      actions
    );

    console.log(
      `[ExecutionService] Cancelled node ${nodeId} and ${cancelledNodes.length} dependents`
    );
    return { success: true, cancelledNodes: cancelledNodes.length + 1 };
  }

  /**
   * Cleanup tabs used by a graph
   * @param {string} graphId - Graph identifier
   */
  async cleanupGraphTabs(graphId) {
    const environment = await getEnvironment();
    const shouldCloseTabs = environment.close_tabs_after_execution !== false;

    if (!shouldCloseTabs) {
      console.log(
        `[ExecutionService] Tab cleanup disabled for graph: ${graphId}`
      );
      return;
    }

    console.log(`[ExecutionService] Cleaning up tabs for graph: ${graphId}`);

    // Find tabs used by this graph's nodes
    const tabsToClose = new Set();
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId && execution.tabId) {
        tabsToClose.add(execution.tabId);
      }
    }

    // Close tabs
    for (const tabId of tabsToClose) {
      if (
        (tabId === this.loginMonitor.tabId && this.loginMonitor.isDedicated) ||
        (tabId === this.observerMonitor.tabId &&
          this.observerMonitor.isDedicated)
      ) {
        console.log(
          "[ExecutionService] Skipping monitor/observer tab during cleanup:",
          tabId
        );
        continue;
      }
      try {
        await chrome.tabs.remove(tabId);
        this.activeTabs.delete(tabId);
        console.log(`[ExecutionService] Closed tab: ${tabId}`);
      } catch (error) {
        console.warn(
          `[ExecutionService] Failed to close tab ${tabId}:`,
          error.message
        );
      }
    }

    // Cleanup node executions for this graph
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId) {
        this.nodeExecutions.delete(nodeId);
      }
    }

    // Remove graph tracking
    this.activeGraphs.delete(graphId);

    // If no graphs remain and no nodes are running, keep the monitor running
    if (this.activeGraphs.size === 0 && this.nodeExecutions.size === 0) {
      console.log(
        "[ExecutionService] No active graphs or nodes remaining. Keeping login monitor running."
      );
    }
  }

  /**
   * Get execution status for a graph
   * @param {string} graphId - Graph identifier
   */
  getGraphStatus(graphId) {
    const graph = this.activeGraphs.get(graphId);
    if (!graph) {
      return { found: false };
    }

    const nodeStatuses = [];
    for (const [nodeId, execution] of this.nodeExecutions) {
      if (execution.graphId === graphId) {
        nodeStatuses.push({
          nodeId,
          status: execution.status,
          action: execution.action.action,
          tabId: execution.tabId,
          duration: execution.duration,
          startTime: execution.startTime,
          endTime: execution.endTime,
        });
      }
    }

    return {
      found: true,
      graphId,
      status: graph.status,
      progress: graph.progress,
      nodeStatuses,
      startTime: graph.startTime,
      endTime: graph.endTime,
      duration: graph.duration,
    };
  }

  /**
   * Get status of all active executions
   */
  getAllExecutionStatus() {
    const graphStatuses = [];
    for (const graphId of this.activeGraphs.keys()) {
      graphStatuses.push(this.getGraphStatus(graphId));
    }

    return {
      activeGraphs: graphStatuses.length,
      activeTabs: this.activeTabs.size,
      activeNodes: this.nodeExecutions.size,
      pausedGraphs: this.pausedGraphs.size,
      graphs: graphStatuses,
      tabs: Array.from(this.activeTabs.entries()).map(([tabId, info]) => ({
        tabId,
        url: info.site.url,
        status: info.status,
        created: info.created,
      })),
      login: {
        required: this.loginMonitor.required,
        lastChecked: this.loginMonitor.lastChecked,
        monitorTabId: this.loginMonitor.tabId,
        pausedByLogin: Array.from(this.loginMonitor.pausedGraphsSet),
      },
    };
  }

  /**
   * Pause a graph execution
   * @param {string} graphId - Graph identifier
   */
  pauseGraph(graphId) {
    try {
      const graph = this.activeGraphs.get(graphId);
      if (!graph) {
        return { success: false, message: `Graph '${graphId}' not found` };
      }

      if (graph.status !== "running") {
        return {
          success: false,
          message: `Graph '${graphId}' is not running (status: ${graph.status})`,
        };
      }

      if (this.pausedGraphs.has(graphId)) {
        return {
          success: false,
          message: `Graph '${graphId}' is already paused`,
        };
      }

      // Mark graph as paused
      this.pausedGraphs.add(graphId);
      graph.status = "paused";
      graph.pausedAt = new Date();

      console.log(`[ExecutionService] Graph paused: ${graphId}`);

      return {
        success: true,
        message: `Graph '${graphId}' paused successfully`,
        pausedAt: graph.pausedAt,
      };
    } catch (error) {
      console.error(
        `[ExecutionService] Error pausing graph ${graphId}:`,
        error
      );
      return {
        success: false,
        message: `Failed to pause graph: ${error.message}`,
      };
    }
  }

  /**
   * Resume a paused graph execution
   * @param {string} graphId - Graph identifier
   */
  resumeGraph(graphId) {
    try {
      const graph = this.activeGraphs.get(graphId);
      if (!graph) {
        return { success: false, message: `Graph '${graphId}' not found` };
      }

      if (graph.status !== "paused") {
        return {
          success: false,
          message: `Graph '${graphId}' is not paused (status: ${graph.status})`,
        };
      }

      if (!this.pausedGraphs.has(graphId)) {
        return {
          success: false,
          message: `Graph '${graphId}' is not in paused state`,
        };
      }

      // Resume graph
      this.pausedGraphs.delete(graphId);
      graph.status = "running";
      graph.resumedAt = new Date();

      // Calculate pause duration
      if (graph.pausedAt) {
        graph.pauseDuration =
          (graph.pauseDuration || 0) + (graph.resumedAt - graph.pausedAt);
      }

      // Resume execution by resolving the pause promise
      const resolver = this.pauseResolvers.get(graphId);
      if (resolver) {
        resolver();
        this.pauseResolvers.delete(graphId);
      }

      console.log(`[ExecutionService] Graph resumed: ${graphId}`);

      return {
        success: true,
        message: `Graph '${graphId}' resumed successfully`,
        resumedAt: graph.resumedAt,
        totalPauseDuration: graph.pauseDuration || 0,
      };
    } catch (error) {
      console.error(
        `[ExecutionService] Error resuming graph ${graphId}:`,
        error
      );
      return {
        success: false,
        message: `Failed to resume graph: ${error.message}`,
      };
    }
  }

  /**
   * Check if execution should pause and wait if needed
   * @param {string} graphId - Graph identifier
   */
  async checkForPause(graphId) {
    if (this.pausedGraphs.has(graphId)) {
      console.log(
        `[ExecutionService] Graph ${graphId} is paused, waiting for resume...`
      );

      // Create a promise that resolves when the graph is resumed
      return new Promise((resolve) => {
        // If already resumed, resolve immediately
        if (!this.pausedGraphs.has(graphId)) {
          resolve();
          return;
        }

        // Store the resolver to be called when resuming
        this.pauseResolvers.set(graphId, resolve);
      });
    }
  }

  // ==================== LOGIN MONITORING SYSTEM ====================
  updatePrevSummary(summary) {
    try {
      if (!summary) return;
      console.log("[ExecutionService] Updating previous summary:", {
        open: {
          count: summary.open?.count,
          firstKey: !!summary.open?.firstKey,
          lastKey: !!summary.open?.lastKey,
        },
        completed: {
          count: summary.completed?.count,
          firstKey: !!summary.completed?.firstKey,
          lastKey: !!summary.completed?.lastKey,
        },
      });
      this.loginMonitor.orderSummary.open =
        summary.open || this.loginMonitor.orderSummary.open;
      this.loginMonitor.orderSummary.completed =
        summary.completed || this.loginMonitor.orderSummary.completed;
    } catch (_) {}
  }

  // Build a stable key from an order object to match content-script summary keys
  buildOrderKey(order, section) {
    try {
      // Map our stored order shape to summary key fields
      const symbol = order?.symbol || "";
      const type = order?.type || "";
      const exchange = order?.exchange || "";
      const product = order?.product || "";
      // Quantity string: prefer the raw string if present, else reconstruct
      let quantityStr = "";
      if (order?.quantity && typeof order.quantity === "string") {
        quantityStr = order.quantity;
      } else if (order?.quantity && typeof order.quantity === "object") {
        const filled = Number.isFinite(order.quantity.filled)
          ? order.quantity.filled
          : 0;
        const total = Number.isFinite(order.quantity.total)
          ? order.quantity.total
          : filled;
        quantityStr = `${filled}/${total}`;
      }
      const status = order?.status || "";
      const time = order?.time || "";
      // Price selection mirrors summary: average-price if available else last-price
      let priceStr = "";
      if (section === "completed") {
        priceStr =
          (order?.avgPrice != null ? String(order.avgPrice) : "") || "";
      } else {
        // open: choose price if present else ltp
        const price = order?.price != null ? order.price : "";
        const ltp = order?.ltp != null ? order.ltp : "";
        priceStr = String(price !== "" ? price : ltp);
      }
      return [
        symbol,
        type,
        exchange,
        product,
        quantityStr,
        status,
        time,
        priceStr,
      ].join("|");
    } catch {
      return "";
    }
  }

  // Trim a list of orders to the new boundary [firstKey..lastKey]
  trimOrdersByBoundary(orders, newFirstKey, newLastKey, section) {
    if (!Array.isArray(orders) || orders.length === 0) return [];
    const keys = orders.map((o) => this.buildOrderKey(o, section));
    let startIdx = 0;
    let endIdx = orders.length - 1;
    if (newFirstKey) {
      const idx = keys.indexOf(newFirstKey);
      if (idx >= 0) startIdx = idx;
    }
    if (newLastKey) {
      const idx = keys.lastIndexOf(newLastKey);
      if (idx >= 0) endIdx = idx;
    }
    if (startIdx > endIdx) return [];
    return orders.slice(startIdx, endIdx + 1);
  }

  // Store previous order data for reuse when early-exiting
  storePreviousOrderData(openOrders, completedOrders) {
    try {
      if (!this.loginMonitor.previousOrderData) {
        this.loginMonitor.previousOrderData = { open: null, completed: null };
      }

      if (openOrders && Array.isArray(openOrders)) {
        this.loginMonitor.previousOrderData.open = openOrders;
      }

      if (completedOrders && Array.isArray(completedOrders)) {
        this.loginMonitor.previousOrderData.completed = completedOrders;
      }

      console.log("[ExecutionService] Stored previous order data for reuse:", {
        openCount: this.loginMonitor.previousOrderData.open?.length || 0,
        completedCount:
          this.loginMonitor.previousOrderData.completed?.length || 0,
      });
    } catch (_) {}
  }

  // Get stored previous order data when early-exiting
  getPreviousOrderData() {
    try {
      return (
        this.loginMonitor.previousOrderData || { open: null, completed: null }
      );
    } catch (_) {
      return { open: null, completed: null };
    }
  }

  shouldEarlyExitFromSummary(summary) {
    try {
      if (!summary) return false;
      const prev = this.loginMonitor.orderSummary;

      const check = (curr, p) => {
        const firstRun = p.count === null;
        if (firstRun) return { unchanged: false, mismatch: false };
        const sameCount = curr.count === p.count;
        // Since orders are always sorted, just check first and last keys
        const sameBoundary =
          curr.firstKey === p.firstKey && curr.lastKey === p.lastKey;
        if (sameCount && sameBoundary)
          return { unchanged: true, mismatch: false };
        if (sameCount && !sameBoundary)
          return { unchanged: false, mismatch: true };
        return { unchanged: false, mismatch: false };
      };

      const o = check(summary.open || {}, prev.open);
      const c = check(summary.completed || {}, prev.completed);

      console.log("[ExecutionService] Early-exit decision:", {
        prevOpen: prev.open,
        currOpen: summary.open,
        prevCompleted: prev.completed,
        currCompleted: summary.completed,
        checkOpen: o,
        checkCompleted: c,
        mismatchStreak: this.loginMonitor.stabilize.mismatchStreak,
      });

      const allUnchanged = o.unchanged && c.unchanged;
      if (allUnchanged) {
        this.loginMonitor.stabilize.mismatchStreak = 0;
        // console.log(
        //   "[ExecutionService] Early exit: summaries unchanged - orders are the same"
        // );
        return true;
      }

      const anyMismatch = o.mismatch || c.mismatch;
      if (anyMismatch) {
        this.loginMonitor.stabilize.mismatchStreak++;
        console.log(
          "[ExecutionService] Summary mismatch with same counts. Streak =",
          this.loginMonitor.stabilize.mismatchStreak
        );
        if (
          this.loginMonitor.stabilize.mismatchStreak <
          this.loginMonitor.stabilize.maxStreak
        ) {
          // Treat as transient jank; skip heavy fetch this tick
          console.log(
            "[ExecutionService] Skipping heavy fetch this tick due to transient mismatch"
          );
          return true;
        }
      }

      this.loginMonitor.stabilize.mismatchStreak = 0;
      console.log(
        "[ExecutionService] Proceeding to heavy fetch: change detected or stabilized mismatches"
      );
      return false;
    } catch (_) {
      return false;
    }
  }

  /**
   * Get Kite base URL from config
   * @returns {Promise<string>} Base URL for Kite
   */
  async getKiteBaseUrl() {
    try {
      const res = await fetch(chrome.runtime.getURL("lib/shared-config.json"));
      const cfg = await res.json();
      const site =
        cfg?.SUPPORTED_SITES && Object.values(cfg.SUPPORTED_SITES)[0];
      return site?.urlPrefix || site?.url || "https://kite.zerodha.com/";
    } catch {
      return "https://kite.zerodha.com/";
    }
  }

  /**
   * Get orders page URL
   * @returns {Promise<string>} Orders page URL
   */
  async getOrdersPageUrl() {
    const base = await this.getKiteBaseUrl();
    return new URL("orders", base).toString();
  }

  /**
   * Get dashboard/home URL (base URL) for observer tab
   */
  async getDashboardUrl() {
    return await this.getKiteBaseUrl();
  }

  /**
   * Ensure a tab is grouped under the Aagman Monitors tab group
   */
  async addTabToSmartAgentGroup(tabId, options = {}) {
    const collapseGroup = options.collapse !== false; // default true
    try {
      if (!tabId) return;
      try {
        const env = await getEnvironment();
        if (env && env.disable_tab_grouping === true) return;
      } catch (_) {}
      if (this.isTabInCooldown && this.isTabInCooldown(tabId)) {
        console.warn(
          "[ExecutionService] Skipping grouping due to recent tab move/attach/detach"
        );
        return;
      }
      // Unpin first — pinned tabs cannot be grouped
      try {
        await chrome.tabs.update(tabId, { pinned: false });
      } catch (e) {
        const m = e?.message || "";
        if (
          m.includes("cannot be edited right now") ||
          m.includes("user may be dragging") ||
          m.includes("Tabs cannot be edited")
        ) {
          console.warn(
            "[ExecutionService] Tab unpin skipped due to user interaction"
          );
          return;
        }
      }

      const ensureGrouped = async () => {
        let existingGroupId = null;
        try {
          const groups = await chrome.tabGroups.query({
            title: "Aagman Monitors",
          });
          if (Array.isArray(groups) && groups.length > 0) {
            existingGroupId = groups[0].id;
          }
        } catch (_) {}

        let groupIdUsed = existingGroupId;
        if (groupIdUsed != null) {
          try {
            await chrome.tabs.group({ tabIds: tabId, groupId: groupIdUsed });
          } catch (e) {
            const m = e?.message || "";
            if (
              m.includes("cannot be edited right now") ||
              m.includes("user may be dragging") ||
              m.includes("Tabs cannot be edited")
            ) {
              console.warn(
                "[ExecutionService] Tab grouping skipped due to user interaction"
              );
              return;
            }
          }
        } else {
          try {
            groupIdUsed = await chrome.tabs.group({ tabIds: tabId });
          } catch (e) {
            const m = e?.message || "";
            if (
              m.includes("cannot be edited right now") ||
              m.includes("user may be dragging") ||
              m.includes("Tabs cannot be edited")
            ) {
              console.warn(
                "[ExecutionService] Tab grouping skipped due to user interaction"
              );
              return;
            }
          }
        }

        if (groupIdUsed != null) {
          try {
            await chrome.tabGroups.update(groupIdUsed, {
              title: "Aagman Monitors",
              color: "purple",
              collapsed: collapseGroup,
            });
          } catch (e) {
            const m = e?.message || "";
            if (
              m.includes("cannot be edited right now") ||
              m.includes("user may be dragging") ||
              m.includes("Tabs cannot be edited")
            ) {
              console.warn(
                "[ExecutionService] Tab group update skipped due to user interaction"
              );
              return;
            }
          }
          if (collapseGroup) {
            try {
              setTimeout(() => {
                try {
                  chrome.tabGroups.update(groupIdUsed, { collapsed: true });
                } catch (e) {
                  const m = e?.message || "";
                  if (
                    m.includes("cannot be edited right now") ||
                    m.includes("user may be dragging") ||
                    m.includes("Tabs cannot be edited")
                  ) {
                    console.warn(
                      "[ExecutionService] Tab group collapse skipped due to user interaction"
                    );
                  }
                }
              }, 250);
            } catch (_) {}
          }
        }
      };

      // Best effort once; do not retry to avoid duplicates or conflicts
      try {
        await ensureGrouped();
      } catch (e) {
        const m = e?.message || "";
        if (
          m.includes("cannot be edited right now") ||
          m.includes("user may be dragging") ||
          m.includes("Tabs cannot be edited")
        ) {
          console.warn(
            "[ExecutionService] Tab grouping completely skipped due to user interaction"
          );
        }
      }
    } catch (_) {}
  }

  /**
   * Ensure login monitor tab exists and is ready
   */
  async ensureLoginMonitorTab() {
    const ordersUrl = await this.getOrdersPageUrl();

    // Reuse if exists
    if (this.loginMonitor.tabId) {
      try {
        await chrome.tabs.get(this.loginMonitor.tabId);
      } catch {
        this.loginMonitor.tabId = null;
      }
    }

    // Try reusing stored tabId from previous sessions to avoid duplicates
    if (!this.loginMonitor.tabId) {
      try {
        const data = await new Promise((resolve) =>
          chrome.storage?.local?.get?.(["loginMonitorTabId"], resolve)
        );
        const storedId = data?.loginMonitorTabId;
        if (storedId) {
          try {
            const existing = await chrome.tabs.get(storedId);
            if (existing && existing.id) {
              this.loginMonitor.tabId = existing.id;
              this.loginMonitor.isDedicated = true;
            }
          } catch (_) {
            // stored id invalid, clear it
            try {
              chrome.storage?.local?.remove?.("loginMonitorTabId");
            } catch (_) {}
          }
        }
      } catch (_) {}
    }

    // IMPORTANT: Always create a dedicated monitor tab to avoid touching user's current tab
    // Do not adopt existing tabs; keep this tab pinned and inactive
    // Guard against concurrent calls with a creation promise
    if (!this.loginMonitor.tabId) {
      if (!this.loginMonitor.creationPromise) {
        this.loginMonitor.creationPromise = chrome.tabs
          .create({ url: ordersUrl, pinned: false, active: false })
          .then(async (tab) => {
            this.loginMonitor.tabId = tab.id;
            this.loginMonitor.isDedicated = true;
            try {
              chrome.storage?.local?.set?.({ loginMonitorTabId: tab.id });
            } catch (_) {}
            try {
              await this.addTabToSmartAgentGroup(tab.id);
            } catch (_) {}
          })
          .catch(() => {})
          .finally(() => {
            this.loginMonitor.creationPromise = null;
          });
      }
      await this.loginMonitor.creationPromise;
    }

    // Ensure content script is ready
    const sites = await getSupportedSites();
    const site = Object.values(sites)[0];
    await this.waitForTabLoad(this.loginMonitor.tabId);
    await this.ensureContentScript(this.loginMonitor.tabId, site.contentScript);
    await this.sendMessageToTab(this.loginMonitor.tabId, {
      type: "PERFORM_SITE_ACTIONS",
      actions: [
        {
          action: "ShowMonitoringBanner",
          arguments: {
            text: "SmartAgent: Login Monitor (do not close)",
            bg: "#0b3d91",
            fg: "#ffffff",
          },
        },
      ],
    }).catch(() => {});
    try {
      await this.addTabToSmartAgentGroup(this.loginMonitor.tabId);
    } catch (_) {}
  }

  /**
   * Ensure dedicated observer tab exists (separate from login monitor)
   * This tab hosts all MutationObservers and should not be reloaded periodically.
   */
  async ensureObserverMonitorTab() {
    const dashboardUrl = await this.getDashboardUrl();

    if (this.observerMonitor.tabId) {
      try {
        const existing = await chrome.tabs.get(this.observerMonitor.tabId);
        // If tab exists but is already in our group, reuse it; else we still reuse it
        if (!existing) this.observerMonitor.tabId = null;
      } catch {
        this.observerMonitor.tabId = null;
      }
    }

    // Try reusing stored observer tabId first to avoid duplicates
    if (!this.observerMonitor.tabId) {
      try {
        const data = await new Promise((resolve) =>
          chrome.storage?.local?.get?.(["observerMonitorTabId"], resolve)
        );
        const storedId = data?.observerMonitorTabId;
        if (storedId) {
          try {
            const existing = await chrome.tabs.get(storedId);
            if (existing && existing.id) {
              this.observerMonitor.tabId = existing.id;
              this.observerMonitor.isDedicated = true;
            }
          } catch (_) {
            // stored id invalid, clear it
            try {
              chrome.storage?.local?.remove?.("observerMonitorTabId");
            } catch (_) {}
          }
        }
      } catch (_) {}
    }

    if (!this.observerMonitor.tabId) {
      if (!this.observerMonitor.creationPromise) {
        this.observerMonitor.creationPromise = chrome.tabs
          .create({ url: dashboardUrl, pinned: false, active: false })
          .then(async (tab) => {
            this.observerMonitor.tabId = tab.id;
            this.observerMonitor.isDedicated = true;
            try {
              chrome.storage?.local?.set?.({ observerMonitorTabId: tab.id });
            } catch (_) {}
            try {
              await this.addTabToSmartAgentGroup(tab.id);
            } catch (_) {}
          })
          .catch(() => {})
          .finally(() => {
            this.observerMonitor.creationPromise = null;
          });
      }
      await this.observerMonitor.creationPromise;
    }

    try {
      const sites = await getSupportedSites();
      const site = Object.values(sites)[0];
      await this.waitForTabLoad(this.observerMonitor.tabId);
      await this.ensureContentScript(
        this.observerMonitor.tabId,
        site.contentScript
      );
      await this.sendMessageToTab(this.observerMonitor.tabId, {
        type: "PERFORM_SITE_ACTIONS",
        actions: [
          {
            action: "ShowMonitoringBanner",
            arguments: {
              text: "SmartAgent: Observer Tab (do not close)",
              bg: "#004d40",
              fg: "#ffffff",
            },
          },
        ],
      }).catch(() => {});
    } catch (_) {}
    try {
      await this.addTabToSmartAgentGroup(this.observerMonitor.tabId);
    } catch (_) {}
  }

  /**
   * Check login status and toggle pause/resume for all graphs
   */
  async checkLoginAndTogglePause() {
    if (this.loginMonitor.checking) return;
    if (!this.loginMonitor.tabId) return;

    this.loginMonitor.checking = true;

    try {
      const ordersUrl = await this.getOrdersPageUrl();
      // Refresh and keep on Orders ONLY on the dedicated monitor tab
      if (this.loginMonitor.isDedicated) {
        await chrome.tabs.update(this.loginMonitor.tabId, {
          url: ordersUrl,
          pinned: false,
          active: false,
        });
      }
      await this.waitForTabLoad(this.loginMonitor.tabId);
      await this.sendMessageToTab(this.loginMonitor.tabId, {
        type: "PERFORM_SITE_ACTIONS",
        actions: [{ action: "ShowMonitoringBanner", arguments: {} }],
      }).catch(() => {});

      const result = await this.sendMessageToTab(this.loginMonitor.tabId, {
        type: "PERFORM_SITE_ACTIONS",
        actions: [{ action: "IsLoginRequired", arguments: {} }],
      });

      let required = null;
      if (result && Array.isArray(result.results)) {
        const r0 = result.results[0];
        if (r0?.success) required = !!r0?.data?.loginRequired;
      }

      const previousRequired = this.loginMonitor.required;
      this.loginMonitor.required = required;
      this.loginMonitor.lastChecked = new Date();

      console.log(
        `[ExecutionService] Login check: required=${required}, previous=${previousRequired}`
      );

      // If login state just switched to required=false, allow a fresh fetch
      if (required === false && previousRequired !== false) {
        this.loginMonitor.fetchedThisSession = false;
      }

      if (required === true) {
        // Login required - pause all running graphs
        if (previousRequired !== true) {
          try {
            const sites = await getSupportedSites();
            const defaultSite =
              sites && Object.values(sites)[0] ? Object.values(sites)[0] : null;
            const brokerName =
              defaultSite && defaultSite.name ? defaultSite.name : "Broker";
            if (typeof chrome !== "undefined" && chrome?.runtime?.sendMessage) {
              chrome.runtime.sendMessage(
                {
                  type: "LOGIN_REQUIRED",
                  brokerName,
                  targetTab: "chat",
                },
                () => {}
              );
            }
          } catch (pubErr) {
            console.warn(
              "[ExecutionService] Failed to publish LOGIN_REQUIRED message:",
              pubErr
            );
          }
          console.log(
            `[ExecutionService] Login required - pausing all running graphs`
          );

          for (const [graphId, graph] of this.activeGraphs.entries()) {
            if (graph.status === "running") {
              console.log(
                `[ExecutionService] Pausing graph ${graphId} due to login requirement`
              );
              const res = this.pauseGraph(graphId);
              if (res?.success) {
                this.loginMonitor.pausedGraphsSet.add(graphId);
              }
            }
          }
        }
      } else if (required === false && previousRequired === true) {
        // Emit LOGIN_RESOLVED on transition to not required
        try {
          const sites = await getSupportedSites();
          const defaultSite =
            sites && Object.values(sites)[0] ? Object.values(sites)[0] : null;
          const brokerName =
            defaultSite && defaultSite.name ? defaultSite.name : "Broker";
          if (typeof chrome !== "undefined" && chrome?.runtime?.sendMessage) {
            chrome.runtime.sendMessage(
              {
                type: "LOGIN_RESOLVED",
                brokerName,
                targetTab: "chat",
              },
              () => {}
            );
          }
        } catch (pubErr) {
          console.warn(
            "[ExecutionService] Failed to publish LOGIN_RESOLVED message:",
            pubErr
          );
        }
        // Normalize all monitor tabs immediately post-login (reload login monitor only)
        try {
          await this.normalizeAllMonitorTabs({ onLoginResolved: true });
        } catch (_) {}
        // Resume any paused graphs
        if (this.loginMonitor.pausedGraphsSet.size > 0) {
          console.log(
            `[ExecutionService] Login no longer required - resuming paused graphs`
          );
          for (const graphId of Array.from(this.loginMonitor.pausedGraphsSet)) {
            console.log(
              `[ExecutionService] Resuming graph ${graphId} - login resolved`
            );
            const res = this.resumeGraph(graphId);
            if (res?.success) {
              this.loginMonitor.pausedGraphsSet.delete(graphId);
            }
          }
        }
      }

      // When logged in on the monitor tab, fetch full orders every tick and reload
      if (required === false) {
        // 1) Lightweight summary to decide early-exit
        // Since orders are always sorted, we can just check first/last keys to detect changes
        let summaryData = null;
        try {
          const summaryRes = await this.sendMessageToTab(
            this.loginMonitor.tabId,
            {
              type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
              actions: [{ action: "GetOrdersSummary", arguments: {} }],
            }
          );
          if (summaryRes?.success && Array.isArray(summaryRes.results)) {
            const r0 = summaryRes.results[0];
            if (r0?.success) {
              summaryData = r0.data; // { open, completed }
            } else {
              console.warn(
                "[ExecutionService] GetOrdersSummary reported failure:",
                r0?.message
              );
            }
          } else {
            console.warn(
              "[ExecutionService] Unexpected summaryRes shape:",
              summaryRes
            );
          }
        } catch (_) {}

        if (!summaryData) {
          // console.warn(
          //   "[ExecutionService] Summary unavailable; skipping early-exit and running heavy fetch"
          // );
        } else {
          // console.log("[ExecutionService] Summary fetched (open/completed):", {
          //   open: summaryData.open,
          //   completed: summaryData.completed,
          // });
        }

        // Decide if a forced full completed-orders poll is due (recovery)
        const fullPollInterval =
          Number(
            this.timeoutSettings?.completed_orders_full_poll_interval_ms
          ) || 10000;
        const nowMs = Date.now();
        const lastFullMs =
          Number(this.loginMonitor?.completedFullPollLastAt) || 0;
        const mustDoFullPoll = nowMs - lastFullMs >= fullPollInterval;

        if (
          summaryData &&
          this.shouldEarlyExitFromSummary(summaryData) &&
          !mustDoFullPoll
        ) {
          this.updatePrevSummary(summaryData);
          console.log(
            "[ExecutionService] Early-exit: reusing previous order data instead of fetching"
          );

          // Use stored previous order data and forward it to background
          const previousData = this.getPreviousOrderData();
          if (previousData.open || previousData.completed) {
            try {
              console.log(
                "[ExecutionService] Forwarding stored previous orders to background:",
                {
                  open: previousData.open?.length || 0,
                  completed: previousData.completed?.length || 0,
                }
              );
              if (typeof globalThis.__upsertOrdersFromMonitor === "function") {
                await globalThis.__upsertOrdersFromMonitor(
                  previousData.open || [],
                  previousData.completed || []
                );
              }
            } catch (bridgeErr) {
              console.warn(
                "[ExecutionService] Failed to forward stored previous orders for PouchDB upsert:",
                bridgeErr
              );
            }
          }

          const delayMs =
            Number(this.timeoutSettings?.monitor_early_exit_delay_ms) || 1000;
          console.log("[ExecutionService] Early-exit wait (ms):", delayMs);
          await this.delay(delayMs);
          try {
            // Wait for network to be idle before reloading (best-effort)
            try {
              await this.sendMessageToTab(this.loginMonitor.tabId, {
                type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
                actions: [
                  { action: "EnsureNetworkMonitor", arguments: {} },
                  {
                    action: "WaitForNetworkIdle",
                    arguments: {
                      minQuietMs: 400,
                      timeoutMs: 5000,
                      maxActiveAllowed: 0,
                    },
                  },
                ],
              });
            } catch (_) {}
            await chrome.tabs.reload(this.loginMonitor.tabId);
          } catch (e) {
            console.warn(
              "[ExecutionService] Failed to reload after early-exit:",
              e?.message
            );
          }
          return;
        }
        // Capture previous summary before updating to compute boundary deltas
        const prevSummary = {
          open: { ...(this.loginMonitor?.orderSummary?.open || {}) },
          completed: { ...(this.loginMonitor?.orderSummary?.completed || {}) },
        };
        if (summaryData) this.updatePrevSummary(summaryData);

        const fetchOutcomes = { openOrders: null, completedOrders: null };

        // Fetch all OPEN orders (iterate each row, extract detailed info)
        try {
          console.log(
            "[ExecutionService] Logged in on monitor tab. Fetching OPEN orders (full scan)..."
          );
          const openArgs = {}; // Define openArgs for GetOpenOrders call
          const openOrdersResult = await this.sendMessageToTab(
            this.loginMonitor.tabId,
            {
              type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
              actions: [{ action: "GetOpenOrders", arguments: openArgs }],
            }
          );
          console.log(
            "[ExecutionService] Open orders fetch result:",
            openOrdersResult
          );
          fetchOutcomes.openOrders = openOrdersResult;
          if (!openOrdersResult?.success) {
            console.warn(
              "[ExecutionService] Open orders reported failure:",
              openOrdersResult?.abortMessage || openOrdersResult
            );
          }
        } catch (err) {
          console.warn(
            "[ExecutionService] Failed to fetch OPEN orders on monitor tab:",
            err
          );
        }

        // Fetch COMPLETED/EXECUTED orders (full or incremental) using summary boundaries
        try {
          console.log(
            "[ExecutionService] Logged in on monitor tab. Fetching COMPLETED orders (" +
              (mustDoFullPoll ? "full" : "boundary incremental") +
              ")..."
          );

          // Previous full list (from last successful tick)
          const prevData = this.getPreviousOrderData?.() || {
            open: null,
            completed: null,
          };
          const prevCompleted = Array.isArray(prevData?.completed)
            ? prevData.completed
            : [];

          // Extract boundary keys from previous summary for incremental fetching
          const prevFirstKey = prevSummary?.completed?.firstKey || null;
          const prevLastKey = prevSummary?.completed?.lastKey || null;

          // Extract boundary keys from current summary for trimming
          const currFirstKey = summaryData?.completed?.firstKey || null;
          const currLastKey = summaryData?.completed?.lastKey || null;

          // Helper to safely pluck orders from an action result
          const extractOrders = (res) =>
            Array.isArray(res?.results) && res.results[0]?.data?.orders
              ? res.results[0].data.orders
              : [];

          // Forced recovery full-scan at fixed interval OR seed case
          if (
            mustDoFullPoll ||
            prevCompleted.length === 0 ||
            !prevFirstKey ||
            !prevLastKey
          ) {
            const completedOrdersResult = await this.sendMessageToTab(
              this.loginMonitor.tabId,
              {
                type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
                actions: [{ action: "GetCompletedOrders", arguments: {} }],
              }
            );
            console.log(
              "[ExecutionService] Completed orders fetch result (full):",
              completedOrdersResult
            );
            fetchOutcomes.completedOrders = completedOrdersResult;
            // Mark full poll attempt time regardless of success to avoid immediate thrash
            this.loginMonitor.completedFullPollLastAt = nowMs;
            if (!completedOrdersResult?.success) {
              console.warn(
                "[ExecutionService] Completed orders reported failure:",
                completedOrdersResult?.abortMessage || completedOrdersResult
              );
            }
          } else {
            // Incremental case: fetch deltas from top and bottom until reaching previous boundaries
            const actions = [];
            if (prevFirstKey) {
              actions.push({
                action: "GetCompletedOrders",
                arguments: { scanDirection: "top", stopAtKeyTop: prevFirstKey },
              });
            }
            if (prevLastKey) {
              actions.push({
                action: "GetCompletedOrders",
                arguments: {
                  scanDirection: "bottom",
                  stopAtKeyBottom: prevLastKey,
                },
              });
            }

            let topDelta = [];
            let bottomDelta = [];
            if (actions.length > 0) {
              // Run sequentially to reuse the same tab state, but each call is lightweight
              for (const a of actions) {
                try {
                  const res = await this.sendMessageToTab(
                    this.loginMonitor.tabId,
                    { type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS, actions: [a] }
                  ).catch((e) => ({ success: false, error: e?.message }));
                  const list = extractOrders(res);
                  if (a.arguments.scanDirection === "top") topDelta = list;
                  else bottomDelta = list;
                } catch (_) {}
              }
            }

            // Merge: new items at top, previous snapshot, and any additions at the bottom
            const combined = [...topDelta, ...prevCompleted, ...bottomDelta];

            // Optional trim to the new boundaries if we have the current summary
            let reconciled = combined;
            if (currFirstKey || currLastKey) {
              reconciled = this.trimOrdersByBoundary(
                combined,
                currFirstKey,
                currLastKey,
                "completed"
              );
            }

            // Dedupe by orderId (preserving order)
            const seen = new Set();
            const deduped = [];
            for (const o of reconciled) {
              const id = o?.detailedInfo?.orderId
                ? String(o.detailedInfo.orderId)
                : null;
              if (!id || seen.has(id)) continue;
              seen.add(id);
              deduped.push(o);
            }

            fetchOutcomes.completedOrders = {
              success: true,
              results: [{ data: { orders: deduped } }],
            };
          }
        } catch (err) {
          console.warn(
            "[ExecutionService] Failed to fetch COMPLETED orders on monitor tab:",
            err
          );
        }

        // Update last fetch time only if at least one fetch succeeded
        if (
          fetchOutcomes.openOrders?.success ||
          fetchOutcomes.completedOrders?.success
        ) {
          this.loginMonitor.lastFetchAt = new Date();
        } else {
          console.warn(
            "[ExecutionService] Neither OPEN nor COMPLETED orders fetch succeeded; not updating lastFetchAt"
          );
        }

        // After each fetch cycle, wait for idle and reload to keep page fresh for next interval
        try {
          try {
            await this.sendMessageToTab(this.loginMonitor.tabId, {
              type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
              actions: [
                { action: "EnsureNetworkMonitor", arguments: {} },
                {
                  action: "WaitForNetworkIdle",
                  arguments: {
                    minQuietMs: 2000,
                    timeoutMs: 5000,
                    maxActiveAllowed: 0,
                  },
                },
              ],
            });
          } catch (_) {}
          await chrome.tabs.reload(this.loginMonitor.tabId);
          await this.waitForTabLoad(this.loginMonitor.tabId);
          await this.normalizeTabForMonitoring(this.loginMonitor.tabId, {
            collapse: true,
          });
        } catch (e) {
          console.warn(
            "[ExecutionService] Failed to reload after fetch:",
            e?.message
          );
        }
        // Push fetched orders to background for PouchDB upsert of broker_status/status using orderId
        try {
          const fetchedOpen =
            Array.isArray(fetchOutcomes.openOrders?.results) &&
            fetchOutcomes.openOrders.results[0]?.data?.orders
              ? fetchOutcomes.openOrders.results[0].data.orders
              : [];
          const fetchedCompleted =
            Array.isArray(fetchOutcomes.completedOrders?.results) &&
            fetchOutcomes.completedOrders.results[0]?.data?.orders
              ? fetchOutcomes.completedOrders.results[0].data.orders
              : [];

          // Store previous order data for next-tick incremental polling and early-exit reuse
          this.storePreviousOrderData(fetchedOpen, fetchedCompleted);

          const filterWithOrderId = (arr) =>
            Array.isArray(arr)
              ? arr.filter((o) => !!o?.detailedInfo?.orderId)
              : [];
          let openWithId = filterWithOrderId(fetchedOpen);
          const completedWithId = filterWithOrderId(fetchedCompleted);

          // If same orderId appears in open and completed, consider it in completed only
          if (completedWithId.length > 0 && openWithId.length > 0) {
            const completedIds = new Set(
              completedWithId
                .map((o) => o?.detailedInfo?.orderId)
                .filter((id) => id != null)
                .map((id) => String(id))
            );
            openWithId = openWithId.filter((o) => {
              const id = o?.detailedInfo?.orderId
                ? String(o.detailedInfo.orderId)
                : null;
              return !id || !completedIds.has(id);
            });
          }

          if (openWithId.length + completedWithId.length > 0) {
            try {
              console.log(
                "[order polling ] forwarding to background (using orderId)",
                {
                  openCount: openWithId.length,
                  completedCount: completedWithId.length,
                  openIdsSample: openWithId.slice(0, 5).map((o, idx) => ({
                    idx,
                    id: String(o?.detailedInfo?.orderId || ""),
                  })),
                  completedIdsSample: completedWithId
                    .slice(0, 5)
                    .map((o, idx) => ({
                      idx,
                      id: String(o?.detailedInfo?.orderId || ""),
                    })),
                }
              );
              if (typeof globalThis.__upsertOrdersFromMonitor === "function") {
                await globalThis.__upsertOrdersFromMonitor(
                  openWithId,
                  completedWithId
                );
              } else {
                console.warn(
                  "[order polling ] upsert bridge function not found"
                );
              }
            } catch (_) {}
          } else {
            console.log(
              "[order polling ] no orders with orderId to upsert this tick"
            );
          }
        } catch (bridgeErr) {
          console.warn(
            "[ExecutionService] Failed to forward monitor orders for PouchDB upsert:",
            bridgeErr
          );
        }
      }
    } catch (e) {
      console.warn(`[ExecutionService] Login check failed:`, e);
      // If tab vanished, recreate next tick
      try {
        await chrome.tabs.get(this.loginMonitor.tabId);
      } catch {
        this.loginMonitor.tabId = null;
      }
    } finally {
      this.loginMonitor.checking = false;
    }
  }

  async normalizeTabForMonitoring(tabId, options = {}) {
    try {
      if (!tabId) return;
      const { url, bannerText, collapse = true } = options;

      // Ensure the tab exists and unpin it; optionally navigate
      try {
        await chrome.tabs.get(tabId);
      } catch (_) {
        return;
      }
      try {
        await chrome.tabs.update(tabId, {
          pinned: false,
          ...(url ? { url } : {}),
          active: false,
        });
      } catch (_) {}

      await this.waitForTabLoad(tabId);

      // Ensure content script
      try {
        const sites = await getSupportedSites();
        const site = Object.values(sites)[0];
        await this.ensureContentScript(tabId, site.contentScript);
      } catch (_) {}

      // Optional banner
      if (bannerText) {
        try {
          await this.sendMessageToTab(tabId, {
            type: "PERFORM_SITE_ACTIONS",
            actions: [
              {
                action: "ShowMonitoringBanner",
                arguments: { text: bannerText, bg: "#0b3d91", fg: "#ffffff" },
              },
            ],
          });
        } catch (_) {}
      }

      // Group it (with optional collapse)
      try {
        await this.addTabToSmartAgentGroup(tabId, { collapse });
      } catch (_) {}
    } catch (_) {}
  }

  async normalizeAllMonitorTabs(options = {}) {
    try {
      const { onLoginResolved = false } = options;
      const items = [];
      if (this.loginMonitor?.tabId) {
        let ordersUrl = null;
        try {
          ordersUrl = await this.getOrdersPageUrl();
        } catch (_) {
          ordersUrl = null;
        }
        items.push({
          name: "loginMonitor",
          tabId: this.loginMonitor.tabId,
          url: ordersUrl,
          bannerText: "SmartAgent: Login Monitor (do not close)",
        });
      }
      if (this.observerMonitor?.tabId) {
        items.push({
          name: "observerMonitor",
          tabId: this.observerMonitor.tabId,
          url: null,
          bannerText: "SmartAgent: Observer Tab (do not close)",
        });
      }

      for (const item of items) {
        // On login resolved, reload monitor tabs to ensure a fresh state
        if (onLoginResolved) {
          try {
            try {
              await this.sendMessageToTab(item.tabId, {
                type: MESSAGE_TYPES.PERFORM_SITE_ACTIONS,
                actions: [
                  { action: "EnsureNetworkMonitor", arguments: {} },
                  {
                    action: "WaitForNetworkIdle",
                    arguments: {
                      minQuietMs: 400,
                      timeoutMs: 5000,
                      maxActiveAllowed: 0,
                    },
                  },
                ],
              });
            } catch (_) {}
            await chrome.tabs.reload(item.tabId);
          } catch (_) {}
          try {
            await this.waitForTabLoad(item.tabId);
          } catch (_) {}
        }
        // Normalize (navigate only if url provided; observer tab keeps url null)
        await this.normalizeTabForMonitoring(item.tabId, {
          url: item.url,
          bannerText: item.bannerText,
          collapse: true,
        });
      }
    } catch (_) {}
  }

  /**
   * Start the login monitoring system
   */
  startLoginMonitor() {
    if (this.loginMonitor.started) return;
    this.loginMonitor.started = true;

    console.log(`[ExecutionService] Starting login monitor`);

    // Ensure tabs (login monitor and observer), then start ticker
    this.ensureLoginMonitorTab().catch((e) => {
      console.warn(`[ExecutionService] Failed to ensure login monitor tab:`, e);
    });
    this.ensureObserverMonitorTab().catch((e) => {
      console.warn(
        `[ExecutionService] Failed to ensure observer monitor tab:`,
        e
      );
    });

    // Recreate if closed. Keep a reference so we can remove this listener on stop.
    this.loginMonitor.onRemovedHandler = (tabId) => {
      if (!this.loginMonitor.started) return; // Do not recreate if monitor is stopped
      if (tabId === this.loginMonitor.tabId && this.loginMonitor.isDedicated) {
        console.log(`[ExecutionService] Login monitor tab closed, recreating`);
        this.loginMonitor.tabId = null;
        // Debounce recreation to avoid duplicate creations from race
        setTimeout(() => {
          if (!this.loginMonitor.started) return; // Respect stopped state
          if (!this.loginMonitor.tabId && !this.loginMonitor.creationPromise) {
            this.ensureLoginMonitorTab().catch((e) => {
              console.warn(
                `[ExecutionService] Failed to recreate login monitor tab:`,
                e
              );
            });
          }
        }, 200);
      }
    };
    chrome.tabs.onRemoved.addListener(this.loginMonitor.onRemovedHandler);

    // Recreate observer monitor tab if closed
    this.observerMonitor.onRemovedHandler = (tabId) => {
      if (!this.loginMonitor.started) return;
      if (
        tabId === this.observerMonitor.tabId &&
        this.observerMonitor.isDedicated
      ) {
        console.log(
          `[ExecutionService] Observer monitor tab closed, recreating`
        );
        this.observerMonitor.tabId = null;
        setTimeout(() => {
          if (!this.loginMonitor.started) return;
          if (
            !this.observerMonitor.tabId &&
            !this.observerMonitor.creationPromise
          ) {
            this.ensureObserverMonitorTab().catch((e) => {
              console.warn(
                `[ExecutionService] Failed to recreate observer monitor tab:`,
                e
              );
            });
          }
        }, 200);
      }
    };
    chrome.tabs.onRemoved.addListener(this.observerMonitor.onRemovedHandler);

    // Start 1-second interval check
    this.loginMonitor.intervalId = setInterval(async () => {
      if (!this.loginMonitor.tabId && !this.loginMonitor.creationPromise) {
        await this.ensureLoginMonitorTab().catch((e) => {
          console.warn(
            `[ExecutionService] Failed to ensure login monitor tab in interval:`,
            e
          );
        });
        return;
      }
      if (this.loginMonitor.tabId) {
        await this.checkLoginAndTogglePause();
      }
    }, 3000);

    // Also check immediately when graphs are added or start executing
    this.loginMonitor.checkImmediately = () => {
      if (!this.loginMonitor.checking && this.loginMonitor.tabId) {
        this.checkLoginAndTogglePause().catch((e) => {
          console.warn(`[ExecutionService] Immediate login check failed:`, e);
        });
      }
    };

    // First immediate check - delay to avoid race with tab creation
    setTimeout(() => {
      if (this.loginMonitor.tabId) {
        this.checkLoginAndTogglePause().catch((e) => {
          console.warn(`[ExecutionService] Initial login check failed:`, e);
        });
      }
    }, 500);
  }

  /**
   * Stop the login monitoring system
   */
  stopLoginMonitor(closeTab = false) {
    if (!this.loginMonitor.started && !this.loginMonitor.tabId) return;

    console.log(`[ExecutionService] Stopping login monitor`);

    if (this.loginMonitor.intervalId) {
      clearInterval(this.loginMonitor.intervalId);
      this.loginMonitor.intervalId = null;
    }

    // Remove onRemoved listener if present to avoid recreating after stop
    if (this.loginMonitor.onRemovedHandler) {
      try {
        chrome.tabs.onRemoved.removeListener(
          this.loginMonitor.onRemovedHandler
        );
      } catch (_) {}
      this.loginMonitor.onRemovedHandler = null;
    }
    if (this.observerMonitor.onRemovedHandler) {
      try {
        chrome.tabs.onRemoved.removeListener(
          this.observerMonitor.onRemovedHandler
        );
      } catch (_) {}
      this.observerMonitor.onRemovedHandler = null;
    }

    // Optionally close the monitoring tabs
    if (closeTab && this.loginMonitor.tabId && this.loginMonitor.isDedicated) {
      const tabIdToClose = this.loginMonitor.tabId;
      try {
        chrome.tabs.remove(tabIdToClose);
      } catch (_) {}
      this.loginMonitor.tabId = null;
      this.loginMonitor.isDedicated = false;
      try {
        chrome.storage?.local?.remove?.("loginMonitorTabId");
      } catch (_) {}
    }
    if (
      closeTab &&
      this.observerMonitor.tabId &&
      this.observerMonitor.isDedicated
    ) {
      const tabIdToClose = this.observerMonitor.tabId;
      try {
        chrome.tabs.remove(tabIdToClose);
      } catch (_) {}
      this.observerMonitor.tabId = null;
      this.observerMonitor.isDedicated = false;
      try {
        chrome.storage?.local?.remove?.("observerMonitorTabId");
      } catch (_) {}
    }

    this.loginMonitor.started = false;
  }

  /**
   * Check login status right before action execution
   * This is the critical final check to prevent actions from executing when login is required
   * @param {number} tabId - Tab ID to check login status in
   * @param {string} nodeId - Node ID for logging
   */
  async checkLoginBeforeExecution(tabId, nodeId, graphId) {
    try {
      console.log(
        `[ExecutionService] Pre-execution login check for node: ${nodeId}`
      );

      // Send login check to the execution tab (not the monitor tab)
      const result = await this.sendMessageToTab(tabId, {
        type: "PERFORM_SITE_ACTIONS",
        actions: [{ action: "IsLoginRequired", arguments: {} }],
      });

      let loginRequired = false;
      if (result && Array.isArray(result.results)) {
        const r0 = result.results[0];
        if (r0?.success) {
          loginRequired = !!r0?.data?.loginRequired;
        }
      }

      console.log(
        `[ExecutionService] Pre-execution login check result: loginRequired=${loginRequired}`
      );

      if (loginRequired) {
        // Ensure monitor reflects required
        this.loginMonitor.required = true;
        this.loginMonitor.lastChecked = new Date();

        // Pause current graph to prevent progress
        if (graphId) {
          this.pauseGraph(graphId);
          this.loginMonitor.pausedGraphsSet.add(graphId);
        }

        console.log(
          `[ExecutionService] Waiting for login before executing node ${nodeId}`
        );
        // Wait until login is no longer required
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(500);
        }

        // Resume graph if it was paused here
        if (graphId && this.pausedGraphs.has(graphId)) {
          this.resumeGraph(graphId);
        }
      }
    } catch (error) {
      console.warn(
        `[ExecutionService] Pre-execution login check failed for node ${nodeId}:`,
        error.message
      );
      // Fall back to monitor state: if login currently required, wait; otherwise proceed
      if (this.loginMonitor.required === true) {
        if (graphId) {
          this.pauseGraph(graphId);
          this.loginMonitor.pausedGraphsSet.add(graphId);
        }
        while (this.loginMonitor.required !== false) {
          await this.checkLoginAndTogglePause().catch(() => {});
          await this.delay(500);
        }
        if (graphId && this.pausedGraphs.has(graphId)) {
          this.resumeGraph(graphId);
        }
      }
    }
  }
}
