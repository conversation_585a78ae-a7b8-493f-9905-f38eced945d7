// Simple UI service for handling status messages and UI interactions
export class UIService {
  constructor(statusElement) {
    this.statusElement = statusElement;
  }

  showMessage(message, type = 'info') {
    if (this.statusElement) {
      this.statusElement.textContent = message;
      this.statusElement.className = `status-message ${type}`;
    }
  }

  showSuccess(message) {
    this.showMessage(message, 'success');
  }

  showError(message) {
    this.showMessage(message, 'error');
  }

  showInfo(message) {
    this.showMessage(message, 'info');
  }

  clearMessage() {
    if (this.statusElement) {
      this.statusElement.textContent = '';
      this.statusElement.className = 'status-message';
    }
  }
}
