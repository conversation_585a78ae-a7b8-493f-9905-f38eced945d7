{"SUPPORTED_SITES": {"kiteByZerodha": {"name": "Kite by <PERSON><PERSON>", "url": "https://kite.zerodha.com/", "urlPrefix": "https://kite.zerodha.com/", "contentScript": "content-scripts/zerodha.js"}}, "ACTION_ARGUMENTS": {"BUY": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE"], "SELL": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE"], "PlaceBuyLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "PRICE"], "PlaceSellLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "PRICE"], "PlaceBuyStopLossMarketOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE"], "PlaceSellStopLossMarketOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE"], "PlaceBuyStopLossLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE", "limitPrice"], "PlaceSellStopLossLimitOrder": ["SYMBOL", "QUANTITY", "PRODUCT_TYPE", "TRIGGER_PRICE", "limitPrice"], "MONITORPROFIT": ["TARGET_PROFIT_AMOUNT"], "ExitAllPositions": [], "SellAll": ["SYMBOL", "EXCHANGE", "PRODUCT_TYPE"], "Monitor": ["condition"], "MonitorConditionThenAct": ["condition", "on_trigger"], "NavigateToProfile": [], "GetProfileInfo": [], "GetPortfolioStats": [], "GetOpenPositionPnL": [], "MonitorSymbolFromWatchlist": ["symbol", "condition", "on_trigger"], "SelectOrderByCriteria": ["TYPE", "SYMBOL", "EXCHANGE", "PRODUCT", "QUANTITY"], "CancelOrder": ["SYMBOL", "QUANTITY", "EXCHANGE", "PRODUCT", "TYPE"], "GetOpenOrders": []}, "MESSAGE_TYPES": {"EXECUTE_ACTIONS": "EXECUTE_ACTIONS", "PERFORM_SITE_ACTIONS": "PERFORM_SITE_ACTIONS", "ACTION_STATUS_UPDATE": "ACTION_STATUS_UPDATE", "EXECUTION_COMPLETE": "EXECUTION_COMPLETE"}, "ENVIRONMENT": {"slow_execute": false, "close_tabs_after_execution": true}, "EXECUTION_SERVICE": {"max_concurrent_tabs": 10, "default_execution_mode": "node-by-node", "available_execution_modes": ["node-by-node"], "tab_reuse_enabled": true, "stop_on_error": true, "fail_dependents_on_error": true}, "TIMEOUT_SETTINGS": {"content_script_general_timeout_seconds": 8000, "content_script_monitoring_timeout_seconds": 3000, "execution_service_general_timeout_seconds": 8000, "execution_service_monitoring_timeout_seconds": 6000, "background_test_timeout_seconds": 10000, "monitor_max_duration_minutes": 60, "monitor_polling_interval_seconds": 60, "monitor_error_interval_seconds": 10, "monitor_max_retry_attempts": 7200, "action_execution_timeout_seconds": 3000, "price_monitor_observer_timeout_minutes": 1200, "monitor_max_consecutive_errors": 5, "monitor_early_exit_delay_ms": 3000}}