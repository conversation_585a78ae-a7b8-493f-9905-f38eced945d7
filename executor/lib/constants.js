// Constants for the executor extension

/**
 * Automation modes
 */
export const AUTOMATION_MODES = {
  CURRENT_TAB: 'currentTab',
  BACKGROUND: 'background'
};

/**
 * Message types for communication between components
 */
export const MESSAGE_TYPES = {
  EXECUTE_ACTIONS: "EXECUTE_ACTIONS",
  PERFORM_SITE_ACTIONS: "PERFORM_SITE_ACTIONS",
  ACTION_STATUS_UPDATE: "ACTION_STATUS_UPDATE",
  EXECUTION_COMPLETE: "EXECUTION_COMPLETE"
};

/**
 * Action types supported by the extension
 */
export const ACTION_TYPES = {
  BUY: "BUY",
  SELL: "SELL",
  PLACE_BUY_LIMIT_ORDER: "PlaceBuyLimitOrder",
  PLACE_SELL_LIMIT_ORDER: "PlaceSellLimitOrder",
  PLACE_BUY_STOP_LOSS_MARKET_ORDER: "PlaceBuyStopLossMarketOrder",
  PLACE_SELL_STOP_LOSS_MARKET_ORDER: "PlaceSellStopLossMarketOrder",
  PLACE_BUY_STOP_LOSS_LIMIT_ORDER: "PlaceBuyStopLossLimitOrder",
  PLACE_SELL_STOP_LOSS_LIMIT_ORDER: "PlaceSellStopLossLimitOrder",
  MONITOR_PROFIT: "MONITORPROFIT",
  CANCEL_ORDER: "CancelOrder",
  GET_PORTFOLIO_STATS: "GetPortfolioStats",
  GET_OPEN_POSITION_PNL: "GetOpenPositionPnL",
  SELECT_ORDER_BY_CRITERIA: "SelectOrderByCriteria"
};

/**
 * Argument types for validation
 */
export const ARGUMENT_TYPES = {
  STRING: "string",
  NUMBER: "number",
  OBJECT: "object"
};

/**
 * Argument keys and their expected types
 */
export const ARGUMENT_CONFIG = {
  SYMBOL: { type: ARGUMENT_TYPES.STRING },
  QUANTITY: { type: ARGUMENT_TYPES.NUMBER },
  PRICE: { type: ARGUMENT_TYPES.NUMBER },
  TRIGGER_PRICE: { type: ARGUMENT_TYPES.NUMBER },
  TARGET_PROFIT_PERCENTAGE: { type: ARGUMENT_TYPES.NUMBER },
  MONITOR_INTERVAL_SECONDS: { type: ARGUMENT_TYPES.NUMBER },
  TARGET_PROFIT_AMOUNT: { type: ARGUMENT_TYPES.NUMBER },
  EXCHANGE: { type: ARGUMENT_TYPES.STRING },
  PRODUCT_TYPE: { type: ARGUMENT_TYPES.STRING },
  PRODUCT: { type: ARGUMENT_TYPES.STRING },
  PRODUCTTYPE: { type: ARGUMENT_TYPES.STRING }, // Support for camelCase format
  TYPE: { type: ARGUMENT_TYPES.STRING },
  type: { type: ARGUMENT_TYPES.STRING },
  symbol: { type: ARGUMENT_TYPES.STRING },
  quantity: { type: ARGUMENT_TYPES.NUMBER },
  exchange: { type: ARGUMENT_TYPES.STRING },
  product: { type: ARGUMENT_TYPES.STRING },
  condition: { type: ARGUMENT_TYPES.OBJECT },
  on_trigger: { type: ARGUMENT_TYPES.OBJECT }
};

/**
 * All possible argument names for case-insensitive matching
 */
export const ALL_ARGUMENT_NAMES = [
  'SYMBOL',
  'QUANTITY',
  'PRICE',
  'TARGET_PROFIT_PERCENTAGE',
  'MONITOR_INTERVAL_SECONDS',
  'TARGET_PROFIT_AMOUNT',
  'EXCHANGE',
  'PRODUCT_TYPE',
  'PRODUCT',
  'TYPE',
  'type',
  'symbol',
  'quantity',
  'exchange',
  'product',
  'PRODUCTTYPE',
  'condition',
  'on_trigger'
];

/**
 * UI message types
 */
export const UI_MESSAGE_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  INFO: 'info'
};

/**
 * Content script identifier
 */
export const CONTENT_SCRIPT_IDENTIFIER = 'SmartFinAgentContentScriptLoaded';

/**
 * Index-related constants
 */
export const INDEX_KEYWORDS = [
  'NIFTY', 'SENSEX', 'BANKEX', 'ALLCAP', 'INDEX', 'INDICES'
];

/**
 * Exchange mapping for indices
 */
export const INDEX_EXCHANGE_MAPPING = {
  NSE: ['NIFTY'], // NIFTY indices are on NSE
  BSE: ['SENSEX', 'BANKEX', 'ALLCAP'] // SENSEX, BANKEX, ALLCAP are on BSE
};

/**
 * Default delays in milliseconds
 */
export const DELAYS = {
  TAB_LOAD_WAIT: 2000,
  PAGE_INITIALIZATION: 2000
};

/**
 * Error messages
 */
export const ERROR_MESSAGES = {
  INVALID_JSON: "Invalid JSON format",
  NO_RESPONSE: "No response from content script",
  UNSUPPORTED_SITE: "Unsupported site",
  INVALID_AUTOMATION_MODE: "Invalid automation mode",
  CROSS_ORIGIN_LIMITATION: "Cross-origin iframe access is not supported in this implementation. Please use the 'Current Tab' mode for automation.",
  MESSAGE_CHANNEL_CLOSED: "Message channel closed before response received. This usually happens when the tab was closed or navigated away during execution.",
  TAB_NOT_ACCESSIBLE: "Tab is not accessible for content script injection."
};

/**
 * Success messages
 */
export const SUCCESS_MESSAGES = {
  EXECUTION_COMPLETE: "All actions executed successfully!",
  VALIDATION_SUCCESS: "Validation successful."
};
