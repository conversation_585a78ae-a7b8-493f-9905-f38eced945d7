// Copy and paste this entire script into Chrome console to use PouchDB utilities
// Usage: After pasting, you can call functions like: updateDocumentStatus('doc123', 'completed')

(function () {
  "use strict";

  // Store functions in global scope for console access
  window.PouchDBUtils = {};

  /**
   * Update document status through background script
   * @param {string} docId - Document ID to update
   * @param {string} newStatus - New status to set
   * @param {Object} options - Additional options for the update
   * @returns {Promise<Object>} Result object with success status and details
   */
  window.PouchDBUtils.updateDocumentStatus = async function (
    docId,
    newStatus,
    options = {}
  ) {
    const {
      metadata = {},
      maxRetries = 3,
      retryDelay = 50,
      createIfMissing = true,
      preserveExistingFields = true,
      requiredFields = ["_id", "type", "created_at"],
    } = options;

    console.log(
      `[PouchDB-Utils] Updating document status: ${docId} -> ${newStatus}`
    );

    try {
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          {
            type: "UPDATE_DOCUMENT_STATUS",
            docId,
            newStatus,
            metadata,
            options: {
              maxRetries,
              retryDelay,
              createIfMissing,
              preserveExistingFields,
              requiredFields,
            },
          },
          (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else if (response && response.success !== undefined) {
              resolve(response);
            } else {
              reject(new Error("Invalid response from background script"));
            }
          }
        );
      });

      if (response.success) {
        console.log(`[PouchDB-Utils] Successfully updated document: ${docId}`, {
          newStatus,
          rev: response.rev,
          attempt: response.attempt,
        });
      } else {
        console.error(`[PouchDB-Utils] Failed to update document: ${docId}`, {
          error: response.error,
          attempt: response.attempt,
        });
      }

      return response;
    } catch (error) {
      console.error(`[PouchDB-Utils] Error updating document: ${docId}`, error);
      return {
        success: false,
        docId,
        newStatus,
        error: error.message || String(error),
        attempt: 0,
      };
    }
  };

  /**
   * Bulk update multiple documents with status changes
   * @param {Array} updates - Array of update objects: [{docId, newStatus, metadata?}]
   * @param {Object} options - Options for bulk update
   * @returns {Promise<Object>} Result object with summary of all updates
   */
  window.PouchDBUtils.bulkUpdateDocumentStatus = async function (
    updates,
    options = {}
  ) {
    console.log(
      `[PouchDB-Utils] Starting bulk update for ${updates.length} documents`
    );

    const results = [];
    let successCount = 0;
    let errorCount = 0;

    for (const update of updates) {
      const { docId, newStatus, metadata = {} } = update;

      try {
        const result = await window.PouchDBUtils.updateDocumentStatus(
          docId,
          newStatus,
          { metadata, ...options }
        );

        results.push(result);
        if (result.success) {
          successCount++;
        } else {
          errorCount++;
        }
      } catch (error) {
        console.error(
          `[PouchDB-Utils] Unexpected error updating ${docId}:`,
          error
        );
        results.push({
          success: false,
          docId,
          newStatus,
          error: error.message || String(error),
          attempt: 0,
        });
        errorCount++;
      }
    }

    console.log(
      `[PouchDB-Utils] Bulk update completed: ${successCount} successful, ${errorCount} failed`
    );

    return {
      success: errorCount === 0,
      total: updates.length,
      successful: successCount,
      failed: errorCount,
      results,
    };
  };

  /**
   * Create a minimal document for status updates when original doesn't exist
   * @param {string} docId - Document ID
   * @param {string} status - Initial status
   * @param {Object} metadata - Additional metadata
   * @returns {Object} Minimal document object
   */
  window.PouchDBUtils.createMinimalDocument = function (
    docId,
    status,
    metadata = {}
  ) {
    const now = new Date().toISOString();

    // Extract document type from ID (e.g., "order_123" -> "order")
    const docType = docId.split("_")[0] || "unknown";

    return {
      _id: docId,
      type: docType,
      status: status,
      created_at: now,
      updated_at: now,
      ...metadata,
    };
  };

  /**
   * Factory function to create a status updater with predefined options
   * @param {Object} defaultOptions - Default options for status updates
   * @returns {Function} Configured status update function
   */
  window.PouchDBUtils.createStatusUpdater = function (defaultOptions = {}) {
    return async (docId, newStatus, metadata = {}, options = {}) => {
      const mergedOptions = { ...defaultOptions, ...options };
      return window.PouchDBUtils.updateDocumentStatus(docId, newStatus, {
        metadata,
        ...mergedOptions,
      });
    };
  };

  /**
   * Factory function to create a bulk status updater with predefined options
   * @param {Object} defaultOptions - Default options for bulk updates
   * @returns {Function} Configured bulk status update function
   */
  window.PouchDBUtils.createBulkStatusUpdater = function (defaultOptions = {}) {
    return async (updates, options = {}) => {
      const mergedOptions = { ...defaultOptions, ...options };
      return window.PouchDBUtils.bulkUpdateDocumentStatus(
        updates,
        mergedOptions
      );
    };
  };

  /**
   * Check if PouchDB is available in background script
   * @returns {Promise<boolean>} True if PouchDB is available
   */
  window.PouchDBUtils.isPouchDBAvailable = async function () {
    try {
      const response = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          { type: "GET_POUCHDB_INSTANCE" },
          (response) => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError.message));
            } else {
              resolve(response);
            }
          }
        );
      });

      return response && response.success;
    } catch (error) {
      console.error(
        "[PouchDB-Utils] Error checking PouchDB availability:",
        error
      );
      return false;
    }
  };

  /**
   * Quick status update function (most commonly used)
   * @param {string} docId - Document ID
   * @param {string} newStatus - New status
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} Result of the update operation
   */
  window.PouchDBUtils.quickUpdate = async function (
    docId,
    newStatus,
    metadata = {}
  ) {
    try {
      // Check if PouchDB is available first
      const isAvailable = await window.PouchDBUtils.isPouchDBAvailable();
      if (!isAvailable) {
        return {
          success: false,
          error: "PouchDB not available in background script",
        };
      }

      return await window.PouchDBUtils.updateDocumentStatus(docId, newStatus, {
        metadata,
      });
    } catch (error) {
      console.error("Quick update failed:", error);
      return { success: false, error: error.message };
    }
  };

  console.log("✅ PouchDB Utils loaded! Available functions:");
  console.log("- PouchDBUtils.updateDocumentStatus(docId, newStatus, options)");
  console.log("- PouchDBUtils.bulkUpdateDocumentStatus(updates, options)");
  console.log("- PouchDBUtils.createMinimalDocument(docId, status, metadata)");
  console.log("- PouchDBUtils.createStatusUpdater(defaultOptions)");
  console.log("- PouchDBUtils.createBulkStatusUpdater(defaultOptions)");
  console.log("- PouchDBUtils.isPouchDBAvailable()");
  console.log("- PouchDBUtils.quickUpdate(docId, newStatus, metadata)");
  console.log("");
  console.log("Example usage:");
  console.log(
    "PouchDBUtils.quickUpdate('order_123', 'completed', { reason: 'executed' })"
  );
  console.log(
    "PouchDBUtils.updateDocumentStatus('monitor_456', 'stopped', { reason: 'cancelled' })"
  );
  console.log("");
  console.log(
    "Note: This script communicates with the background script to access PouchDB."
  );
})();
