// Background script for the executor extension
console.log("🚀 [DEBUG] Background script starting...");

// Load importScripts-compatible executor modules
console.log("🔧 [DEBUG] Loading executor modules...");
try {
  // Load the unified bundle which exposes globals expected by background.js
  importScripts("lib/pouchdb.min.js");
  importScripts("lib/json-rule-engine-bundle.bundle.js");
  console.log("✅ [DEBUG] Executor modules loaded successfully");
} catch (error) {
  console.error("❌ [DEBUG] Failed to load executor modules:", error);
}

// PouchDB variables for execution polling
let localDB = null;
let remoteDB = null;
let changeListener = null; // Track the change listener

// Global engine controller instance
let engineController = null;
let initializing = false;

// Global settings
let timeoutSettings = { background_test_timeout_seconds: 10 };
let executionDefaults = {
  max_concurrent_tabs: 2,
  stop_on_error: true,
  fail_dependents_on_error: true,
};

// Cache of login status for UI dedupe and initial load
let cachedLoginStatus = {
  required: null,
  brokerName: null,
  updatedAt: null,
};

// Persist and load cached login status to survive service worker restarts
async function loadCachedLoginStatusFromStorage() {
  try {
    const data = await chrome.storage.local.get("cached_login_status");
    const stored = data?.cached_login_status;
    if (stored && typeof stored === "object") {
      cachedLoginStatus = {
        required: stored.required ?? null,
        brokerName: stored.brokerName ?? null,
        updatedAt: stored.updatedAt ?? null,
      };
      console.log(
        "[Background] Loaded cached login status from storage:",
        cachedLoginStatus
      );
    }
  } catch (e) {
    console.warn(
      "[Background] Failed to load cached login status from storage:",
      e
    );
  }
}

async function persistCachedLoginStatus() {
  try {
    await chrome.storage.local.set({ cached_login_status: cachedLoginStatus });
  } catch (e) {
    console.warn("[Background] Failed to persist cached login status:", e);
  }
}

// Fire and forget initial load
try {
  loadCachedLoginStatusFromStorage();
} catch (_) {}

// Load execution and timeout settings from shared config
async function loadTimeoutSettings() {
  try {
    const response = await fetch(
      chrome.runtime.getURL("lib/shared-config.json")
    );
    const config = await response.json();
    if (config.TIMEOUT_SETTINGS) {
      timeoutSettings = { ...timeoutSettings, ...config.TIMEOUT_SETTINGS };
      console.log("[Background] Loaded timeout settings:", timeoutSettings);
    }
    if (config.EXECUTION_SERVICE) {
      executionDefaults = {
        ...executionDefaults,
        max_concurrent_tabs: Number.isFinite(
          config.EXECUTION_SERVICE.max_concurrent_tabs
        )
          ? config.EXECUTION_SERVICE.max_concurrent_tabs
          : executionDefaults.max_concurrent_tabs,
        stop_on_error: config.EXECUTION_SERVICE.stop_on_error !== false,
        fail_dependents_on_error:
          config.EXECUTION_SERVICE.fail_dependents_on_error !== false,
      };
      console.log("[Background] Loaded execution defaults:", executionDefaults);
    }
  } catch (error) {
    console.warn(
      "[Background] Failed to load timeout config, using defaults:",
      error
    );
  }
}

/**
 * Initialize PouchDB for execution polling
 */

// SHA-256 hash generator
async function generateHash(message) {
  // Convert the string to a Uint8Array
  const msgUint8 = new TextEncoder().encode(message);
  // Hash the message
  const hashBuffer = await crypto.subtle.digest("SHA-256", msgUint8);
  // Convert the buffer to a hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray
    .map((b) => b.toString(16).padStart(2, "0"))
    .join("");
  return hashHex;
}

const CACHE_NAME = "user-data-cache-v1";
// Store data in the cache
async function storeDataInCache(key, value) {
  await chrome.storage.local.set({ [key]: value });
}

// Retrieve data from the cache
async function getDataFromCache(key) {
  try {
    const data = await chrome.storage.local.get(key);
    // The data is returned as an object, so you access your value by key
    const value = data[key];

    if (value !== undefined) {
      return value;
    } else {
      console.log(`No string found for key "${key}".`);
      return null;
    }
  } catch (error) {
    console.error("Error retrieving string:", error);
    return null;
  }
}

async function initializePouchDB() {
  try {
    console.log("🔧 [DEBUG] Initializing PouchDB...");
    // Get "firebase_uid" from cache
    const firebaseUID = await getDataFromCache("firebase_uid");
    if (!firebaseUID) {
      console.warn(
        "Waiting for login since there is no firebase_uid in localStorage yet."
      );
      return false;
    }
    // Get "couchdb_details" from cache
    const couchdbDetails = await getDataFromCache("couchdb_details");
    if (!couchdbDetails) {
      console.warn(
        "Waiting for login since there is no couchdb_details in localStorage yet."
      );
      return false;
    }

    // Use the same database names as the frontend (with proper hashing)
    const localDBName = `execution_${firebaseUID}_local`;
    const hash = await generateHash(firebaseUID);

    console.log("🔧 [DEBUG] Using database names:");
    console.log("🔧 [DEBUG] - Local:", localDBName);
    console.log("🔧 [DEBUG] - Firebase UID:", firebaseUID);
    console.log("🔧 [DEBUG] - Hash:", hash);
    console.log(
      "🔧 [DEBUG] - Remote:",
      `${couchdbDetails.couchdb_url}/${couchdbDetails.couchdb_database} (Username: ${couchdbDetails.couchdb_user})`
    );

    localDB = new PouchDB(localDBName);
    remoteDB = new PouchDB(
      `${couchdbDetails.couchdb_url}/${couchdbDetails.couchdb_database}`,
      {
        skip_setup: true,
        auth: {
          username: couchdbDetails.couchdb_user,
          password: couchdbDetails.couchdb_password,
        },
      }
    );

    console.log("✅ [DEBUG] PouchDB databases initialized");

    // Set up sync
    const sync = localDB.sync(remoteDB, {
      live: true,
      retry: true,
    });

    sync
      .on("change", function (change) {
        console.log("🔄 [SYNC] Change detected:", change.direction);
      })
      .on("error", function (err) {
        console.error("❌ [SYNC] Sync error:", err);
      });

    console.log("✅ [DEBUG] PouchDB sync initialized");
    return true;
  } catch (error) {
    console.error("❌ [DEBUG] PouchDB initialization failed:", error);
    return false;
  }
}

/**
 * Start monitoring PouchDB for execution requests
 */
function startExecutionMonitoring() {
  if (!localDB) {
    console.error("❌ [EXEC] PouchDB not initialized, cannot start monitoring");
    return;
  }

  // Cancel existing listener if it exists
  if (changeListener) {
    console.log("🔄 [EXEC] Canceling existing change listener...");
    changeListener.cancel();
    changeListener = null;
  }

  console.log("🎯 [EXEC] Starting execution monitoring...");

  // One-time backfill: process any existing pending execution requests
  (async () => {
    try {
      const all = await localDB.allDocs({ include_docs: true });
      const pendingExecs = (all.rows || [])
        .map((r) => r.doc)
        .filter(
          (d) => d && d.type === "execution_request" && d.status === "pending"
        );
      if (pendingExecs.length > 0) {
        console.log(
          `📋 [EXEC] Found ${pendingExecs.length} existing pending execution requests. Processing once...`
        );
      }
      for (const doc of pendingExecs) {
        try {
          handlePouchDBExecution(doc);
        } catch (_) {}
      }
    } catch (e) {
      console.warn(
        "⚠️ [EXEC] Failed to backfill pending execution requests:",
        e
      );
    }
  })();

  changeListener = localDB.changes({
    since: "now", // Avoid replaying old changes on each wake
    live: true,
    include_docs: true,
  });

  changeListener.on("change", (change) => {
    console.log("🎯 [POUCH-MONITOR] Change detected in local PouchDB:", {
      id: change.id,
      seq: change.seq,
      deleted: change.deleted,
      doc_type: change.doc?.type,
      status: change.doc?.status,
    });
    if (change.doc && change.doc.type === "execution_request") {
      if (change.doc.status === "pending") {
        console.log(
          "🚀 [POUCH-MONITOR] Pending execution request detected:",
          change.doc._id
        );
        console.log(
          "🔄 [POUCH-MONITOR] Routing to PouchDB execution (EXECUTE_ACTIONS & RULE_ENGINE_EXECUTE_ACTIONS bypassed)"
        );
        handlePouchDBExecution(change.doc);
      } else {
        console.log(
          "⏭️ [POUCH-MONITOR] Skipping execution request due to status:",
          change.doc.status
        );
      }
    }
  });

  console.log("✅ [EXEC] Execution monitoring started");
}

// Sanitize broker order id from UI (remove leading '#' and surrounding spaces)
function cleanOrderId(val) {
  try {
    const s = (val ?? "").toString().trim();
    return s.startsWith("#") ? s.slice(1).trim() : s;
  } catch (_) {
    return (val ?? "").toString();
  }
}

// Enhanced order id cleaning for robust matching - removes all non-alphanumeric characters
function cleanOrderIdForMatching(val) {
  try {
    const s = (val ?? "").toString().trim();
    // Remove all non-alphanumeric characters for comparison
    return s.replace(/[^a-zA-Z0-9]/g, "").toUpperCase();
  } catch (_) {
    return "";
  }
}

/**
 * Normalize action arguments to canonical keys and types
 */
function normalizeActionArguments(action) {
  try {
    const args = action?.arguments || {};

    const pickFirst = (...candidates) => {
      for (const c of candidates) {
        if (c !== undefined && c !== null) return c;
      }
      return undefined;
    };

    const rawSymbol = pickFirst(
      args.symbol,
      args.SYMBOL,
      action?.SYMBOL,
      action?.symbol,
      // For Monitor actions, symbol is nested in condition
      args.condition?.symbol,
      args.condition?.SYMBOL
    );
    const rawExchange = pickFirst(
      args.exchange,
      args.EXCHANGE,
      action?.exchange,
      action?.EXCHANGE,
      // For Monitor actions, exchange may be nested in condition
      args.condition?.exchange,
      args.condition?.EXCHANGE
    );
    const rawQuantity = pickFirst(
      args.quantity,
      args.QUANTITY,
      action?.QUANTITY,
      action?.quantity
    );
    const rawPrice = pickFirst(
      args.price,
      args.PRICE,
      action?.PRICE,
      action?.price
    );
    const rawProductType = pickFirst(
      args.productType,
      args.PRODUCT_TYPE,
      action?.PRODUCT_TYPE,
      action?.productType
    );

    const toNumberOrZero = (val) => {
      const n = Number(val);
      return Number.isFinite(n) ? n : 0;
    };

    return {
      action: action.action.toLowerCase(),
      symbol: (rawSymbol.toLowerCase() ?? "UNKNOWN").toString(),
      exchange: rawExchange ? rawExchange.toString() : undefined,
      quantity: toNumberOrZero(rawQuantity),
      price: toNumberOrZero(rawPrice),
      productType: (rawProductType ?? "MIS").toString(),
      triggerPrice: args.triggerPrice,
      limitPrice: args.limitPrice,
    };
  } catch (_) {
    return {
      action: action.action.toLowerCase(),
      symbol: "UNKNOWN",
      exchange: undefined,
      quantity: 0,
      price: 0,
      productType: "MIS",
    };
  }
}

/**
 * Retry PouchDB document operations with conflict resolution
 */
async function retryDocumentOperation(operation, maxRetries = 3) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (e) {
      if (e.status === 409 && attempt < maxRetries) {
        console.log(
          `🔄 [POUCH-STORAGE] Conflict retry ${attempt}/${maxRetries}`
        );
        // Exponential backoff: 50ms, 100ms, 200ms
        await new Promise((resolve) =>
          setTimeout(resolve, 50 * Math.pow(2, attempt - 1))
        );
        continue;
      }
      throw e;
    }
  }
}

// Buffered bulk upsert for order_* docs to minimize conflicts under parallel execution
const orderUpsertBuffer = {
  queue: new Map(),
  timer: null,
  flushInMs: 75,
  flushing: false,
};
const MAX_ORDER_UPSERT_BUFFER_SIZE = 100; // Optional cap to force early flush

function enqueueOrderUpsert(docId, draft, immediate = false) {
  try {
    const prev = orderUpsertBuffer.queue.get(docId) || {};
    orderUpsertBuffer.queue.set(docId, { ...prev, ...draft, _id: docId });
    const isCritical =
      (draft?.status || "").toString().toLowerCase() === "cancelled";

    // Immediate flush for critical updates (e.g., cancelled orders) or when buffer is large
    if (
      immediate ||
      isCritical ||
      orderUpsertBuffer.queue.size >= MAX_ORDER_UPSERT_BUFFER_SIZE
    ) {
      if (orderUpsertBuffer.timer) clearTimeout(orderUpsertBuffer.timer);
      flushOrderUpserts().catch(() => {});
      return;
    }

    // Debounced flush for regular updates
    if (orderUpsertBuffer.timer) clearTimeout(orderUpsertBuffer.timer);
    orderUpsertBuffer.timer = setTimeout(() => {
      flushOrderUpserts().catch(() => {});
    }, orderUpsertBuffer.flushInMs);
  } catch (_) {}
}

async function flushOrderUpserts() {
  if (orderUpsertBuffer.flushing) return;
  const items = Array.from(orderUpsertBuffer.queue.values());
  orderUpsertBuffer.queue.clear();
  if (items.length === 0) return;

  orderUpsertBuffer.flushing = true;
  try {
    const keys = items.map((d) => d._id);
    let existingById = {};
    try {
      const res = await localDB.allDocs({ keys, include_docs: true });
      existingById = Object.fromEntries(
        res.rows.filter((r) => r.doc && !r.error).map((r) => [r.id, r.doc])
      );
    } catch (_) {}

    const prepared = items.map((draft) => {
      const existing = existingById[draft._id];
      return existing
        ? {
            ...existing,
            ...draft,
            _rev: existing._rev,
            updated_at: new Date().toISOString(),
          }
        : { ...draft, updated_at: new Date().toISOString() };
    });

    const results = await localDB.bulkDocs(prepared);

    // Retry conflicts once with fresh rev
    const retryIds = results
      .filter((r) => r && r.error && r.status === 409)
      .map((r) => r.id);
    if (retryIds.length > 0) {
      const retryDocs = [];
      for (const id of retryIds) {
        try {
          const fresh = await localDB.get(id);
          const draft =
            prepared.find((d) => d._id === id) ||
            items.find((d) => d._id === id);
          if (draft) {
            retryDocs.push({
              ...fresh,
              ...draft,
              _rev: fresh._rev,
              updated_at: new Date().toISOString(),
            });
          }
        } catch (_) {}
      }
      if (retryDocs.length > 0) {
        await localDB.bulkDocs(retryDocs);
      }
    }
  } finally {
    orderUpsertBuffer.flushing = false;
  }
}

/**
 * Store execution results as order_result and monitoring_alert documents
 */
async function storeExecutionResults(executionResult, originalRequest) {
  if (!localDB) {
    console.warn(
      "⚠️ [POUCH-STORAGE] PouchDB not initialized, cannot store results"
    );
    return;
  }

  console.log(
    "🚀 [POUCH-STORAGE] ===== STARTING EXECUTION RESULTS STORAGE ====="
  );
  console.log("�� [POUCH-STORAGE] Updating execution report status...");
  console.log("📋 [POUCH-STORAGE] Original request ID:", originalRequest._id);
  console.log("📋 [POUCH-STORAGE] Firebase UID:", originalRequest.firebase_uid);
  console.log("📊 [POUCH-STORAGE] Execution success:", executionResult.success);
  console.log("📊 [POUCH-STORAGE] Execution message:", executionResult.message);

  const executionRequestId = originalRequest._id;

  try {
    // Update the execution request status to completed
    try {
      // Try to get existing execution request document
      const existingDoc = await localDB
        .get(executionRequestId)
        .catch(() => null);
      if (existingDoc) {
        console.log(
          "🔄 [POUCH-STORAGE] Execution request document exists, updating status..."
        );

        // Update only the status and add execution result
        const updatedDoc = {
          ...existingDoc,
          status: executionResult.success ? "completed" : "failed",
          execution_result: executionResult,
          updated_at: new Date().toISOString(),
        };

        await localDB.put(updatedDoc);
        console.log(
          "✅ [POUCH-STORAGE] Execution request status updated successfully!"
        );

        console.log("📊 [POUCH-STORAGE] New status:", updatedDoc.status);
      } else {
        console.log(
          "⚠️ [POUCH-STORAGE] Execution request document not found, cannot update status"
        );
      }
    } catch (updateError) {
      if (updateError.status === 409) {
        console.log(
          "⚠️ [POUCH-STORAGE] Conflict detected, retrying with fresh _rev..."
        );
        // Get fresh document and retry
        const freshDoc = await localDB.get(executionRequestId);
        const updatedDoc = {
          ...freshDoc,
          status: executionResult.success ? "completed" : "failed",
          execution_result: executionResult,
          updated_at: new Date().toISOString(),
        };

        await localDB.put(updatedDoc);
        console.log(
          "✅ [POUCH-STORAGE] Execution request status updated after conflict resolution!"
        );
      } else {
        throw updateError; // Re-throw if it's not a conflict error
      }
    }
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] ===== STATUS UPDATE FAILED =====");
    console.error("❌ [POUCH-STORAGE] Error details:", error);
    console.error("❌ [POUCH-STORAGE] Error stack:", error.stack);
    console.error("❌ [POUCH-STORAGE] Request ID:", originalRequest._id);
  }
}

/**
 * Update order status by composite ID
 */
async function updateOrderStatus(
  executionRequestId,
  actionId,
  newStatus,
  additionalData = {}
) {
  if (!localDB) return;

  const docId = `order_${executionRequestId}_${actionId}`;

  try {
    const doc = await localDB.get(docId);
    const updatedDoc = {
      ...doc,
      status: newStatus,
      updated_at: new Date().toISOString(),
      ...additionalData,
    };

    await localDB.put(updatedDoc);
    console.log(
      "✅ [POUCH-STORAGE] Order status updated:",
      docId,
      "→",
      newStatus
    );
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] Failed to update order status:", error);
  }
}

/**
 * Update monitoring alert progress
 */
async function updateMonitoringProgress(
  executionRequestId,
  actionId,
  currentPrice,
  progressPercent,
  status = "inProgress"
) {
  if (!localDB) return;

  // Derive a monitoring-specific composite id without embedding exec_
  const sanitizedGraphId = (executionRequestId || "")
    .toString()
    .replace(/^exec_/, "");
  const docId = `monitor_${sanitizedGraphId}_${actionId}`;

  try {
    const doc = await localDB.get(docId);
    const updatedDoc = {
      ...doc,
      currentPrice: currentPrice.toString(),
      progressPercent,
      status,
      updated_at: new Date().toISOString(),
    };

    await localDB.put(updatedDoc);
    console.log("✅ [POUCH-STORAGE] Monitoring progress updated:", docId);
  } catch (error) {
    console.error("❌ [POUCH-STORAGE] Failed to update monitoring:", error);
  }
}

/**
 * Upsert order_result docs using statuses from login monitor tab
 * - openOrders: orders shown in Pending table → broker_status + status=inProgress
 * - completedOrders: orders from Completed table → broker_status + status executed/cancelled
 */
async function upsertOrdersFromMonitor(openOrders = [], completedOrders = []) {
  if (!localDB) {
    console.warn(
      "[POUCH-STORAGE] upsertOrdersFromMonitor: localDB not initialized"
    );
    return { success: false, message: "PouchDB not initialised" };
  }

  try {
    const firebaseUID = await getDataFromCache("firebase_uid");
    if (!firebaseUID) {
      return { success: false, message: "Missing firebase_uid in cache" };
    }

    try {
      const oc = Array.isArray(openOrders) ? openOrders.length : 0;
      const cc = Array.isArray(completedOrders) ? completedOrders.length : 0;
      const oids = (openOrders || []).filter(
        (r) => !!cleanOrderId(r?.detailedInfo?.orderId)
      ).length;
      const coids = (completedOrders || []).filter(
        (r) => !!cleanOrderId(r?.detailedInfo?.orderId)
      ).length;
      console.log(
        `[POUCH-STORAGE] Monitor upsert start: open=${oc} (withId=${oids}), completed=${cc} (withId=${coids})`
      );

      // Debug: Show sample order IDs from monitor data
      const openOrderIds = (openOrders || [])
        .map((r) => ({
          raw: r?.detailedInfo?.orderId,
          cleaned: cleanOrderId(r?.detailedInfo?.orderId),
        }))
        .filter((o) => o.raw)
        .slice(0, 5); // First 5 samples

      const completedOrderIds = (completedOrders || [])
        .map((r) => ({
          raw: r?.detailedInfo?.orderId,
          cleaned: cleanOrderId(r?.detailedInfo?.orderId),
        }))
        .filter((o) => o.raw)
        .slice(0, 5); // First 5 samples

      if (openOrderIds.length > 0 || completedOrderIds.length > 0) {
        console.log("[POUCH-STORAGE] DEBUG - Sample monitor order IDs:", {
          openSamples: openOrderIds,
          completedSamples: completedOrderIds,
        });
      }
    } catch (_) {}

    const normalize = (s) => (s || "").toString().trim().toUpperCase();
    const toTradeType = (t) => (normalize(t).includes("BUY") ? "BUY" : "SELL");
    const buildKey = (symbol, type) =>
      `${normalize(symbol)}|${toTradeType(type)}`;

    // Build monitor lookups strictly by cleaned orderId
    const byOrderId = new Map(); // cleaned orderId -> { row, completed: boolean }

    for (const row of openOrders || []) {
      const oid = cleanOrderId(row?.detailedInfo?.orderId);
      if (oid) byOrderId.set(oid, { row, completed: false });
    }
    for (const row of completedOrders || []) {
      const oid = cleanOrderId(row?.detailedInfo?.orderId);
      if (oid) byOrderId.set(oid, { row, completed: true });
    }

    // Fetch all order_result docs for this user
    const all = await localDB.allDocs({
      include_docs: true,
      startkey: "order_",
      endkey: "order_\uffff",
    });
    const candidateDocs = (all.rows || [])
      .map((r) => r.doc)
      .filter(
        (d) =>
          d &&
          d.type === "order_result" &&
          d.firebase_uid === firebaseUID &&
          d.status === "inProgress"
      );
    console.log(
      `[POUCH-STORAGE] In-progress order docs to consider: ${candidateDocs.length}`
    );

    // Build bulk updates first
    const updates = [];
    const updateFieldsById = new Map();
    const originalDocById = new Map();
    const cancelledIds = new Set();

    for (const [docIndex, doc] of candidateDocs.entries()) {
      try {
        console.log("[order polling ] evaluating in-progress order doc", {
          docIndex,
          doc_id: doc._id,
          symbol: doc.symbol,
          tradeType: doc.tradeType,
          product: doc.product,
          orderType: doc.orderType,
          quantity: doc.quantity,
          status: doc.status,
          broker_order_id: doc.broker_order_id,
        });
      } catch (_) {}

      const brokerId = cleanOrderId(doc.broker_order_id);
      if (!brokerId) {
        try {
          console.log(
            "[POUCH-STORAGE] Skipping doc without broker_order_id under strict orderId policy",
            { id: doc._id }
          );
        } catch (_) {}
        continue;
      }

      const found = byOrderId.get(brokerId);
      if (!found) {
        try {
          console.log("[order polling ] no monitor match for order doc", {
            docIndex,
            doc_id: doc._id,
            broker_order_id: doc.broker_order_id,
            cleaned: brokerId,
            monitor_keys_sample: Array.from(byOrderId.keys()).slice(0, 10),
            total_monitor_orders: byOrderId.size,
          });
        } catch (_) {}
        continue;
      }

      const source = found.row;
      const isCompleted = found.completed;
      const brokerStatus = (source.status || "").toString().trim();
      const s = brokerStatus.toUpperCase();
      let mappedStatus = doc.status || "inProgress";
      if (isCompleted) {
        if (
          s.includes("CANCEL") ||
          s.includes("REJECT") ||
          s.includes("UNKNOWN")
        )
          mappedStatus = "cancelled";
        else mappedStatus = "executed";
      } else {
        mappedStatus = "inProgress"; // open orders remain inProgress; only broker_status is updated semantically
      }

      const updateFields = {
        broker_status: brokerStatus,
        status: mappedStatus,
        execution_details: {
          ...(doc.execution_details || {}),
          monitor_snapshot: source,
        },
        updated_at: new Date().toISOString(),
      };

      const docToUpdate = { ...doc, ...updateFields };
      updates.push(docToUpdate);
      updateFieldsById.set(doc._id, updateFields);
      originalDocById.set(doc._id, doc);
      if ((mappedStatus || "").toString().toLowerCase() === "cancelled") {
        cancelledIds.add(doc._id);
      }
    }

    if (updates.length === 0) {
      console.log(
        "[order polling ] upsert completed: 0 docs updated (no matches)"
      );
      return { success: true, updated: 0 };
    }

    // First bulk attempt
    let updatedCount = 0;
    const successIds = new Set();
    try {
      const res = await localDB.bulkDocs(updates);
      for (const r of res || []) {
        if (r && r.ok && r.id) {
          updatedCount++;
          successIds.add(r.id);
        }
      }
    } catch (e) {
      console.warn(
        "[order polling ] bulkDocs failed on first attempt:",
        e?.message || e
      );
    }

    // Retry conflicts
    const retryUpdates = [];
    try {
      const res = await localDB.bulkDocs(updates);
      for (const r of res || []) {
        if (r && r.error && r.status === 409 && r.id) {
          try {
            const fresh = await localDB.get(r.id);
            const updateFields = updateFieldsById.get(r.id) || {};
            retryUpdates.push({
              ...fresh,
              ...updateFields,
              _rev: fresh._rev,
              updated_at: new Date().toISOString(),
            });
          } catch (gErr) {
            console.warn(
              "[order polling ] failed to get fresh doc for retry:",
              r.id,
              gErr?.message || gErr
            );
          }
        }
      }
    } catch (_) {}

    if (retryUpdates.length > 0) {
      try {
        const retryRes = await localDB.bulkDocs(retryUpdates);
        for (const r of retryRes || []) {
          if (r && r.ok && r.id && !successIds.has(r.id)) {
            updatedCount++;
            successIds.add(r.id);
          }
        }
      } catch (e) {
        console.warn(
          "[order polling ] bulkDocs retry failed:",
          e?.message || e
        );
      }
    }

    console.log(
      `[order polling ] upsert completed: ${updatedCount} docs updated (of ${candidateDocs.length} candidates)`
    );

    // Trigger cascades for successfully-updated cancelled parents
    try {
      for (const id of Array.from(cancelledIds)) {
        if (!successIds.has(id)) continue;
        const doc = originalDocById.get(id) || {};
        const graphId = doc.execution_request_id || "";
        const parentId = doc.action_id || "";
        if (graphId && parentId) {
          console.log(
            "[Cancel Order P][POUCHDB_BULK_CANCEL] Detected parent cancellation via monitor upsert (bulk)",
            { graphId, parentId }
          );
          await triggerCascadeForParent(graphId, parentId);
        }
      }
    } catch (cascadeErr) {
      console.warn(
        "[Cancel Order P][POUCHDB_BULK_CANCEL] Failed to trigger cascade after monitor cancel (bulk):",
        cascadeErr?.message || cascadeErr
      );
    }

    return { success: true, updated: updatedCount };
  } catch (error) {
    console.error("[POUCH-STORAGE] upsertOrdersFromMonitor failed:", error);
    return { success: false, message: error?.message || String(error) };
  }
}

globalThis.__upsertOrdersFromMonitor = upsertOrdersFromMonitor;

/**
 * Trigger cascade cancellation/stopping for dependents of a parent node
 * by reading the execution_request primitives and computing dependents.
 */
async function triggerCascadeForParent(graphId, parentId) {
  try {
    const execDoc = await localDB.get(graphId);
    const actions = Array.isArray(execDoc?.primitives)
      ? execDoc.primitives
      : [];
    if (actions.length === 0) {
      console.warn(
        "[Cancel Order P][POUCHDB_BULK_CANCEL] No primitives found for graph, skipping cascade",
        { graphId }
      );
      return { success: false, message: "No actions to cascade" };
    }

    // Build dependents map
    const dependents = new Map();
    const getId = (a, idx) =>
      a?.id
        ? String(a.id)
        : a?.action
        ? `${a.action}_${idx + 1}`
        : `ACTION_${idx + 1}`;
    const ids = actions.map((a, idx) => ({
      id: getId(a, idx),
      action: a?.action || "",
      depends_on: a?.depends_on,
    }));
    ids.forEach((a) => {
      const deps = Array.isArray(a.depends_on)
        ? a.depends_on
        : a.depends_on
        ? [a.depends_on]
        : [];
      deps.forEach((d) => {
        if (!dependents.has(d)) dependents.set(d, []);
        dependents.get(d).push(a.id);
      });
    });

    const toCancel = [];
    const seen = new Set();
    const isACT = (id) => {
      const a = ids.find((x) => x.id === id);
      const name = (a?.action || "").toString().toUpperCase();
      return [
        "MONITOR",
        "MONITORPROFIT",
        "MONITORSYMBOLFROMWATCHLIST",
        "MONITORCONDITIONTHENACT",
      ].includes(name);
    };
    const walk = (id) => {
      if (seen.has(id)) return;
      seen.add(id);
      const children = dependents.get(id) || [];
      children.forEach((child) => {
        toCancel.push({
          nodeId: child,
          isACT: isACT(child),
          docPrefix: isACT(child) ? "monitor_" : "order_",
          newStatus: isACT(child) ? "stopped" : "cancelled",
        });
        walk(child);
      });
    };

    // Include parent for doc update assurance
    toCancel.push({
      nodeId: parentId,
      isACT: isACT(parentId),
      docPrefix: isACT(parentId) ? "monitor_" : "order_",
      newStatus: isACT(parentId) ? "stopped" : "cancelled",
    });
    walk(parentId);

    console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] Computed cascade set", {
      graphId,
      parentId,
      count: toCancel.length,
      nodes: toCancel,
    });
    const res = await handleBulkCancelRequest({ graphId, nodes: toCancel });
    console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] Cascade result", res);
    return res;
  } catch (e) {
    console.warn(
      "[Cancel Order P][POUCHDB_BULK_CANCEL] triggerCascadeForParent failed",
      e?.message || e
    );
    return { success: false, message: e?.message || String(e) };
  }
}

/**
 * Handle bulk cancellation request for multiple nodes
 * Updates order_* docs to cancelled and monitor_* docs to stopped
 * @param {Object} request - Request object with graphId and nodes array
 */
async function handleBulkCancelRequest(request) {
  if (!localDB) {
    console.warn("[POUCHDB_BULK_CANCEL] localDB not initialized");
    return { success: false, error: "PouchDB not initialized" };
  }

  const { graphId, nodes } = request;
  try {
    console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] request received", {
      graphId,
      nodesCount: Array.isArray(nodes) ? nodes.length : 0,
    });
  } catch (_) {}
  try {
    console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] request detail", {
      nodes: (nodes || []).map((n) => ({
        nodeId: n?.nodeId,
        isACT: n?.isACT,
        docPrefix: n?.docPrefix,
        newStatus: n?.newStatus,
      })),
    });
  } catch (_) {}
  if (!Array.isArray(nodes) || nodes.length === 0) {
    return { success: false, error: "No nodes provided for cancellation" };
  }

  console.log(
    `[POUCHDB_BULK_CANCEL] Processing bulk cancel for ${nodes.length} nodes in graph ${graphId}`
  );

  const results = [];
  let successCount = 0;
  let errorCount = 0;

  // Get cached firebase_uid for creating minimal docs
  const firebaseUID = await getDataFromCache("firebase_uid");

  for (const node of nodes) {
    const { nodeId, isACT, docPrefix, newStatus } = node;
    // IMPORTANT: For monitor_* docs elsewhere we strip leading exec_ from graphId
    const gid = isACT
      ? (graphId || "").toString().replace(/^exec_/, "")
      : graphId;
    const docId = `${docPrefix}${gid}_${nodeId}`;
    try {
      console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] node handling", {
        nodeId,
        isACT,
        docPrefix,
        newStatus,
        docId,
        originalGraphId: graphId,
        gid,
      });
    } catch (_) {}

    try {
      // Try to update existing document with 409 conflict retry
      const updateResult = await updateDocumentWithRetry(
        docId,
        newStatus,
        isACT,
        firebaseUID,
        graphId,
        nodeId
      );
      try {
        console.log(" [Cancel Order P][POUCHDB_BULK_CANCEL] update result", {
          docId,
          attempt: updateResult.attempt,
          success: updateResult.success,
          error: updateResult.error,
        });
      } catch (_) {}

      if (updateResult.success) {
        successCount++;
        results.push({
          nodeId,
          docId,
          status: newStatus,
          updated: true,
        });
        console.log(
          `[POUCHDB_BULK_CANCEL] Successfully updated ${docId} to ${newStatus}`
        );
      } else {
        errorCount++;
        results.push({
          nodeId,
          docId,
          error: updateResult.error,
          updated: false,
        });
        console.warn(
          `[POUCHDB_BULK_CANCEL] Failed to update ${docId}:`,
          updateResult.error
        );
      }
    } catch (error) {
      errorCount++;
      results.push({
        nodeId,
        docId,
        error: error.message,
        updated: false,
      });
      console.error(`[POUCHDB_BULK_CANCEL] Error processing ${docId}:`, error);
    }
  }

  console.log(
    `[POUCHDB_BULK_CANCEL] Bulk cancel completed: ${successCount} successful, ${errorCount} failed`
  );
  try {
    console.log("[Cancel Order P][POUCHDB_BULK_CANCEL] summary", {
      updated: successCount,
      failed: errorCount,
      results,
    });
  } catch (_) {}

  return {
    success: errorCount === 0,
    updated: successCount,
    failed: errorCount,
    results,
  };
}

/**
 * Update document with 409 conflict retry logic
 * Creates minimal document if missing
 */
async function updateDocumentWithRetry(
  docId,
  newStatus,
  isACT,
  firebaseUID,
  graphId,
  nodeId,
  maxRetries = 3
) {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Try to get existing document
      let doc;
      try {
        console.log(
          "[Cancel Order P][POUCHDB_BULK_CANCEL] updateWithRetry begin",
          { docId, newStatus, isACT, graphId, nodeId }
        );
      } catch (_) {}
      try {
        doc = await localDB.get(docId);
        try {
          console.log(
            "[Cancel Order P][POUCHDB_BULK_CANCEL] fetched existing doc",
            {
              docId,
              hasRev: !!doc?._rev,
              type: doc?.type,
              currentStatus: doc?.status,
            }
          );
        } catch (_) {}
      } catch (error) {
        if (error.status === 404) {
          // Document doesn't exist, create minimal doc as per memory guidance
          console.log(
            `[POUCHDB_BULK_CANCEL] Creating minimal doc for ${docId}`
          );
          doc = createMinimalCancelDoc(
            docId,
            isACT,
            firebaseUID,
            graphId,
            nodeId
          );
          try {
            console.log(
              "[Cancel Order P][POUCHDB_BULK_CANCEL] created minimal doc",
              { docId, isACT, firebaseUID, graphId, nodeId }
            );
          } catch (_) {}
        } else {
          throw error;
        }
      }

      // Update the document with monotonic guard for monitors
      let finalStatus = newStatus;
      try {
        if (isACT) {
          const rank = {
            inProgress: 0,
            stopped: 1,
            triggered: 2,
            order_executed: 3,
          };
          const prev = (doc?.status || "").toString();
          const next = (newStatus || "").toString();
          const pr = rank[prev] ?? -1;
          const nr = rank[next] ?? -1;
          if (nr < pr) {
            finalStatus = prev; // do not downgrade monitors
          }
          try {
            console.log("[monitor-debug] bulk-cancel status check", {
              docId,
              isACT,
              prevStatus: prev,
              requestedStatus: newStatus,
              finalStatus,
            });
          } catch (_) {}
        }
      } catch (_) {}

      const updatedDoc = {
        ...doc,
        status: finalStatus,
        cancelled_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      try {
        console.log(
          "[Cancel Order P][POUCHDB_BULK_CANCEL] putting updated doc",
          { docId, newStatus, isACT, hasRev: !!updatedDoc?._rev }
        );
      } catch (_) {}
      await localDB.put(updatedDoc);
      try {
        if (isACT) {
          console.log("[monitor-debug] bulk-cancel put", {
            docId,
            finalStatus,
          });
        }
      } catch (_) {}
      return { success: true, attempt };
    } catch (error) {
      if (error.status === 409 && attempt < maxRetries) {
        console.log(
          `[POUCHDB_BULK_CANCEL] Conflict detected for ${docId}, retrying attempt ${
            attempt + 1
          }/${maxRetries}`
        );
        // Brief delay before retry
        await new Promise((resolve) => setTimeout(resolve, 50 * attempt));
        continue;
      }
      try {
        console.warn(
          "[Cancel Order P][POUCHDB_BULK_CANCEL] updateWithRetry error",
          { docId, attempt, error: error?.message }
        );
      } catch (_) {}

      return {
        success: false,
        error: error.message || String(error),
        attempt,
      };
    }
  }

  return {
    success: false,
    error: `Max retries (${maxRetries}) exceeded for ${docId}`,
  };
}

/**
 * Create minimal document for cancellation when original doc is missing
 */
function createMinimalCancelDoc(docId, isACT, firebaseUID, graphId, nodeId) {
  const timestamp = new Date().toISOString();

  if (isACT) {
    // Create minimal monitor_* document
    return {
      _id: docId,
      type: "monitoring_result",
      firebase_uid: firebaseUID,
      graph_id: graphId,
      node_id: nodeId,
      status: "stopped",
      created_at: timestamp,
      updated_at: timestamp,
      cancelled_at: timestamp,
      reason: "dependency_cancelled",
    };
  } else {
    // Create minimal order_* document
    return {
      _id: docId,
      type: "order_result",
      firebase_uid: firebaseUID,
      graph_id: graphId,
      node_id: nodeId,
      status: "cancelled",
      created_at: timestamp,
      updated_at: timestamp,
      cancelled_at: timestamp,
      reason: "dependency_cancelled",
    };
  }
}

/**
 * Handle execution requests from PouchDB (REPLACES Chrome runtime API for all execution)
 */
async function handlePouchDBExecution(executionDoc) {
  try {
    console.log(
      "🚀 [POUCH-EXEC] Processing execution request:",
      executionDoc._id
    );
    console.log(
      "🔄 [POUCH-EXEC] Chrome runtime API bypassed - using PouchDB model"
    );

    if (executionDoc.primitives && executionDoc.primitives.length > 0) {
      console.log(
        "📋 [POUCH-EXEC] Executing primitives:",
        executionDoc.primitives.length
      );
      console.log("🎯 [POUCH-EXEC] Primitives:", executionDoc.primitives);

      // Use real execution logic for all PouchDB execution requests
      // This will attempt real DOM automation and fail properly if not on correct site
      console.log(
        "🔄 [POUCH-EXEC] Using real execution path with ExecutionService"
      );
      // Mark the execution request as inProgress before invoking the engine
      try {
        if (localDB && executionDoc && executionDoc._id) {
          // Guard: Only proceed if currently pending
          if (executionDoc.status !== "pending") {
            console.log(
              "⏭️ [POUCH-EXEC] Execution doc is not pending (",
              executionDoc.status,
              ") — skipping execution."
            );
            return; // Do not execute again
          }
          let updatedDoc = {
            ...executionDoc,
            status: "inProgress",
            updated_at: new Date().toISOString(),
          };
          try {
            await localDB.put(updatedDoc);
          } catch (err) {
            if (err && err.status === 409) {
              const latest = await localDB.get(executionDoc._id);
              if (latest.status !== "pending") {
                console.log(
                  "⏭️ [POUCH-EXEC] Latest doc status is not pending (",
                  latest.status,
                  ") — skipping execution."
                );
                return; // Another worker already progressed it
              }
              updatedDoc = {
                ...latest,
                status: "inProgress",
                updated_at: new Date().toISOString(),
              };
              await localDB.put(updatedDoc);
            } else {
              throw err;
            }
          }
          console.log(
            "📌 [POUCH-EXEC] Marked execution request inProgress:",
            executionDoc._id
          );
        } else {
          console.warn(
            "⚠️ [POUCH-EXEC] Cannot mark inProgress - localDB or _id missing"
          );
        }
      } catch (markErr) {
        console.warn(
          "⚠️ [POUCH-EXEC] Failed to mark execution as inProgress:",
          markErr
        );
        return; // Safety: don't proceed if we couldn't secure inProgress
      }
      const result = await handleExecutionRequest({
        actions: executionDoc.primitives,
        automationMode:
          executionDoc.automationMode || globalThis.AUTOMATION_MODES.BACKGROUND,
        siteId: executionDoc.siteId || "kiteByZerodha", // Default to Zerodha
        tabId: executionDoc.tabId,
        graphId: executionDoc._id,
        firebase_uid: executionDoc.firebase_uid,
      });

      console.log(
        "✅ [POUCH-EXEC] Execution completed for:",
        executionDoc._id,
        "Result:",
        result
      );

      // Store execution results in PouchDB
      await storeExecutionResults(result, executionDoc);
    } else {
      console.warn("⚠️ [POUCH-EXEC] No primitives found in execution request");
    }
  } catch (error) {
    console.error("❌ [POUCH-EXEC] Execution failed:", error);
  }
}

/**
 * Initialize the background script
 */
async function initializeBackground() {
  try {
    // Initialize PouchDB for execution polling (optional)
    const pouchReady = await initializePouchDB();
    if (!pouchReady) {
      console.warn(
        "PouchDB was not initialised yet; proceeding to initialize engine and login monitor."
      );
    }
  } catch {}

  if (initializing) {
    console.warn("Alread being initialised.");
    return; // Prevent concurrent inits
  }
  if (engineController) {
    console.warn("Alread initialised.");
    return; // Already initialized
  }

  initializing = true;
  try {
    console.log("🚀 Initializing background script...");

    // Load timeout settings
    await loadTimeoutSettings();

    // Initialize the real EngineController
    try {
      if (typeof globalThis.EngineController !== "undefined") {
        console.log("🔧 [DEBUG] Creating EngineController instance...");
        engineController = new globalThis.EngineController();
        await engineController.initialize();
        console.log(
          "✅ [DEBUG] Real EngineController initialized successfully"
        );
        // Do NOT auto-start login monitor; we'll run one-shot checks on demand
      } else {
        throw new Error("EngineController not found in global scope");
      }
    } catch (engineError) {
      console.error(
        "❌ [DEBUG] Engine controller initialization failed:",
        engineError
      );
      // Create a fallback mock
      engineController = {
        executeActionArray: async (graphId, actions) => {
          console.log(
            "🔄 [FALLBACK] Using fallback execution for:",
            actions.length,
            "actions"
          );
          return {
            success: false,
            message: "Engine controller initialization failed",
            error: engineError.message,
          };
        },
      };
    }
    startExecutionMonitoring();

    console.log("✅ Background script initialized successfully");
  } catch (error) {
    console.error("❌ Error initializing background script:", error);
  } finally {
    initializing = false;
  }
}

/**
 * Close previous version's monitor tab using stored tabId
 */
async function closeExistingMonitorTabFromStorage() {
  try {
    const data = await new Promise((res) =>
      chrome.storage.local.get(["loginMonitorTabId"], res)
    );
    const id = data?.loginMonitorTabId;
    if (id) {
      try {
        await chrome.tabs.remove(id);
      } catch (_) {}
      try {
        chrome.storage.local.remove("loginMonitorTabId");
      } catch (_) {}
    }
  } catch (_) {}
}

/**
 * Close previous version's observer monitor tab using stored tabId
 */
async function closeExistingObserverMonitorTabFromStorage() {
  try {
    const data = await new Promise((res) =>
      chrome.storage.local.get(["observerMonitorTabId"], res)
    );
    const id = data?.observerMonitorTabId;
    if (id) {
      try {
        await chrome.tabs.remove(id);
      } catch (_) {}
      try {
        chrome.storage.local.remove("observerMonitorTabId");
      } catch (_) {}
    }
  } catch (_) {}
}

/**
 * Close previous version's observer monitor tab using stored tabId
 */
async function closeExistingObserverMonitorTabFromStorage() {
  try {
    const data = await new Promise((res) =>
      chrome.storage.local.get(["observerMonitorTabId"], res)
    );
    const id = data?.observerMonitorTabId;
    if (id) {
      try {
        await chrome.tabs.remove(id);
      } catch (_) {}
      try {
        chrome.storage.local.remove("observerMonitorTabId");
      } catch (_) {}
    }
  } catch (_) {}
}

/**
 * Fallback: close pinned Orders tabs if storage is missing/stale
 */
async function closePinnedOrdersTabs() {
  try {
    const tabs = await chrome.tabs.query({
      pinned: true,
      url: "https://kite.zerodha.com/*",
    });
    for (const t of tabs || []) {
      if ((t.url || "").includes("/orders")) {
        try {
          await chrome.tabs.remove(t.id);
        } catch (_) {}
      }
    }
  } catch (_) {}
}

/**
 * Close all tabs inside the "Aagman Monitors" tab group (if present)
 */
async function closeSmartAgentMonitorGroupTabs() {
  try {
    const titles = ["Aagman Monitors", "SmartAgent Monitors"]; // support legacy title too if ajit or amit have old groups in chrome
    for (const title of titles) {
      const groups = await chrome.tabGroups.query({ title });
      for (const g of groups || []) {
        try {
          const tabs = await chrome.tabs.query({ groupId: g.id });
          const ids = (tabs || []).map((t) => t.id).filter(Boolean);
          if (ids.length > 0) {
            try {
              await chrome.tabs.remove(ids);
            } catch (_) {}
          }
          try {
            await chrome.tabGroups.update(g.id, { collapsed: true });
          } catch (_) {}
        } catch (_) {}
      }
    }
  } catch (_) {}
}

/**
 * Close all tabs inside the "SmartAgent Monitors" tab group (if present)
 */
async function closeSmartAgentMonitorGroupTabs() {
  try {
    const groups = await chrome.tabGroups.query({
      title: "SmartAgent Monitors",
    });
    for (const g of groups || []) {
      try {
        const tabs = await chrome.tabs.query({ groupId: g.id });
        const ids = (tabs || []).map((t) => t.id).filter(Boolean);
        if (ids.length > 0) {
          try {
            await chrome.tabs.remove(ids);
          } catch (_) {}
        }
        try {
          await chrome.tabGroups.update(g.id, { collapsed: true });
        } catch (_) {}
      } catch (_) {}
    }
  } catch (_) {}
}

/**
 * Handle execution requests using node-by-node execution
 */
async function handleExecutionRequest(request) {
  try {
    // Initialize engine controller if not already done
    if (!engineController) {
      console.warn("engineController is not initialised.");
      await initializeBackground();
    }
    const { actions, automationMode } = request;
    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    // For traditional execution, we'll use the execution service directly
    // This maintains backward compatibility
    let executionService;
    try {
      if (typeof globalThis.ExecutionService !== "undefined") {
        executionService = new globalThis.ExecutionService();
      } else if (typeof ExecutionService !== "undefined") {
        executionService = new ExecutionService();
      } else {
        console.warn(
          "⚠️ [DEBUG] ExecutionService not available, using fallback"
        );
        return {
          success: false,
          message: "ExecutionService not available in service worker",
        };
      }
    } catch (error) {
      console.error(
        "❌ [DEBUG] ExecutionService initialization failed:",
        error
      );
      return {
        success: false,
        message: "ExecutionService initialization failed",
      };
    }

    // Get action arguments configuration
    let actionArgumentsConfig;
    try {
      if (typeof globalThis.getActionArguments !== "undefined") {
        actionArgumentsConfig = await globalThis.getActionArguments();
      } else if (typeof getActionArguments !== "undefined") {
        actionArgumentsConfig = await getActionArguments();
      } else {
        console.warn(
          "⚠️ [DEBUG] getActionArguments not available, using fallback"
        );
        actionArgumentsConfig = {}; // Fallback empty config
      }
    } catch (error) {
      console.error("❌ [DEBUG] getActionArguments failed:", error);
      actionArgumentsConfig = {}; // Fallback empty config
    }

    // Convert siteId to site configuration
    let site = null;
    if (request.siteId) {
      try {
        let supportedSites;
        if (typeof globalThis.getSupportedSites !== "undefined") {
          supportedSites = await globalThis.getSupportedSites();
        } else if (typeof getSupportedSites !== "undefined") {
          supportedSites = await getSupportedSites();
        } else {
          console.warn(
            "⚠️ [DEBUG] getSupportedSites not available, using fallback"
          );
          return {
            success: false,
            message: "getSupportedSites not available in service worker",
          };
        }

        site = supportedSites[request.siteId];
        if (!site) {
          return {
            success: false,
            message: `Unsupported site: ${request.siteId}`,
          };
        }
        // Add the siteId to the site object for reference
        site.siteId = request.siteId;
      } catch (error) {
        console.error("❌ [DEBUG] getSupportedSites failed:", error);
        return { success: false, message: "Failed to get supported sites" };
      }
    }

    console.log("🔍 Execution request details:", {
      siteId: request.siteId,
      tabId: request.tabId,
      automationMode: automationMode || "currentTab",
      actionsCount: actions.length,
    });

    // Build a stable graph id: prefer provided graphId (exec_*) else synthesize one
    const tempGraphId =
      request && request.graphId
        ? request.graphId
        : `exec_${Date.now()}_${Math.random().toString(36).slice(2, 10)}`;

    // Ensure firebase_uid is present for downstream persistence
    if (!request.firebase_uid) {
      try {
        const uid = await getDataFromCache("firebase_uid");
        if (uid) request.firebase_uid = uid;
      } catch (_) {}
    }

    // Preprocess actions: split MonitorConditionThenAct into Monitor + ACT with dependency
    let expandedActions = [];
    try {
      for (let idx = 0; idx < actions.length; idx++) {
        const a = actions[idx] || {};
        const upper = (a.action || "").toString().toUpperCase();
        if (upper === "MONITORCONDITIONTHENACT") {
          const condition = a?.arguments?.condition ?? a?.condition;
          const onTrigger = a?.arguments?.on_trigger ?? a?.on_trigger;
          const baseId = a?.id ? String(a.id) : `MCTA_${idx + 1}`;
          const monitorAction = {
            ...a,
            id: baseId,
            action: "Monitor",
            arguments: { ...(a?.arguments || {}), condition },
          };
          // Remove on_trigger from monitor args/top-level if present to avoid confusion downstream
          try {
            if (monitorAction?.arguments?.on_trigger)
              delete monitorAction.arguments.on_trigger;
          } catch (_) {}
          try {
            if (
              Object.prototype.hasOwnProperty.call(monitorAction, "on_trigger")
            )
              delete monitorAction.on_trigger;
          } catch (_) {}
          const actAction = {
            id: `${baseId}_ACT`,
            action: onTrigger?.action,
            arguments: onTrigger?.arguments || {},
            depends_on: [baseId],
          };
          expandedActions.push(monitorAction);
          expandedActions.push(actAction);
        } else {
          expandedActions.push(a);
        }
      }
    } catch (mctaErr) {
      console.warn(
        "[Background] Failed to preprocess MonitorConditionThenAct split:",
        mctaErr
      );
      expandedActions = actions.slice();
    }

    // Normalize IDs to ensure callback actionId matches our lookup
    const seenIds = new Set();
    const normalizedActions = expandedActions.map((a, idx) => {
      const actionName = (a?.action || `ACTION_${idx + 1}`).toString();
      let id = a?.id ? String(a.id) : `${actionName}_${idx + 1}`;
      // Ensure uniqueness if user supplied duplicates
      while (seenIds.has(id)) {
        id = `${id}_${Math.random().toString(36).slice(2, 6)}`;
      }
      seenIds.add(id);
      return { ...a, id };
    });

    // Map actions by id for quick lookup in callback (use normalized list)
    const actionsById = new Map(normalizedActions.map((a) => [a.id, a]));

    // Execute the action array using the rule engine
    // Pre-create per-order docs with status inProgress so UI can render immediately using bulk operations
    try {
      const docsToCreate = [];
      const docIds = [];

      for (const action of normalizedActions) {
        const normalizedArgs = normalizeActionArguments(action);
        // Pre-create monitor docs similar to orders to show inProgress immediately
        const isMonitorAction = [
          "monitor",
          "monitorprofit",
          "monitorsymbolfromwatchlist",
        ].includes((normalizedArgs.action || "").toLowerCase());
        if (isMonitorAction) {
          const condition =
            action.condition || action.arguments?.condition || {};
          const sanitizedGraphId = (tempGraphId || "")
            .toString()
            .replace(/^exec_/, "");
          const docId = `monitor_${sanitizedGraphId}_${action.id}`;
          const nowIso = new Date().toISOString();

          docsToCreate.push({
            docId,
            baseDoc: {
              _id: docId,
              type: "monitoring_alert",
              firebase_uid: request.firebase_uid,
              execution_request_id: tempGraphId,
              action_id: action.id,
              id: docId,
              description: `Monitor ${condition.symbol || "UNKNOWN"} ${
                condition.operator || "condition"
              } ${condition.value ?? ""}`,
              symbol: condition.symbol || "UNKNOWN",
              triggerPrice: (condition.value ?? 0).toString(),
              currentPrice: "0",
              progress: "0/1",
              progressPercent: 0,
              status: "inProgress",
              // Minimal placeholders for UI compatibility
              orderType: "MONITOR",
              stopLoss: "",
              product: "MIS",
              created_at: nowIso,
              updated_at: nowIso,
            },
          });
          continue;
        }
        const isOrderAction = [
          "buy",
          "sell",
          "placebuylimitorder",
          "placeselllimitorder",
          "placebuystoplossmarketorder",
          "placesellstoplossmarketorder",
          "placebuystoplosslimitorder",
          "placesellstoplosslimitorder",
          "sellall",
          "exitallpositions",
        ].includes((normalizedArgs.action || "").toLowerCase());
        if (!isOrderAction) continue;

        // Do not pre-create order docs for ACT nodes derived from MonitorConditionThenAct
        const isActFromMcta =
          typeof action?.id === "string" && action.id.endsWith("_ACT");
        if (isActFromMcta) continue;

        const docId = `order_${tempGraphId}_${action.id}`;
        docIds.push(docId);
        const nowIso = new Date().toISOString();

        docsToCreate.push({
          docId,
          baseDoc: {
            _id: docId,
            type: "order_result",
            firebase_uid: request.firebase_uid,
            execution_request_id: tempGraphId,
            action_id: action.id,
            id: docId,
            symbol: normalizedArgs.symbol,
            triggerPrice: (normalizedArgs.triggerPrice || 0).toString(),
            limitPrice: (normalizedArgs.limitPrice || 0).toString(),
            tradeType: (normalizedArgs.action.toUpperCase() || "").includes(
              "BUY"
            )
              ? "BUY"
              : "SELL",
            action: normalizedArgs.action,
            quantity: (normalizedArgs.quantity || 0).toString(),
            price: (normalizedArgs.price || 0).toString(),
            status: "inProgress",
            timestamp: nowIso,
            broker: "zerodha",
            product: normalizedArgs.productType,
            orderType: (action.action || "").includes("Limit")
              ? "LIMIT"
              : "MARKET",
            broker_order_id: "",
            execution_details: {},
            created_at: nowIso,
            updated_at: nowIso,
          },
        });
      }

      if (docsToCreate.length > 0) {
        // First, try to get all existing docs in bulk
        const existingDocs = {};
        try {
          const allDocsResult = await localDB.allDocs({
            keys: docIds,
            include_docs: true,
          });
          for (const row of allDocsResult.rows) {
            if (row.doc && !row.error) {
              existingDocs[row.id] = row.doc;
            }
          }
        } catch (e) {
          console.warn(
            "⚠️ [POUCH-STORAGE] Failed to fetch existing docs, proceeding with creation:",
            e
          );
        }

        // Prepare bulk docs with proper revisions
        const bulkDocs = docsToCreate.map(({ docId, baseDoc }) => {
          const existing = existingDocs[docId];
          return existing
            ? {
                ...existing,
                ...baseDoc,
                _rev: existing._rev,
                updated_at: new Date().toISOString(),
              }
            : baseDoc;
        });

        // Use bulkDocs for atomic operation
        try {
          const results = await localDB.bulkDocs(bulkDocs);
          let successCount = 0;
          const conflicts = [];

          for (const result of results) {
            if (result.ok) {
              successCount++;
            } else if (result.error && result.status === 409) {
              conflicts.push(result.id);
            }
          }

          console.log(
            `✅ [POUCH-STORAGE] Bulk pre-upserted ${successCount}/${docsToCreate.length} order docs`
          );

          // Handle conflicts with retry
          if (conflicts.length > 0) {
            console.log(
              `🔄 [POUCH-STORAGE] Retrying ${conflicts.length} conflicted docs`
            );
            const retryDocs = [];
            for (const conflictId of conflicts) {
              try {
                const fresh = await localDB.get(conflictId);
                const originalDoc = docsToCreate.find(
                  (d) => d.docId === conflictId
                )?.baseDoc;
                if (originalDoc) {
                  retryDocs.push({
                    ...fresh,
                    ...originalDoc,
                    _rev: fresh._rev,
                    updated_at: new Date().toISOString(),
                  });
                }
              } catch (e) {
                console.warn(
                  `⚠️ [POUCH-STORAGE] Failed to retry conflict for ${conflictId}:`,
                  e
                );
              }
            }

            if (retryDocs.length > 0) {
              const retryResults = await localDB.bulkDocs(retryDocs);
              const retrySuccessCount = retryResults.filter((r) => r.ok).length;
              console.log(
                `✅ [POUCH-STORAGE] Conflict retry: ${retrySuccessCount}/${retryDocs.length} resolved`
              );
            }
          }
        } catch (e) {
          console.error("❌ [POUCH-STORAGE] Bulk pre-upsert failed:", e);
        }
      }
    } catch (e) {
      console.warn(
        "⚠️ [POUCH-STORAGE] Failed to pre-create inProgress order docs:",
        e
      );
    }

    // Create queryStatus callback to query PouchDB document status
    // Track not_found durations so we can auto-cancel after a timeout instead of failing immediately
    const __depNotFoundTracker = new Map(); // key: `${graphId}:${actionId}` -> { firstSeen: number, hits: number }
    const DEP_NOT_FOUND_CANCEL_MS = 60000; // 60s; consider wiring to shared config
    const queryStatus = async (graphId, actionId) => {
      try {
        // Query both order_ and monitor_ documents for the given actionId
        const orderDocId = `order_${graphId}_${actionId}`;
        const monitorDocId = `monitor_${graphId}_${actionId}`;
        const sanitizedGraphId = (graphId || "")
          .toString()
          .replace(/^exec_/, "");
        const monitorDocIdSan = `monitor_${sanitizedGraphId}_${actionId}`;

        // Try order document first
        try {
          const orderDoc = await localDB.get(orderDocId);
          console.log(
            `[POUCH-QUERY] Found order doc ${orderDocId} with status: ${orderDoc.status}`
          );
          console.log("[monitor fix][POUCH-QUERY] order doc found", {
            docId: orderDocId,
            status: orderDoc.status,
            actionId,
          });
          try {
            const key = `${graphId}:${actionId}`;
            __depNotFoundTracker.delete(key);
          } catch (_) {}
          return orderDoc.status;
        } catch (orderError) {
          // If order doc not found, try monitor document
          if (orderError.status === 404) {
            try {
              // Try unsanitized first
              const monitorDoc = await localDB.get(monitorDocId);
              console.log(
                `[POUCH-QUERY] Found monitor doc ${monitorDocId} with status: ${monitorDoc.status}`
              );
              console.log(
                "[monitor fix][POUCH-QUERY] monitor doc found (unsanitized)",
                {
                  docId: monitorDocId,
                  status: monitorDoc.status,
                  actionId,
                }
              );
              try {
                const key = `${graphId}:${actionId}`;
                __depNotFoundTracker.delete(key);
              } catch (_) {}
              return monitorDoc.status;
            } catch (monitorError) {
              if (monitorError.status === 404) {
                // Try sanitized monitor id as fallback
                try {
                  const monitorDoc2 = await localDB.get(monitorDocIdSan);
                  console.log(
                    `[POUCH-QUERY] Found monitor doc (san) ${monitorDocIdSan} with status: ${monitorDoc2.status}`
                  );
                  console.log(
                    "[monitor fix][POUCH-QUERY] monitor doc found (sanitized)",
                    {
                      docId: monitorDocIdSan,
                      status: monitorDoc2.status,
                      actionId,
                    }
                  );
                  try {
                    const key = `${graphId}:${actionId}`;
                    __depNotFoundTracker.delete(key);
                  } catch (_) {}
                  return monitorDoc2.status;
                } catch (monitorError2) {
                  if (monitorError2.status === 404) {
                    console.log(
                      `[POUCH-QUERY] Neither order nor monitor doc found for actionId: ${actionId}, returning 'not_found'`
                    );
                    // Neither order nor monitor doc exists yet: wait, unless we've waited too long
                    const key = `${graphId}:${actionId}`;
                    const now = Date.now();
                    let rec = __depNotFoundTracker.get(key);
                    if (!rec) rec = { firstSeen: now, hits: 0 };
                    rec.hits += 1;
                    __depNotFoundTracker.set(key, rec);

                    if (now - rec.firstSeen > DEP_NOT_FOUND_CANCEL_MS) {
                      console.log(
                        "[monitor fix][POUCH-QUERY] not_found timeout exceeded, returning 'cancelled'",
                        {
                          actionId,
                          waitedMs: now - rec.firstSeen,
                          hits: rec.hits,
                        }
                      );
                      return "cancelled"; // triggers cascade in dependency gate
                    }

                    console.log(
                      "[monitor fix][POUCH-QUERY] not_found (no docs yet) — dependency should wait",
                      {
                        actionId,
                        waitedMs: now - rec.firstSeen,
                        hits: rec.hits,
                      }
                    );
                    return "not_found";
                  }
                  throw monitorError2;
                }
              }
              throw monitorError;
            }
          }
          throw orderError;
        }
      } catch (error) {
        console.error(
          `[POUCH-QUERY] Failed to query status for actionId ${actionId}:`,
          error
        );
        return "error";
      }
    };

    const result = await engineController.executeActionArray(
      tempGraphId,
      normalizedActions,
      {
        siteId: request.siteId,
        tabId: request.tabId,
        reuseTab: true,
        closeAfterNode: true,
        stopOnError: executionDefaults.stop_on_error,
        failDependentsOnError: executionDefaults.fail_dependents_on_error,
        maxParallel: executionDefaults.max_concurrent_tabs,
      },

      async (graphId, actionId, status, info = {}) => {
        try {
          // Look up in expanded map; if not found, try to infer a synthetic action shell
          let action = actionsById.get(actionId);
          if (!action) {
            // Build a minimal shell using info if available to avoid dropping persistence
            action = {
              id: actionId,
              action: (info?.action || info?.data?.action || "").toString(),
              arguments: info?.data || {},
            };
          }
          if (!action || !localDB) return;

          const normalizedArgs = normalizeActionArguments(action);
          const nowIso = new Date().toISOString();
          const firebaseUid = request.firebase_uid;

          const isOrderAction = [
            "buy",
            "sell",
            "placebuylimitorder",
            "placeselllimitorder",
            "placebuystoplossmarketorder",
            "placesellstoplossmarketorder",
            "placebuystoplosslimitorder",
            "placesellstoplosslimitorder",
            "sellall",
            "exitallpositions",
          ].includes(normalizedArgs.action);

          if (isOrderAction) {
            const idStr = (actionId || "").toString();
            const hasDepends =
              Array.isArray(action?.depends_on) && action.depends_on.length > 0;
            const parentIdCandidate = hasDepends ? action.depends_on[0] : null;
            const parentAction = parentIdCandidate
              ? actionsById.get(parentIdCandidate)
              : null;
            const parentTypeUpper = (parentAction?.action || "")
              .toString()
              .toUpperCase();
            const parentIsMonitor = [
              "MONITOR",
              "MONITORPROFIT",
              "MONITORSYMBOLFROMWATCHLIST",
              "MONITORCONDITIONTHENACT",
            ].includes(parentTypeUpper);
            const isActFromMcta = hasDepends && parentIsMonitor;

            // For ACT from MonitorConditionThenAct: do not create order_* doc.
            // Do NOT promote monitor_* to order_executed here; login monitor will update it.
            if (isActFromMcta) {
              const contentResult = info?.resultDetail || info?.result || null;
              const monitorBaseId = action.depends_on[0];
              const sanitizedGraphId = (graphId || "")
                .toString()
                .replace(/^exec_/, "");
              const monitorDocId = `monitor_${sanitizedGraphId}_${monitorBaseId}`;

              try {
                const existing = await localDB
                  .get(monitorDocId)
                  .catch(() => null);
                const nowIso = new Date().toISOString();
                const orderTypeNorm = (() => {
                  const a = (normalizedArgs?.action || "")
                    .toString()
                    .toLowerCase();
                  if (a.includes("stoploss") && a.includes("limit"))
                    return "SL-L";
                  if (a.includes("stoploss") && a.includes("market"))
                    return "SL-M";
                  if (a.includes("limit")) return "LIMIT";
                  return "MARKET";
                })();
                const tradeTypeVerb = (() => {
                  const up = (action?.action || "").toString().toUpperCase();
                  if (up.includes("BUY")) return "BUY";
                  if (up.includes("SELL")) return "SELL";
                  return "SELL";
                })();
                const proposedStatus =
                  status === "completed" && contentResult?.success !== false
                    ? "order_executed"
                    : "stopped";
                const finalStatusForMonitor = (() => {
                  try {
                    const rank = {
                      inProgress: 0,
                      stopped: 1,
                      triggered: 2,
                      order_executed: 3,
                    };
                    const prev = (existing?.status || "").toString();
                    const next = (proposedStatus || "").toString();
                    const pr = rank[prev] ?? -1;
                    const nr = rank[next] ?? -1;
                    const final = nr >= pr ? next : prev;
                    try {
                      console.log("[monitor-debug] ACT result monitor update", {
                        monitorDocId,
                        nodeStatus: status,
                        actSuccess: contentResult?.success,
                        prevStatus: prev,
                        proposedStatus: next,
                        finalStatus: final,
                      });
                    } catch (_) {}
                    return final;
                  } catch (_) {
                    return proposedStatus;
                  }
                })();

                const updated = {
                  ...(existing || {
                    _id: monitorDocId,
                    type: "monitoring_alert",
                    firebase_uid: request.firebase_uid,
                    execution_request_id: graphId,
                    action_id: monitorBaseId,
                    id: monitorDocId,
                  }),
                  status: (function () {
                    try {
                      const prev = (existing?.status || "").toString();
                      const cs = (contentResult?.status || "")
                        .toString()
                        .toUpperCase();
                      const actFailed =
                        contentResult?.success === false || status === "failed";
                      // No promotion: mark as 'cancelled' on explicit cancel/reject or ACT failure; else keep previous status
                      const proposed =
                        cs.includes("UNKNOWN") ||
                        cs.includes("CANCEL") ||
                        cs.includes("REJECT") ||
                        actFailed
                          ? "cancelled"
                          : prev;
                      const rank = {
                        inProgress: 0,
                        stopped: 1,
                        cancelled: 2,
                        triggered: 2,
                        order_executed: 3,
                      };
                      const pr = rank[prev] ?? -1;
                      const nr = rank[proposed] ?? -1;
                      const final = nr >= pr ? proposed : prev;
                      try {
                        console.log(
                          "[monitor-debug] ACT->monitor status map (no-promo)",
                          {
                            monitorDocId,
                            contentStatus: cs,
                            prevStatus: prev,
                            proposedStatus: proposed,
                            finalStatus: final,
                          }
                        );
                      } catch (_) {}
                      return final;
                    } catch (_) {
                      return existing?.status || "triggered";
                    }
                  })(),
                  progress: "1/1",
                  progressPercent: 100,
                  broker: "zerodha",
                  broker_status:
                    contentResult?.status || existing?.broker_status || "",
                  broker_order_id:
                    cleanOrderId(contentResult?.orderId) ||
                    existing?.broker_order_id ||
                    "",
                  // Helpful execution summary (non-breaking additional fields)
                  orderExecution: {
                    action: action.action,
                    arguments: action.arguments || {},
                    tradeType: tradeTypeVerb,
                    product: normalizedArgs?.productType,
                    orderType: orderTypeNorm,
                    symbol: normalizedArgs?.symbol,
                    quantity: (normalizedArgs?.quantity || 0).toString(),
                    price: (normalizedArgs?.price || 0).toString(),
                    node_status: status,
                    node_result: contentResult,
                  },
                  updated_at: nowIso,
                };
                if (existing) {
                  await localDB.put({ ...updated, _rev: existing._rev });
                } else {
                  await localDB.put(updated);
                }
                console.log(
                  `[POUCH-STORAGE] Monitor updated from ACT result:`,
                  monitorDocId
                );
              } catch (updErr) {
                console.warn(
                  "[POUCH-STORAGE] Failed updating monitor from ACT result",
                  updErr
                );
              }
              return; // Skip order_* doc creation for ACT
            }

            const docId = `order_${graphId}_${actionId}`;
            const contentResult = info?.resultDetail || info?.result || null;
            try {
              console.log("[POUCH-STORAGE] Node callback result:", {
                actionId,
                nodeStatus: status,
                contentStatus: contentResult?.status,
                orderId: cleanOrderId(contentResult?.orderId),
              });
            } catch (_) {}
            const immediateMappedStatus = (() => {
              try {
                const cs = (contentResult?.status || "")
                  .toString()
                  .toUpperCase();
                const ns = (status || "").toString().toLowerCase();
                const succ =
                  typeof contentResult?.success === "boolean"
                    ? contentResult.success
                    : undefined;
                const actionUpper = (action?.action || "")
                  .toString()
                  .toUpperCase();
                const isBulkExit =
                  actionUpper === "SELLALL" ||
                  actionUpper === "EXITALLPOSITIONS";
                if (
                  cs.includes("CANCEL") ||
                  cs.includes("REJECT") ||
                  cs.includes("UNKNOWN")
                )
                  return "cancelled";
                if (ns === "cancelled" || ns === "failed" || ns === "error")
                  return "cancelled";
                if (succ === false) return "cancelled";
                // If node completed successfully (or broker status indicates execution), mark as executed
                if (
                  ns === "completed" &&
                  succ === true &&
                  (cs.includes("EXECUT") || cs.includes("COMPLETE"))
                )
                  return "executed";
                // Fallback for bulk-exit primitives (SellAll / ExitAllPositions):
                // When broker_status is absent/unknown but node completed successfully, consider executed
                if (isBulkExit) {
                  const hasExplicitBrokerStatus = cs.length > 0;
                  const isExplicitCancel =
                    cs.includes("CANCEL") || cs.includes("REJECT");
                  if (
                    !isExplicitCancel &&
                    (succ === true || ns === "completed")
                  ) {
                    // If we lack a clear broker execution status but the node succeeded, mark executed
                    if (
                      !hasExplicitBrokerStatus ||
                      !(cs.includes("EXECUT") || cs.includes("COMPLETE"))
                    ) {
                      return "executed";
                    }
                  }
                }
              } catch (_) {}
              return "inProgress";
            })();

            // Derive trade type robustly from the original action label first, then fallback
            const originalActionUpper = (action?.action ?? "")
              .toString()
              .toUpperCase();
            const normalizedActionLower = (normalizedArgs?.action ?? "")
              .toString()
              .toLowerCase();
            const tradeTypeVerb = (() => {
              if (originalActionUpper.includes("BUY")) return "BUY";
              if (originalActionUpper.includes("SELL")) return "SELL";
              if (normalizedActionLower.includes("buy")) return "BUY";
              if (normalizedActionLower.includes("sell")) return "SELL";
              return "SELL"; // conservative default
            })();

            // Normalize order type from normalized action (case-insensitive)
            const orderTypeNorm = (() => {
              const a = normalizedActionLower;
              if (a.includes("stoploss") && a.includes("limit")) return "SL-L";
              if (a.includes("stoploss") && a.includes("market")) return "SL-M";
              if (a.includes("limit")) return "LIMIT";
              return "MARKET";
            })();

            const baseDoc = {
              _id: docId,
              type: "order_result",
              firebase_uid: firebaseUid,

              // Tracking
              execution_request_id: graphId,
              action_id: actionId,

              // Frontend-compatible
              id: docId,
              symbol: normalizedArgs.symbol,
              tradeType: tradeTypeVerb,
              quantity: (normalizedArgs.quantity || 0).toString(),
              price: (normalizedArgs.price || 0).toString(),
              // Immediate cancellation on the spot if detected; otherwise keep inProgress
              status: immediateMappedStatus,

              timestamp: nowIso,
              broker: "zerodha",
              broker_status: contentResult?.status || "",
              product: normalizedArgs.productType,
              orderType: orderTypeNorm,

              // Additional placeholders (will be enriched later if needed)
              broker_order_id: cleanOrderId(contentResult?.orderId) || "",

              execution_details: {
                node_status: status,
                node_result: contentResult,
              },
              created_at: nowIso,
              updated_at: nowIso,
            };

            try {
              // Immediate flush for cancelled orders to propagate cascades quickly
              const isImmediate =
                (baseDoc?.status || "").toString().toLowerCase() ===
                "cancelled";
              enqueueOrderUpsert(docId, baseDoc, isImmediate);
              console.log(
                `✅ [POUCH-STORAGE] Order enqueued for bulk upsert (${status}):`,
                docId
              );

              // If this node was immediately determined as cancelled, trigger a cascade to dependents
              try {
                if (immediateMappedStatus === "cancelled") {
                  console.log(
                    "[Cancel Order P][POUCHDB_BULK_CANCEL] Immediate cancel detected in node callback; triggering cascade",
                    { graphId, parentId: actionId }
                  );
                  await triggerCascadeForParent(graphId, actionId);
                }
              } catch (cascadeErr) {
                console.warn(
                  "[Cancel Order P][POUCHDB_BULK_CANCEL] Cascade trigger failed from node callback:",
                  cascadeErr?.message || cascadeErr
                );
              }

              // Debug: Log broker_order_id population for debugging
              try {
                console.log(
                  `[POUCH-STORAGE] DEBUG - Order broker_order_id populated:`,
                  {
                    docId,
                    broker_order_id: baseDoc.broker_order_id,
                    from_contentResult_orderId: contentResult?.orderId,
                    cleaned: cleanOrderId(contentResult?.orderId),
                    status: status,
                  }
                );
              } catch (_) {}
            } catch (e) {
              if (e.status === 409) {
                const fresh = await localDB.get(docId);
                const conflictResolvedDoc = {
                  ...fresh,
                  ...baseDoc,
                  _rev: fresh._rev,
                  updated_at: nowIso,
                };
                await localDB.put(conflictResolvedDoc);
                console.log(
                  `✅ [POUCH-STORAGE] Order conflict resolved:`,
                  docId
                );

                // If this node was immediately determined as cancelled, trigger a cascade to dependents (post-conflict)
                try {
                  if (immediateMappedStatus === "cancelled") {
                    console.log(
                      "[Cancel Order P][POUCHDB_BULK_CANCEL] Immediate cancel detected in node callback (post-conflict); triggering cascade",
                      { graphId, parentId: actionId }
                    );
                    await triggerCascadeForParent(graphId, actionId);
                  }
                } catch (cascadeErr) {
                  console.warn(
                    "[Cancel Order P][POUCHDB_BULK_CANCEL] Cascade trigger failed from node callback (post-conflict):",
                    cascadeErr?.message || cascadeErr
                  );
                }

                // Debug: Log broker_order_id after conflict resolution
                try {
                  console.log(
                    `[POUCH-STORAGE] DEBUG - Order broker_order_id after conflict:`,
                    {
                      docId,
                      broker_order_id: conflictResolvedDoc.broker_order_id,
                      from_contentResult_orderId: contentResult?.orderId,
                      cleaned: cleanOrderId(contentResult?.orderId),
                      status: status,
                    }
                  );
                } catch (_) {}
              } else {
                console.error(
                  "❌ [POUCH-STORAGE] Order upsert enqueue failed:",
                  e
                );
              }
            }
            return;
          }
          console.log("normalizedArgs to debug=>", normalizedArgs);

          // Removed legacy MonitorConditionThenAct persistence.

          if ((normalizedArgs.action || "").toUpperCase() === "MONITOR") {
            const contentResult = info?.resultDetail || info?.result || null;
            const monitorMappedStatus = (() => {
              if (status === "running") return "inProgress";
              if (status === "completed") {
                return contentResult?.success ? "triggered" : "stopped";
              }
              return "stopped";
            })();
            const condition =
              action.condition || action.arguments?.condition || {};

            // Attempt to enrich monitor with associated ACT details (dependent node)
            const actNode = (normalizedActions || []).find(
              (a) =>
                Array.isArray(a?.depends_on) && a.depends_on.includes(actionId)
            );
            const actArgs = actNode?.arguments || {};
            const getCaseInsensitive = (obj, keyUpperList) => {
              try {
                const entry = Object.keys(obj || {}).find((k) =>
                  keyUpperList.includes(k.toUpperCase())
                );
                return entry ? obj[entry] : undefined;
              } catch (_) {
                return undefined;
              }
            };
            const actProduct =
              getCaseInsensitive(actArgs, ["PRODUCT_TYPE", "PRODUCTTYPE"]) ||
              "MIS";
            const actOrderType = actNode?.action || "MONITOR";

            // Create monitoring id with monitor_ prefix and without embedded exec_
            const sanitizedGraphId = (graphId || "")
              .toString()
              .replace(/^exec_/, "");
            const docId = `monitor_${sanitizedGraphId}_${actionId}`;
            const baseDoc = {
              _id: docId,
              type: "monitoring_alert",
              firebase_uid: firebaseUid,

              // Tracking
              execution_request_id: graphId,
              action_id: actionId,

              // Frontend-compatible
              id: docId,
              description: `Monitor ${condition.symbol || "UNKNOWN"} ${
                condition.operator || "condition"
              } ${condition.value || ""}`,

              symbol: condition.symbol || "UNKNOWN",
              triggerPrice: (condition.value || 0).toString(),

              currentPrice: (contentResult?.conditionValue ?? 0).toString(),
              progress:
                status === "completed" && contentResult?.success
                  ? "1/1"
                  : "0/1",
              progressPercent:
                status === "completed" && contentResult?.success ? 100 : 0,
              status: monitorMappedStatus,

              // Include target ACT information when available
              orderType: actOrderType,
              stopLoss: "",
              product: actProduct,
              onTrigger: actNode
                ? {
                    action: actNode.action,
                    arguments: actArgs,
                  }
                : undefined,

              // Original data
              condition,

              created_at: nowIso,
              updated_at: nowIso,
            };

            try {
              console.log("[monitor-debug] Monitor upsert base", {
                docId,
                nodeStatus: status,
                contentSuccess: contentResult?.success,
                conditionValue: contentResult?.conditionValue,
                mappedStatus: monitorMappedStatus,
              });
            } catch (_) {}

            try {
              // Ensure monitor status is monotonic (no downgrade)
              const chooseMonotonicStatus = (prev, next) => {
                try {
                  const rank = {
                    inProgress: 0,
                    stopped: 1,
                    triggered: 2,
                    order_executed: 3,
                  };
                  const p = (prev || "").toString();
                  const n = (next || "").toString();
                  const pr = rank[p] ?? -1;
                  const nr = rank[n] ?? -1;
                  return nr >= pr ? n : p;
                } catch (_) {
                  return next || prev;
                }
              };

              const existing = await localDB.get(docId).catch(() => null);
              let toPut;
              if (existing) {
                // Preserve higher status and progress
                const monotonicStatus = chooseMonotonicStatus(
                  existing.status,
                  baseDoc.status
                );
                const newProgressPercent = Math.max(
                  Number(existing.progressPercent || 0),
                  Number(baseDoc.progressPercent || 0)
                );
                const pickExisting =
                  Number(existing.progressPercent || 0) >=
                  Number(baseDoc.progressPercent || 0);
                const monotonicProgress = pickExisting
                  ? existing.progress
                  : baseDoc.progress;

                try {
                  console.log("[monitor-debug] Monitor upsert existing", {
                    docId,
                    prevStatus: existing.status,
                    requested: baseDoc.status,
                    finalStatus: monotonicStatus,
                    prevProgressPercent: existing.progressPercent,
                    newProgressPercent,
                  });
                } catch (_) {}

                toPut = {
                  ...existing,
                  ...baseDoc,
                  status: monotonicStatus,
                  progressPercent: newProgressPercent,
                  progress: monotonicProgress,
                  _rev: existing._rev,
                  updated_at: nowIso,
                };
              } else {
                try {
                  console.log("[monitor-debug] Monitor upsert new", {
                    docId,
                    finalStatus: baseDoc.status,
                  });
                } catch (_) {}
                toPut = baseDoc;
              }
              await localDB.put(toPut);
              console.log(
                `✅ [POUCH-STORAGE] Monitoring upserted (${status}):`,
                docId
              );
            } catch (e) {
              if (e.status === 409) {
                const fresh = await localDB.get(docId);
                // Apply monotonic status/progress on conflict resolution as well
                const monotonicStatus = (function () {
                  try {
                    const rank = {
                      inProgress: 0,
                      stopped: 1,
                      triggered: 2,
                      order_executed: 3,
                    };
                    const p = (fresh.status || "").toString();
                    const n = (baseDoc.status || "").toString();
                    const pr = rank[p] ?? -1;
                    const nr = rank[n] ?? -1;
                    return nr >= pr ? n : p;
                  } catch (_) {
                    return baseDoc.status || fresh.status;
                  }
                })();
                const newProgressPercent = Math.max(
                  Number(fresh.progressPercent || 0),
                  Number(baseDoc.progressPercent || 0)
                );
                const pickFresh =
                  Number(fresh.progressPercent || 0) >=
                  Number(baseDoc.progressPercent || 0);
                const monotonicProgress = pickFresh
                  ? fresh.progress
                  : baseDoc.progress;

                await localDB.put({
                  ...fresh,
                  ...baseDoc,
                  status: monotonicStatus,
                  progressPercent: newProgressPercent,
                  progress: monotonicProgress,
                  _rev: fresh._rev,
                  updated_at: nowIso,
                });
                console.log(
                  `✅ [POUCH-STORAGE] Monitoring conflict resolved:`,
                  docId
                );
              } else {
                console.error(
                  "❌ [POUCH-STORAGE] Monitoring upsert failed:",
                  e
                );
              }
            }

            // If monitor executed an ACT (from MonitorConditionThenAct), we do NOT create a separate order_ doc anymore.
            // The ACT node callback will update the corresponding monitor_* doc to 'order_executed'.
          }
        } catch (err) {
          console.error("❌ [POUCH-STORAGE] Callback processing failed:", err);
        }
      },
      queryStatus
    );

    return result;
  } catch (error) {
    console.error("Error in current tab test:", error);
    return {
      success: false,
      message: `Test failed: ${error?.message || error}`,
      error: error.toString(),
    };
  }
}

/**
 * Handle simple action execution with per-task tab management
 */
async function handleSimpleActionExecution(request) {
  try {
    const { actions } = request;

    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    console.log(
      "🎯 Executing action array directly:",
      actions.length,
      "actions"
    );

    // Create a temporary graph ID for execution
    // TODO: Remove this once we have a proper graph ID and add uuid to the graph ID
    const tempGraphId = `temp_${Date.now()}_uuid`;

    // Execute the action array using the rule engine
    const result = await engineController.executeActionArray(
      tempGraphId,
      actions,
      { closeAfterNode: true }
    );

    return result;
  } catch (error) {
    console.error("Error in traditional execution:", error);
    return { success: false, message: `Error: ${error?.message || error}` };
  }
}

/**
 * Handle rule engine requests
 */
async function handleRuleEngineRequest(request, sendResponse) {
  try {
    if (!engineController) {
      await initializeBackground();
    }

    switch (request.type) {
      case "RULE_ENGINE_ADD_GRAPH":
        engineController.addGraph(request.graphId, request.actions);
        sendResponse({
          success: true,
          message: "Action graph added to rule engine",
        });
        break;

      // REMOVED: RULE_ENGINE_EXECUTE_ACTIONS now handled via PouchDB only

      case "RULE_ENGINE_TRIGGER_GRAPH":
        const triggerResult = await engineController.triggerGraph(
          request.graphId,
          request.startActionId
        );
        sendResponse(triggerResult);
        break;

      case "RULE_ENGINE_TRIGGER_GRAPH_WITH_PARENT_TRACKING":
        const triggerWithParentResult =
          await engineController.triggerGraphWithParentTracking(
            request.graphId,
            request.startActionId,
            request.options || {}
          );
        sendResponse(triggerWithParentResult);
        break;

      case "RULE_ENGINE_GET_PARENT_STATUS":
        const parentStatusResult =
          await engineController.getParentStatusBeforeSubtree(
            request.graphId,
            request.parentActionId
          );
        sendResponse({ success: true, parentStatus: parentStatusResult });
        break;

      case "RULE_ENGINE_GET_NODE_STATUS":
        const nodeStatus = engineController.getNodeStatus(
          request.graphId,
          request.actionId
        );
        sendResponse({ success: true, nodeStatus });
        break;

      case "RULE_ENGINE_GET_NODE_STATUSES":
        const nodeStatuses = engineController.getNodeStatuses(request.graphId);
        sendResponse({ success: true, nodeStatuses });
        break;

      case "RULE_ENGINE_GET_STATUS":
        const status = engineController.getStatus();
        sendResponse({ success: true, status });
        break;

      case "RULE_ENGINE_GET_CONFIG":
        const config = await engineController.getConfiguration();
        sendResponse({ success: true, config });
        break;

      default:
        sendResponse({
          success: false,
          message: `Unknown DAG engine request type: ${request.type}`,
        });
    }
  } catch (error) {
    console.error("Error handling DAG engine request:", error);
    sendResponse({
      success: false,
      message: `Error: ${error?.message || error}`,
    });
  }
}

/**
 * Handle test current tab request
 */
async function handleTestCurrentTab(request) {
  try {
    const { tabId, action, arguments: actionArgs } = request;

    if (!tabId || !action || !actionArgs) {
      return {
        success: false,
        message: "Missing required parameters (tabId, action, arguments)",
      };
    }

    console.log("🧪 Testing current tab execution:", {
      tabId,
      action,
      actionArgs,
    });

    // Initialize engine controller if not already done
    if (!engineController) {
      await initializeBackground();
    }

    // Test 1: Check if execution service can be loaded
    console.log("🧪 Loading execution service...");
    let executionService;
    try {
      if (typeof globalThis.ExecutionService !== "undefined") {
        executionService = new globalThis.ExecutionService();
      } else if (typeof ExecutionService !== "undefined") {
        executionService = new ExecutionService();
      } else {
        console.warn("⚠️ ExecutionService not available in bundle");
        return {
          success: false,
          message:
            "ExecutionService not available in service worker environment",
        };
      }
      console.log("✅ Execution service loaded successfully");
    } catch (error) {
      console.error("❌ ExecutionService loading failed:", error);
      return {
        success: false,
        message: `ExecutionService loading failed: ${error.message}`,
      };
    }

    // Test 2: Check if action is supported (simplified)
    console.log("🧪 Checking if action is supported...");
    const supportedActions = [
      "BUY",
      "SELL",
      "PlaceBuyLimitOrder",
      "PlaceSellLimitOrder",
      "PlaceBuyStopLossMarketOrder",
      "PlaceSellStopLossMarketOrder",
      "PlaceBuyStopLossLimitOrder",
      "PlaceSellStopLossLimitOrder",
      "MONITORPROFIT",
      "ExitAllPositions",
      "SellAll",
      "MonitorConditionThenAct",
      "Monitor",
      "NavigateToProfile",
      "GetProfileInfo",
    ];
    console.log("Supported actions:", supportedActions);

    if (!supportedActions.includes(action)) {
      return {
        success: false,
        message: `Action '${action}' is not supported. Supported actions: ${supportedActions.join(
          ", "
        )}`,
      };
    }

    // Test 3: Simple test - just try to inject content script and send a message
    console.log("🧪 Testing content script injection and communication...");

    try {
      // Try to inject content script
      await chrome.scripting.executeScript({
        target: { tabId },
        files: ["content-scripts/zerodha.js"],
      });
      console.log("✅ Content script injected successfully");

      // Wait a bit for the content script to initialize
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Try to send a simple test message
      const testMessage = {
        type: "PERFORM_SITE_ACTIONS",
        actions: [
          {
            action: "GetProfileInfo",
            arguments: {},
          },
        ],
      };

      console.log("📤 Sending test message to content script...");
      const response = await new Promise((resolve, reject) => {
        // Add timeout to prevent hanging
        const timeout = setTimeout(() => {
          reject(
            new Error(
              "Test message timeout: No response received within 10 seconds"
            )
          );
        }, 10000);

        chrome.tabs.sendMessage(tabId, testMessage, (response) => {
          clearTimeout(timeout);

          if (chrome.runtime.lastError) {
            const errorMessage = chrome.runtime.lastError.message;
            console.error(
              `[Background] Chrome runtime error for tab ${tabId}:`,
              errorMessage
            );

            // Handle specific message channel closed error
            if (
              errorMessage.includes("message channel closed") ||
              errorMessage.includes("asynchronous response") ||
              errorMessage.includes("Could not establish connection")
            ) {
              reject(
                new Error(
                  `Message channel closed: ${errorMessage}. This usually happens when the tab was closed or navigated away during execution.`
                )
              );
            } else {
              reject(new Error(`Failed to send message: ${errorMessage}`));
            }
          } else if (!response) {
            reject(new Error("No response from content script"));
          } else {
            resolve(response);
          }
        });
      });

      console.log("📥 Received response from content script:", response);

      return {
        success: true,
        message: "Content script communication test successful",
        details: response,
        supportedActions: supportedActions,
      };
    } catch (injectionError) {
      console.error(
        "❌ Content script injection/communication failed:",
        injectionError
      );
      return {
        success: false,
        message: `Content script test failed: ${injectionError.message}`,
        error: injectionError.toString(),
        supportedActions: supportedActions,
      };
    }
  } catch (error) {
    console.error("Error in current tab test:", error);
    return {
      success: false,
      message: `Test failed: ${error?.message || error}`,
      error: error.toString(),
    };
  }
}

/**
 * Handle simple action execution with per-task tab management
 */
async function handleSimpleActionExecution(request) {
  try {
    const { actions } = request;

    if (!actions || !Array.isArray(actions)) {
      return { success: false, message: "Invalid actions array" };
    }

    console.log(
      "🎯 Executing action array directly:",
      actions.length,
      "actions"
    );

    // Create a temporary graph ID for execution
    // TODO: Remove this once we have a proper graph ID and add uuid to the graph ID
    const tempGraphId = `temp_${Date.now()}_uuid`;

    // Execute the action array using the rule engine
    const result = await engineController.executeActionArray(
      tempGraphId,
      actions,
      { closeAfterNode: true }
    );

    return result;
  } catch (error) {
    console.error("Error in simple action execution:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle environment configuration request
 */
async function handleEnvironmentConfigRequest() {
  try {
    let environment;
    if (typeof globalThis.getEnvironment !== "undefined") {
      environment = await globalThis.getEnvironment();
    } else if (typeof getEnvironment !== "undefined") {
      environment = await getEnvironment();
    } else {
      console.warn("⚠️ [DEBUG] getEnvironment not available, using fallback");
      environment = { close_tabs_after_execution: true }; // Fallback config
    }
    return {
      success: true,
      config: environment,
    };
  } catch (error) {
    console.error("Error getting environment config:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle toggle tab closing request
 */
async function handleToggleTabClosing() {
  try {
    let environment;
    if (typeof globalThis.getEnvironment !== "undefined") {
      environment = await globalThis.getEnvironment();
    } else if (typeof getEnvironment !== "undefined") {
      environment = await getEnvironment();
    } else {
      console.warn("⚠️ [DEBUG] getEnvironment not available, using fallback");
      environment = { close_tabs_after_execution: true }; // Fallback config
    }

    const currentSetting = environment.close_tabs_after_execution;
    const newSetting = !currentSetting;

    // Update the shared config
    const response = await fetch(
      chrome.runtime.getURL("lib/shared-config.json")
    );
    const config = await response.json();
    config.ENVIRONMENT.close_tabs_after_execution = newSetting;

    // Note: In a real implementation, you might want to save this to storage
    // For now, we'll just return the new setting
    console.log(
      `🔄 Tab closing setting toggled from ${currentSetting} to ${newSetting}`
    );

    return {
      success: true,
      close_tabs_after_execution: newSetting,
      message: `Tab closing setting toggled to ${newSetting}`,
    };
  } catch (error) {
    console.error("Error toggling tab closing:", error);
    return {
      success: false,
      message: `Error: ${error?.message || error}`,
    };
  }
}

/**
 * Handle messages from popup and content scripts
 */
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  try {
    console.log("[Cancel Order P] onMessage", { type: request?.type });
  } catch (_) {}
  console.log(" == Received message ===>", request.type);
  switch (request.type) {
    case "ZERODHA_ORDERS_API_RESULT": {
      try {
        const payload = request?.payload || {};
        if (
          payload &&
          payload.status === "success" &&
          Array.isArray(payload.data)
        ) {
          try {
            const count = payload.data.length;
            const sample = payload.data.slice(0, 3).map((o) => ({
              order_id: o?.order_id,
              status: o?.status,
              symbol: o?.tradingsymbol,
            }));
            console.log("[Background] ZERODHA_ORDERS_API_RESULT received", {
              httpOk: request?.ok,
              httpStatus: request?.status,
              apiStatus: payload?.status,
              count,
              sample,
            });
          } catch (_) {}
          const raw = payload.data;
          const completed = [];
          const open = [];
          for (const o of raw) {
            const row = {
              time: o.exchange_timestamp || o.order_timestamp || "",
              type: o.transaction_type || "",
              symbol: o.tradingsymbol || "",
              exchange: o.exchange || "",
              product: o.product || "",
              quantity: {
                filled: Number(o.filled_quantity || 0),
                total: Number(o.quantity || 0),
                remaining: Math.max(
                  0,
                  Number(o.quantity || 0) - Number(o.filled_quantity || 0)
                ),
              },
              avgPrice: Number(o.average_price || 0),
              status: o.status || "",
              detailedInfo: {
                orderId: String(o.order_id || ""),
                exchangeOrderId: o.exchange_order_id || undefined,
                orderTimestamp: o.order_timestamp || undefined,
                exchangeTimestamp: o.exchange_timestamp || undefined,
                transactionType: o.transaction_type || undefined,
                tradingSymbol: o.tradingsymbol || undefined,
                orderStatus: o.status || undefined,
                price: o.price || undefined,
                triggerPrice: o.trigger_price || undefined,
                orderType: o.order_type || undefined,
                product: o.product || undefined,
                validity: o.validity || undefined,
              },
            };

            const s = (o.status || "").toUpperCase();
            if (
              s.includes("COMPLETE") ||
              s.includes("CANCEL") ||
              s.includes("REJECT")
            ) {
              completed.push(row);
            } else {
              open.push(row);
            }
          }

          if (typeof globalThis.__upsertOrdersFromMonitor === "function") {
            globalThis
              .__upsertOrdersFromMonitor(open, completed)
              .catch(() => {});
          }
        }
      } catch (e) {
        console.warn(
          "[Background] Failed handling ZERODHA_ORDERS_API_RESULT:",
          e
        );
      }
      // Optional response to keep channel clean
      sendResponse?.({ success: true });
      return true;
    }
    case "GET_LOGIN_STATUS": {
      sendResponse({
        success: true,
        data: {
          required: cachedLoginStatus.required,
          brokerName: cachedLoginStatus.brokerName,
          updatedAt: cachedLoginStatus.updatedAt,
        },
      });
      break;
    }
    case "BROKER_STATUS_CHECK": {
      // Optional: request.brokerName (future multi-broker support)
      const brokerName =
        request?.brokerName || cachedLoginStatus.brokerName || "zerodha";
      const force = request?.force === true;

      // Helper to respond and persist
      const respond = (required) => {
        cachedLoginStatus = {
          required,
          brokerName,
          updatedAt: Date.now(),
        };
        persistCachedLoginStatus();
        sendResponse({
          success: true,
          data: cachedLoginStatus,
        });
      };

      // If we have a fresh cache (e.g., last 15s), return immediately
      const now = Date.now();
      const isFresh =
        !force &&
        typeof cachedLoginStatus.updatedAt === "number" &&
        now - cachedLoginStatus.updatedAt < 15_000 &&
        cachedLoginStatus.required !== null;
      if (isFresh) {
        respond(cachedLoginStatus.required);
        break;
      }

      // One-shot check using execution service without enabling continuous monitor
      (async () => {
        try {
          if (!engineController) {
            console.warn(
              "[Background] engineController not initialized. Initializing..."
            );
            await initializeBackground();
          }
          if (!engineController?.executionService) {
            throw new Error("ExecutionService unavailable");
          }

          // Ensure a monitor tab exists briefly, perform a single check, avoid starting intervals
          await engineController.executionService.ensureLoginMonitorTab();
          // Reuse existing check logic but do it once
          const prevStarted =
            engineController.executionService.loginMonitor?.started;
          try {
            await engineController.executionService.checkLoginAndTogglePause();
          } catch (e) {
            console.warn("[Background] checkLoginAndTogglePause failed:", e);
          }
          const required =
            !!engineController.executionService.loginMonitor?.required;
          // Do not start long-running monitor here; if it was not started, keep it stopped
          if (
            !prevStarted &&
            engineController.executionService.loginMonitor?.started
          ) {
            try {
              engineController.executionService.stopLoginMonitor(false);
            } catch (_) {}
          }
          respond(required);
        } catch (e) {
          console.warn(
            "[Background] BROKER_STATUS_CHECK failed, returning cached state:",
            e
          );
          respond(cachedLoginStatus.required ?? true); // default to requiring login when unsure
        }
      })();
      return true; // keep channel open for async response
    }

    case "EXTENSION_OTP_VERIFIED":
    case "CONNECT_TO_COUCHDB":
      if (request.success) {
        console.log(
          `User has logged in successfully. Storing firebaseUid "${request.firebaseUid}" in cache...`
        );
        console.log(`Couchdb details`, request.couchdb_details);
        (async () => {
          try {
            await storeDataInCache("firebase_uid", request.firebaseUid);
            await storeDataInCache("couchdb_details", request.couchdb_details);
            initializeBackground();
            console.log("Started monitoring.");
          } catch (err) {
            console.error(err);
          }
        })();
      } else {
        console.warn("Login was not successful");
      }
      return true;

    case "WAKE_UP_SERVICE_WORKER":
      console.log("⏰ [WAKE] Service worker wake-up requested");
      // Re-initialize PouchDB connection if needed
      if (!localDB) {
        console.log("🔧 [WAKE] PouchDB not initialized, re-initializing...");
        initializeBackground();
      } else {
        console.log("✅ [WAKE] PouchDB already initialized");
        console.log(
          "🔄 [WAKE] Restarting execution monitoring to ensure change listener is active..."
        );
        startExecutionMonitoring();
      }
      sendResponse({ success: true, message: "Service worker is awake" });
      return true;

    case "EXECUTE_ACTIONS":
    case "RULE_ENGINE_EXECUTE_ACTIONS":
      // Handle node-by-node execution requests
      handleExecutionRequest(request).then(sendResponse);
      return true; // Keep message channel open for async response

    case "GET_ENGINE_STATUS":
      // Get engine status for testing
      if (engineController) {
        sendResponse(engineController.getStatus());
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized",
        });
      }
      break;

    case "GET_EXECUTION_STATUS":
      // Get execution service status for testing
      if (engineController) {
        sendResponse(engineController.getAllExecutionsStatus());
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized",
        });
      }
      break;

    case "PAUSE_GRAPH":
      // Pause a graph execution
      if (engineController && request.graphId) {
        const result = engineController.pauseGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "RESUME_GRAPH":
      // Resume a graph execution
      if (engineController && request.graphId) {
        const result = engineController.resumeGraphExecution(request.graphId);
        sendResponse(result);
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "CANCEL_GRAPH":
      // Cancel a graph execution
      if (engineController && request.graphId) {
        engineController
          .cancelGraphExecution(request.graphId)
          .then(sendResponse);
        return true; // Keep message channel open for async response
      } else {
        sendResponse({
          success: false,
          message: "Engine controller not initialized or graph ID missing",
        });
      }
      break;

    case "TEST_CURRENT_TAB":
      // Handle async operations properly with error handling
      (async () => {
        try {
          const result = await handleTestCurrentTab(request);
          sendResponse(result);
        } catch (error) {
          console.error("Error in TEST_CURRENT_TAB:", error);
          sendResponse({
            success: false,
            message: `Test failed: ${error?.message || error}`,
            error: error.toString(),
          });
        }
      })();
      return true; // Keep message channel open for async response

    case "GET_ENVIRONMENT_CONFIG":
      // Handle environment configuration request
      handleEnvironmentConfigRequest().then(sendResponse);
      return true; // Keep message channel open for async response

    case "TOGGLE_TAB_CLOSING":
      // Handle toggle tab closing request
      handleToggleTabClosing().then(sendResponse);
      return true; // Keep message channel open for async response

    case "POUCHDB_BULK_CANCEL":
      // Handle bulk cancellation of nodes and document updates
      handleBulkCancelRequest(request).then(sendResponse);
      return true; // Keep message channel open for async response

    case "GET_POUCHDB_INSTANCE":
      // Allow content scripts to access PouchDB operations
      if (localDB) {
        sendResponse({ success: true, db: "available" });
      } else {
        sendResponse({ success: false, error: "PouchDB not initialized" });
      }
      return false; // No async response needed

    case "UPDATE_DOCUMENT_STATUS":
      // Handle document status updates from content scripts
      if (!localDB) {
        sendResponse({ success: false, error: "PouchDB not initialized" });
        return false;
      }

      const { docId, newStatus, metadata = {}, options = {} } = request;
      handleDocumentStatusUpdate(docId, newStatus, metadata, options).then(
        sendResponse
      );
      return true; // Keep message channel open for async response

    default:
      sendResponse({
        success: false,
        message: `Unknown message type: ${request.type}`,
      });
  }
});

/**
 * Handle tab updates
 */
let loginStatusCheckDebounceId = null;
async function oneShotLoginCheckAndBroadcast(
  brokerName = "zerodha",
  reason = "tabs_update"
) {
  try {
    if (!engineController) {
      await initializeBackground();
    }
    if (!engineController?.executionService) return;

    await engineController.executionService.ensureLoginMonitorTab();
    const prevStarted = engineController.executionService.loginMonitor?.started;
    try {
      await engineController.executionService.checkLoginAndTogglePause();
    } catch (e) {
      console.warn("[Background] oneShotLoginCheck failed:", e);
    }
    const required = !!engineController.executionService.loginMonitor?.required;

    const prev = cachedLoginStatus.required;
    cachedLoginStatus = { required, brokerName, updatedAt: Date.now() };
    persistCachedLoginStatus();

    // Broadcast status change to all connected clients
    if (required !== prev) {
      try {
        // Broadcast to all connected clients via runtime message
        chrome.runtime.sendMessage({
          type: required ? "LOGIN_REQUIRED" : "LOGIN_RESOLVED",
          brokerName,
          targetTab: "chat",
          reason,
        });

        console.log(
          `[Background] Broadcasted ${
            required ? "LOGIN_REQUIRED" : "LOGIN_RESOLVED"
          } for ${brokerName}`
        );
      } catch (broadcastError) {
        console.warn(
          "[Background] Failed to broadcast login status change:",
          broadcastError
        );
      }
    }

    if (
      !prevStarted &&
      engineController.executionService.loginMonitor?.started
    ) {
      try {
        engineController.executionService.stopLoginMonitor(false);
      } catch (_) {}
    }
  } catch (e) {
    console.warn("[Background] oneShotLoginCheckAndBroadcast error:", e);
  }
}

chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  try {
    if (
      changeInfo.status === "complete" &&
      tab?.url &&
      tab.url.startsWith("https://kite.zerodha.com/")
    ) {
      if (loginStatusCheckDebounceId) {
        try {
          clearTimeout(loginStatusCheckDebounceId);
        } catch (_) {}
      }
      loginStatusCheckDebounceId = setTimeout(() => {
        oneShotLoginCheckAndBroadcast("zerodha", "tabs_onUpdated");
      }, 800);
    }
  } catch (_) {}
});

/**
 * Initialize background script when extension loads
 */
chrome.runtime.onInstalled.addListener(async (details) => {
  console.log("🔧 Extension installed event:", details?.reason);
  if (details?.reason === "update") {
    await closeExistingMonitorTabFromStorage();
    await closeExistingObserverMonitorTabFromStorage();
  }
  // On both install and update, aggressively clean leftover monitor tabs/groups
  await closePinnedOrdersTabs();
  await closeSmartAgentMonitorGroupTabs();
  initializeBackground();
});

chrome.runtime.onStartup.addListener(() => {
  console.log("🚀 Extension started, initializing background script...");
  // Best-effort cleanup for any leftover grouped monitor tabs
  closeSmartAgentMonitorGroupTabs();
  initializeBackground();
});

// Note: Initialization is triggered via onInstalled and onStartup to allow
// cleanup to run first and avoid duplicate observer tabs on install/update.

chrome.action.onClicked.addListener((tab) => {
  // Use the tabId from the tab that was clicked to open the side panel
  chrome.sidePanel.open({ tabId: tab.id });
});

// Close monitor tabs on extension update/uninstall
chrome.runtime.onUpdateAvailable.addListener(() => {
  console.log("🔄 Extension update available, closing monitor tabs...");
  closeMonitorTabs();
  closeSmartAgentMonitorGroupTabs();
});

chrome.runtime.onSuspend.addListener(() => {
  console.log("🛑 Extension suspending, closing monitor tabs...");
  closeMonitorTabs();
  closeSmartAgentMonitorGroupTabs();
});

/**
 * Close both monitor tabs when extension is being updated or uninstalled
 */
function closeMonitorTabs() {
  try {
    if (engineController && engineController.executionService) {
      const executionService = engineController.executionService;
      // Close login monitor tab
      if (
        executionService.loginMonitor &&
        executionService.loginMonitor.tabId
      ) {
        try {
          chrome.tabs.remove(executionService.loginMonitor.tabId);
          console.log("🗑️ Closed login monitor tab");
        } catch (e) {
          console.warn("Failed to close login monitor tab:", e);
        }
      }
      // Close observer monitor tab
      if (
        executionService.observerMonitor &&
        executionService.observerMonitor.tabId
      ) {
        try {
          chrome.tabs.remove(executionService.observerMonitor.tabId);
          console.log("🗑️ Closed observer monitor tab");
        } catch (e) {
          console.warn("Failed to close observer monitor tab:", e);
        }
      }
    }
  } catch (error) {
    console.warn("Error closing monitor tabs:", error);
  }
}

/**
 * Handle document status updates from content scripts
 * @param {string} docId - Document ID to update
 * @param {string} newStatus - New status to set
 * @param {Object} metadata - Additional metadata
 * @param {Object} options - Update options
 * @returns {Promise<Object>} Result of the update operation
 */
async function handleDocumentStatusUpdate(
  docId,
  newStatus,
  metadata = {},
  options = {}
) {
  try {
    console.log(
      `[Background] Handling document status update: ${docId} -> ${newStatus}`
    );

    const {
      maxRetries = 3,
      retryDelay = 50,
      createIfMissing = true,
      preserveExistingFields = true,
      requiredFields = ["_id", "type", "created_at"],
    } = options;
    try {
      const norm = (newStatus || "").toString().trim().toLowerCase();
      if (norm === "unknown") {
        newStatus = "cancelled";
      }
    } catch (_) {}
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(
          `[Background] Updating document status: ${docId} -> ${newStatus} (attempt ${attempt}/${maxRetries})`
        );

        // Try to get existing document
        let doc;
        try {
          doc = await localDB.get(docId);
          console.log(`[Background] Retrieved existing document: ${docId}`, {
            hasRev: !!doc?._rev,
            currentStatus: doc?.status,
            type: doc?.type,
          });
        } catch (error) {
          if (error.status === 404 && createIfMissing) {
            // Document doesn't exist, create minimal doc
            doc = createMinimalCancelDoc(docId, false, null, null, null);
            console.log(`[Background] Created minimal document for: ${docId}`);
          } else if (error.status === 404) {
            throw new Error(`Document ${docId} not found`);
          } else {
            throw error;
          }
        }

        // Prepare updated document with monotonic guard for monitor_* docs
        let finalStatus = newStatus;
        try {
          const isMonitorDoc =
            typeof docId === "string" && docId.startsWith("monitor_");
          if (isMonitorDoc) {
            const rank = {
              inProgress: 0,
              stopped: 1,
              triggered: 2,
              order_executed: 3,
            };
            const prev = (doc?.status || "").toString();
            const next = (newStatus || "").toString();
            const pr = rank[prev] ?? -1;
            const nr = rank[next] ?? -1;
            if (nr < pr) finalStatus = prev; // do not downgrade monitors
            try {
              console.log("[monitor-debug] updateDocumentStatus", {
                docId,
                prevStatus: prev,
                requestedStatus: newStatus,
                finalStatus,
              });
            } catch (_) {}
          }
        } catch (_) {}

        const updatedDoc = {
          ...(preserveExistingFields ? doc : {}),
          status: finalStatus,
          updated_at: new Date().toISOString(),
          ...metadata,
        };

        // Ensure required fields are preserved
        requiredFields.forEach((field) => {
          if (doc && doc[field] !== undefined) {
            updatedDoc[field] = doc[field];
          }
        });

        // Update the document
        const result = await localDB.put(updatedDoc);
        console.log(`[Background] Successfully updated document: ${docId}`, {
          newStatus,
          rev: result.rev,
          attempt,
        });

        // If monitor document was updated to "stopped", trigger cascade cancellation of dependents
        try {
          const isMonitorDoc =
            typeof docId === "string" && docId.startsWith("monitor_");
          if (isMonitorDoc && finalStatus === "stopped") {
            // Extract graphId and actionId from the monitor document
            const graphId =
              updatedDoc.execution_request_id || updatedDoc.graph_id;
            const actionId = updatedDoc.action_id || updatedDoc.node_id;

            if (graphId && actionId) {
              console.log(
                "[Monitor Stop][POUCHDB_BULK_CANCEL] Monitor document updated to stopped, triggering cascade",
                { docId, graphId, actionId }
              );

              // Trigger cascade cancellation for all dependents of this monitor
              await triggerCascadeForParent(graphId, actionId);

              console.log(
                "[Monitor Stop][POUCHDB_BULK_CANCEL] Cascade triggered successfully for stopped monitor",
                { docId, graphId, actionId }
              );
            } else {
              console.warn(
                "[Monitor Stop][POUCHDB_BULK_CANCEL] Missing graphId/actionId on stopped monitor doc, skipping cascade",
                { docId, graphId, actionId }
              );
            }
          }
        } catch (cascadeErr) {
          console.warn(
            "[Monitor Stop][POUCHDB_BULK_CANCEL] Failed to trigger cascade for stopped monitor:",
            cascadeErr?.message || cascadeErr
          );
        }

        return {
          success: true,
          docId,
          newStatus,
          rev: result.rev,
          attempt,
          metadata: updatedDoc,
        };
      } catch (error) {
        if (error.status === 409 && attempt < maxRetries) {
          // Conflict detected, retry with exponential backoff
          const delay = retryDelay * Math.pow(2, attempt - 1);
          console.log(
            `[Background] Conflict detected for ${docId}, retrying in ${delay}ms (attempt ${
              attempt + 1
            }/${maxRetries})`
          );
          await new Promise((resolve) => setTimeout(resolve, delay));
          continue;
        }

        console.error(`[Background] Failed to update document: ${docId}`, {
          attempt,
          error: error.message || String(error),
          status: error.status,
        });

        return {
          success: false,
          docId,
          newStatus,
          error: error.message || String(error),
          attempt,
          status: error.status,
        };
      }
    }

    return {
      success: false,
      docId,
      newStatus,
      error: `Failed to update document after ${maxRetries} attempts`,
      attempt: maxRetries,
    };
  } catch (error) {
    console.error(`[Background] Error in handleDocumentStatusUpdate:`, error);
    return {
      success: false,
      docId,
      newStatus,
      error: error.message || String(error),
      attempt: 0,
    };
  }
}
