/* Simplified popup styles for Smart Agent Executor */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  line-height: 1.6;
}

.container {
  width: 650px;
  min-height: 600px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Header */
header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  text-align: center;
}

header h1 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
}

header p {
  font-size: 14px;
  opacity: 0.9;
}

/* Main content */
main {
  padding: 20px;
}

/* Sections */
section {
  margin-bottom: 24px;
}

section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Input section */
.input-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.input-group {
  margin-bottom: 16px;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
  font-size: 14px;
}

textarea {
  width: 100%;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  resize: vertical;
  transition: border-color 0.2s ease;
}

textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea::placeholder {
  color: #adb5bd;
}

/* Button group */
.button-group {
  display: flex;
  gap: 12px;
  align-items: center;
}

.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-1px);
}

/* Status section */
.status-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.status-message {
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 500;
}

.status-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-info {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

/* Execution log */
.execution-log {
  max-height: 300px; /* Increased height for more detailed logs */
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  margin-bottom: 8px; /* Increased spacing between entries */
  padding: 6px 0;
  border-left: 3px solid transparent;
  padding-left: 8px;
  white-space: pre-wrap; /* Preserve line breaks and spaces */
  word-wrap: break-word;
}

.log-entry:last-child {
  margin-bottom: 0;
}

/* Enhanced log entry styling for different types */
.log-entry.log-success {
  border-left-color: #28a745;
  background: rgba(40, 167, 69, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-left: -8px;
}

.log-entry.log-error {
  border-left-color: #dc3545;
  background: rgba(220, 53, 69, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-left: -8px;
}

.log-entry.log-info {
  border-left-color: #17a2b8;
  background: rgba(23, 162, 184, 0.05);
  padding: 8px;
  border-radius: 4px;
  margin-left: -8px;
}

/* Timestamp styling */
.log-time {
  color: #6c757d;
  font-weight: 500;
  font-size: 11px;
}

/* Action ID styling */
.log-entry strong {
  color: #495057;
  font-weight: 600;
}

/* Explanation styling */
.log-entry .explanation {
  color: #6c757d;
  font-style: italic;
  font-size: 11px;
  margin-top: 2px;
  padding-left: 12px;
}

/* Summary section styling */
.log-entry.summary {
  background: rgba(102, 126, 234, 0.1);
  border-left-color: #667eea;
  font-weight: 500;
  color: #495057;
}

/* Execution time styling */
.log-entry.execution-time {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
  color: #856404;
  font-weight: 500;
}

.log-success {
  color: #28a745;
}

.log-error {
  color: #dc3545;
}

.log-info {
  color: #17a2b8;
}

/* Help section */
.help-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.help-section summary {
  cursor: pointer;
  font-weight: 500;
  color: #495057;
  padding: 8px 0;
  user-select: none;
}

.help-section summary:hover {
  color: #667eea;
}

.help-content {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e9ecef;
}

.help-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 12px 0 8px 0;
}

.help-content h4:first-child {
  margin-top: 0;
}

.help-content pre {
  background: #2d3748;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 11px;
  line-height: 1.4;
  margin: 8px 0;
}

.help-content code {
  background: #f1f3f4;
  color: #d73a49;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 11px;
}

.help-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.help-content li {
  margin-bottom: 4px;
  font-size: 13px;
}

.help-content strong {
  color: #333;
}

/* Scrollbar styling */
.execution-log::-webkit-scrollbar {
  width: 6px;
}

.execution-log::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.execution-log::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.execution-log::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Responsive design */
@media (max-width: 670px) {
  .container {
    width: 100%;
    border-radius: 0;
  }

  .button-group {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s ease-in-out infinite;
}

/* Focus styles for accessibility */
.btn:focus,
textarea:focus,
summary:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}
