# Technology Stack

## Core Technologies

- **Chrome Extension Manifest V3**: Modern extension architecture with service workers
- **Vanilla JavaScript**: No external frameworks, pure JS for all functionality
- **Chrome Extension APIs**: Extensive use of tabs, scripting, offscreen, and runtime APIs
- **Content Scripts**: DOM manipulation and site-specific automation logic
- **JSON Configuration**: Centralized configuration management via `lib/shared-config.json`

## Key Libraries & APIs

- **Chrome APIs Used**:
  - `chrome.tabs` - Tab management and background automation
  - `chrome.scripting` - Content script injection
  - `chrome.runtime` - Message passing and resource loading
  - `chrome.offscreen` - Background document management (future use)

## Architecture Patterns

- **Message Passing**: Communication between popup, background, and content scripts
- **Service Worker**: Background script handles coordination and validation
- **Content Script Injection**: Dynamic script loading based on site detection
- **Configuration-Driven**: Site support and action definitions via JSON config

## Development Commands

Since this is a Chrome extension, there are no traditional build commands. Development workflow:

1. **Load Extension**: Chrome → Extensions → Developer Mode → Load Unpacked
2. **Reload Extension**: Click reload button in Chrome extensions page after changes
3. **Debug**: Use Chrome DevTools for popup, background script console for service worker
4. **Test**: Manual testing on supported trading platforms

## File Types

- `.js` files: All JavaScript (background, popup, content scripts)
- `.json` files: Configuration and manifest
- `.html/.css` files: UI components (popup, offscreen document)
- `.png` files: Extension icons
