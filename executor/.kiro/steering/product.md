# Product Overview

SmartFinAgent Executor is a Chrome extension that automates financial trading actions on supported platforms through JSON-based commands.

## Core Features

- **Dual Automation Modes**: Execute actions in current tab (with visual feedback) or background tab (hidden execution)
- **JSON-Driven Actions**: Users input structured JSON commands for BUY, SELL, and MONIT<PERSON><PERSON>OFIT operations
- **Platform Support**: Currently supports Kite by Zerodha trading platform
- **Security-First**: All operations run in user's browser context with no external data transmission

## Supported Actions

- `BUY`: Purchase securities with symbol, quantity, price, and product type
- `SELL`: Sell securities with same parameters as buy
- `MONITORPROFIT`: Monitor positions for target profit thresholds

## Target Users

Traders and financial professionals who need to automate repetitive trading actions while maintaining control and transparency over their operations.
