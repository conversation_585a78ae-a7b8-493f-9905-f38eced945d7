{"user": {"userId": "user-123", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "9987548963", "avatar": "S", "joinDate": "January 2024", "tradingExperience": "Intermediate", "preferredMarkets": ["Stocks", "Options", "Futures"]}, "brokers": [{"id": "zerodha", "name": "<PERSON><PERSON>", "logo": "/assets/zerodha-logo.png", "isConnected": true, "description": "Integrating with Zerodha's brokerage platform allows you to seamlessly manage your trading activities."}, {"id": "groww", "name": "Groww", "logo": "/assets/groww-logo.png", "isConnected": false, "description": "Integrating with Groww's brokerage platform allows you to seamlessly manage your trading activities."}, {"id": "upstox", "name": "Upstox", "logo": "/assets/upstox-logo.png", "isConnected": false, "description": "Integrating with Upstox's brokerage platform allows you to seamlessly manage your trading activities."}]}