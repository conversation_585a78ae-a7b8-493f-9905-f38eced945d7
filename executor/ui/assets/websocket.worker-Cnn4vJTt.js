var e=Object.defineProperty,t=(t,s,o)=>((t,s,o)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:o}):t[s]=o)(t,"symbol"!=typeof s?s+"":s,o);const s=void 0,o=void 0;new class{constructor(){t(this,"ws",null),t(this,"reconnectAttempts",0),t(this,"maxReconnectAttempts",5),t(this,"reconnectTimeout",1e3),t(this,"isReconnecting",!1),t(this,"userId",""),t(this,"isConnected",!1),t(this,"messageQueue",[]),t(this,"lastMessageTime",null),t(this,"lastNetworkStatus",null),t(this,"currentTab","chat"),this.lastNetworkStatus=navigator.onLine,self.onmessage=this.handleMessage.bind(this),console.log("🚀 [Worker] WebSocket Worker STARTED successfully!"),console.log("[Worker] Initialized with:",{wsUrl:s,wsEndpoint:o}),console.log("[Worker] Ready to receive messages from main thread")}handleMessage(e){var t;const{type:s,payload:o}=e.data;switch(console.log("[Worker] Received message:",{type:s,payload:o}),s){case"CONNECT":this.userId=o.userId,console.log("[Worker] Connecting with userId:",this.userId),this.connect();break;case"DISCONNECT":console.log("[Worker] Disconnecting"),this.disconnect();break;case"SEND_MESSAGE":console.log("[Worker] Sending message:",o),o.typeOfMessage&&(this.currentTab=o.typeOfMessage),this.isConnected?this.sendMessage(o):(console.log("[Worker] Not connected, queueing message"),this.messageQueue.push(o),this.connect());break;case"NETWORK_STATUS_CHANGE":if(console.log("[Worker] Network status changed:",o.isOnline),this.lastNetworkStatus===o.isOnline)return void console.log("[Worker] Network status unchanged, skipping");this.lastNetworkStatus=o.isOnline,!o.isOnline||this.isConnected||this.isReconnecting||(null==(t=this.ws)?void 0:t.readyState)===WebSocket.CONNECTING?o.isOnline||(console.log("[Worker] Network is offline"),this.isConnected=!1):(console.log("[Worker] Network is back online, attempting to reconnect"),this.connect())}}connect(){try{if(this.ws){if(console.log("[Worker] WebSocket already exists, checking state..."),this.ws.readyState===WebSocket.OPEN&&this.isConnected)return void console.log("[Worker] WebSocket already connected");if(this.ws.readyState===WebSocket.CONNECTING)return void console.log("[Worker] WebSocket is already connecting, skipping");this.ws.close(),this.ws=null}const e=`${s}undefined?user_id=${this.userId}`;console.log("[Worker] Connecting to:",e),this.ws=new WebSocket(e),this.ws.onopen=()=>{for(console.log("[Worker] WebSocket connected"),this.isConnected=!0,this.reconnectAttempts=0,this.reconnectTimeout=1e3,this.isReconnecting=!1,this.postMessage({type:"CONNECTION_STATUS",payload:{isConnected:!0}});this.messageQueue.length>0;){const e=this.messageQueue.shift();e&&this.sendMessage(e)}},this.ws.onmessage=e=>{try{const t=JSON.parse(e.data);console.log("[Worker] Received WebSocket message:",t),t.error||"error"===t.status?(console.log("[Worker] Backend error detected:",t),this.postMessage({type:"ERROR",payload:{message:t.error||"Backend error occurred",details:t.details||t,tab:this.currentTab}})):this.postMessage({type:"WEBSOCKET_MESSAGE",payload:{...t,typeOfMessage:this.currentTab}})}catch(t){console.error("[Worker] Failed to parse message:",t),this.postMessage({type:"ERROR",payload:{message:"Failed to parse message",tab:this.currentTab}})}},this.ws.onclose=e=>{console.log("[Worker] WebSocket closed:",e),this.isConnected=!1,this.postMessage({type:"CONNECTION_STATUS",payload:{isConnected:!1,code:e.code}}),e.wasClean||this.isReconnecting||this.attemptReconnect()},this.ws.onerror=e=>{var t,o;console.error("[Worker] WebSocket error:",e),this.isConnected||(null==(t=this.ws)?void 0:t.readyState)===WebSocket.OPEN||(this.isConnected=!1,this.postMessage({type:"ERROR",payload:{message:"WebSocket connection failed",url:`${s}undefined`,timestamp:Date.now(),connectionAttempt:this.reconnectAttempts+1,tab:this.currentTab,details:{readyState:null==(o=this.ws)?void 0:o.readyState,readyStateText:this.ws?this.ws.readyState===WebSocket.CONNECTING?"CONNECTING":this.ws.readyState===WebSocket.OPEN?"OPEN":this.ws.readyState===WebSocket.CLOSING?"CLOSING":this.ws.readyState===WebSocket.CLOSED?"CLOSED":"UNKNOWN":"UNKNOWN",isOnline:navigator.onLine,userId:this.userId||"",lastMessageTime:this.lastMessageTime||null}}}))}}catch(e){console.error("[Worker] Connection error:",e),this.isConnected=!1,this.postMessage({type:"ERROR",payload:{message:e instanceof Error?e.message:"Connection error occurred",timestamp:Date.now(),tab:this.currentTab}})}}attemptReconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return console.log("[Worker] Max reconnection attempts reached"),void this.postMessage({type:"CONNECTION_STATUS",payload:{isConnected:!1,message:"Max reconnection attempts reached"}});this.isReconnecting=!0,this.reconnectAttempts++,console.log(`[Worker] Attempting reconnect ${this.reconnectAttempts}/${this.maxReconnectAttempts}`),this.postMessage({type:"CONNECTION_STATUS",payload:{isConnected:!1,attempt:this.reconnectAttempts}}),setTimeout(()=>{this.connect(),this.reconnectTimeout*=2},this.reconnectTimeout)}sendMessage(e){if(!this.ws)return console.log("[Worker] No WebSocket instance"),this.messageQueue.push(e),void this.connect();if(!this.isConnected||this.ws.readyState!==WebSocket.OPEN)return console.log("[Worker] WebSocket not connected, readyState:",this.ws.readyState,"isConnected:",this.isConnected),console.log("[Worker] Queueing message"),this.messageQueue.push(e),this.ws.readyState===WebSocket.CONNECTING?void console.log("[Worker] WebSocket is connecting, message queued"):void(this.ws.readyState>=WebSocket.CLOSING&&(console.log("[Worker] WebSocket is closed/closing, attempting to reconnect..."),this.connect()));try{console.log("[Worker] Sending WebSocket message:",e),this.ws.send(JSON.stringify(e))}catch(t){console.error("[Worker] Send error:",t),this.messageQueue.push(e),this.postMessage({type:"ERROR",payload:{message:t instanceof Error?t.message:"Send error occurred",timestamp:Date.now(),tab:this.currentTab}})}}disconnect(){this.ws&&(console.log("[Worker] Closing WebSocket connection"),this.ws.close(1e3,"Normal closure"),this.ws=null),this.isConnected=!1,this.isReconnecting=!1,this.userId="",this.messageQueue=[]}postMessage(e){console.log("[Worker] Posting message to main thread:",e),self.postMessage(e)}};
