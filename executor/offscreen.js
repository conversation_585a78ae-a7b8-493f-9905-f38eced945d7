// Offscreen script for background processing
console.log('Smart Agent Offscreen script loaded');

// Handle messages from the background script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Offscreen received message:', message);

  if (message.type === 'PROCESS_DATA') {
    // Process data in the offscreen context
    const result = processData(message.data);
    sendResponse({ success: true, result });
  }

  return true; // Keep the message channel open for async response
});

function processData(data) {
  // Simple data processing function
  return {
    processed: true,
    timestamp: new Date().toISOString(),
    data: data
  };
}
