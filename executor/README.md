# Smart Agent Executor

A Chrome extension that provides AI-powered trading automation and order execution capabilities.

## Features

- **AI-Powered Trading**: Execute trades using natural language commands
- **Real-time Monitoring**: Track orders and positions across multiple brokers
- **Automated Execution**: Place orders automatically based on AI recommendations
- **Multi-Broker Support**: Currently supports Zerodha with extensible architecture
- **Intelligent Login Detection**: Event-driven broker authentication monitoring

## Broker Login Status Detection

The executor includes intelligent broker login status detection to ensure trading operations are only performed when the user is properly authenticated.

### Detection Mechanism

**Event-Driven Architecture (No Constant Polling):**
- **On-Demand Checks**: `BROKER_STATUS_CHECK` message handler performs one-shot status validation
- **Tab Navigation Monitoring**: `tabs.onUpdated` listener detects login/logout on Zerodha pages
- **Execution-Time Validation**: Login status verified before any broker-requiring operations
- **Persistent Cache**: Status stored in `chrome.storage.local` to survive service worker restarts

**Status Broadcasting:**
- `LOGIN_REQUIRED`: Broadcast when broker login is detected as required
- `LOGIN_RESOLVED`: Broadcast when broker login is no longer required
- Debounced updates to prevent excessive notifications

### Implementation Details

**Background Service Worker:**
```javascript
// One-shot status check with force option
case "BROKER_STATUS_CHECK": {
  const force = request?.force === true;
  // Perform detection and update cache
  // Broadcast status changes
}

// Tab update monitoring for Zerodha
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (tab?.url?.startsWith("https://kite.zerodha.com/")) {
    // Debounced status check and broadcast
  }
});
```

**Status Persistence:**
- Cache includes `required`, `brokerName`, and `updatedAt` fields
- Automatically loaded on service worker startup
- Updated on every status change or check

### User Experience

**Login Detection Scenarios:**
- ✅ Extension opens with broker logged out → Immediate status detection
- ✅ User logs out while extension active → Tab update triggers detection
- ✅ User logs in after logout → Status cleared via navigation or manual check
- ✅ Service worker restart → Status restored from persistent cache

**Trading Operations:**
- All broker-requiring operations validate login status first
- Login monitor starts only during execution windows
- No constant background monitoring when idle

## Installation

1. Clone the repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build`
4. Load the extension in Chrome from the `dist` directory

## Development

```bash
# Install dependencies
npm install

# Build for development
npm run dev

# Build for production
npm run build

# Watch for changes
npm run watch
```

## Configuration

The extension uses a shared configuration file (`lib/shared-config.json`) for:
- Timeout settings
- Execution service parameters
- Environment-specific configurations

## Architecture

- **Background Script**: Service worker handling message routing and broker status
- **Content Scripts**: Zerodha integration and DOM manipulation
- **Execution Service**: Order execution and monitoring logic
- **WebSocket Provider**: Real-time communication with backend services

## Multi-Broker Support

Currently supports Zerodha with architecture designed for easy extension to additional brokers:
- Broker-specific content scripts
- Configurable detection logic
- Modular execution services
